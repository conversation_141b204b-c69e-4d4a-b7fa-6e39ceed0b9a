package com.qbook.insight.handler.controller;

import com.qbook.insight.common.domain.R;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.entity.Member;
import com.qbook.insight.handler.service.MemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 会员相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "Member Operate", description = "会员操作")
@RestController
@RequestMapping("/member/user")
public class MemberController {

  @Resource private MemberService memberService;

  @ApiOperation(value = "获取当前用户的会员信息")
  @GetMapping("/info")
  public R info() {
    return R.ok(memberService.info());
  }

  @ApiOperation(value = "修改用户会员信息")
  @PutMapping("/{id}")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R update(@PathVariable long id, @RequestBody Member member) {
    return R.ok(memberService.update(id, member));
  }

  @ApiOperation(value = "获取用户会员信息详情")
  @GetMapping("/{id}")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R getById(@PathVariable long id) {
    return R.ok(memberService.getById(id));
  }

  @ApiOperation(value = "获取用户会员信息列表")
  @GetMapping("/list")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R list(@ApiParam("是否激活(0-否，1-是)") @RequestParam(required = false) Integer isActive) {
    return R.ok(memberService.list(isActive));
  }

  @ApiOperation(value = "获取用户会员信息列表(分页)")
  @GetMapping("/page")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R page(
      PageParam pageParam,
      @ApiParam("是否激活(0-否，1-是)") @RequestParam(required = false) Integer isActive) {
    return R.ok(memberService.page(pageParam, isActive));
  }
}
