package com.qbook.insight.collector.domian;

import java.util.Date;
import lombok.Data;

/**
 * 监听MQ任务返回数据实体类
 *
 * <AUTHOR>
 */
@Data
public class TaskReturnData {

  /** 任务创建时间 */
  private Date createTime;

  /** 任务ID */
  private Integer id;

  /** 登录税号 */
  private String loginTaxId;

  /** 公司名称 */
  private String name;

  /** 操作人ID */
  private String operateId;

  /** 所属月份 */
  private Integer ownerMonth;

  /** 所属系统 */
  private String ownerSystem;

  /** 所属年份 */
  private Integer ownerYear;

  /** 登录密码 */
  private String password;

  /** 省份 */
  private String province;

  /** 结果队列名称 */
  private String resultQueue;

  /** 任务内容 */
  private String taskContent;

  /** 任务完成时间 */
  private Date taskFinishTime;

  /** 任务唯一标识 */
  private String taskId;

  /** 任务状态 */
  private Integer taskState;

  /** 任务类型 */
  private Integer taskType;

  /** 税号 */
  private String taxId;

  /** 任务结果 */
  private String taskResult;
}
