package com.qbook.insight.common.vo;

import com.qbook.insight.common.entity.Report;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 报告VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportVO extends Report {

  @ApiModelProperty("用户名")
  private String username;

  @ApiModelProperty("用户昵称")
  private String realName;

  @ApiModelProperty("公司名称")
  private String corpName;

  @ApiModelProperty("税号")
  private String taxId;
}
