package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.DataSource;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 股东
 *
 * <AUTHOR>
 */
@Data
public class Shareholder {

  @ApiModelProperty("投资方")
  @DataSource("电子税务局:纳税人信息")
  private String name;

  @ApiModelProperty("投资比例(%)")
  @DataSource("电子税务局:纳税人信息")
  private Float percent;
}
