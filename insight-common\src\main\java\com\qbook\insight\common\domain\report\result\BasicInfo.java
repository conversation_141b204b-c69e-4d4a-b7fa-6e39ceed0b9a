package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.DataSource;
import com.qbook.insight.common.annotation.ResultLevel1;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 企业基本信息
 *
 * <AUTHOR>
 */
@Data
public class BasicInfo {

  @ApiModelProperty("企业名称")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private String corpName;

  @ApiModelProperty("社会信用代码")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private String taxId;

  @ApiModelProperty("注册地址")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private String registeredAddress;

  @ApiModelProperty("登记日期")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private String registrationDate;

  @ApiModelProperty("当前状态")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private Integer status;

  @ApiModelProperty("注册资本")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private Float registeredCapital;

  @ApiModelProperty("实收资本")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private Float paidInCapital;

  @ApiModelProperty("所属行业")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private String industry;

  @ApiModelProperty("经营范围")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private String businessScope;

  @ApiModelProperty("主管税务机关")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private String taxAuthority;

  @ApiModelProperty("法人信息")
  @ResultLevel1
  private LegalPerson legalPerson;

  @ApiModelProperty("登记注册类型")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private String registrationType;

  @ApiModelProperty("纳税信用等级")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private String taxCreditRating;

  @ApiModelProperty("是否出口企业")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private Boolean isExportEnterprise;

  @ApiModelProperty("是否高新技术企业")
  @ResultLevel1
  @DataSource("电子税务局:所得税年度申报表")
  private Boolean isHighTechEnterprise;

  @ApiModelProperty("是否小微企业")
  @ResultLevel1
  @DataSource("电子税务局:所得税年度申报表")
  private Boolean isSmallMicroEnterprise;

  @ApiModelProperty("纳税人类型 (适用会计制度?)")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private String taxpayerType;

  @ApiModelProperty("股东及关联方分析")
  @ResultLevel1
  private ShareholderAnalysis shareholderAnalysis;
}
