package com.qbook.insight.analyzer.service;

import com.qbook.insight.analyzer.stratetgy.AnalysisStrategyManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 任务分析服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TaskAnalysisService {

  private final AnalysisStrategyManager analyzeStrategyManager;

  // 使用自定义线程池，异步执行
  @Async("analysisTaskExecutor")
  // 最多重试3次，间隔2秒
  @Retryable(maxAttempts = 3, backoff = @Backoff(delay = 2000))
  public void executeAnalysisTask(String data, String strategyType) {
    try {
      analyzeStrategyManager.executeStrategy(data, strategyType);
    } catch (Exception e) {
      log.error("策略 [{}] 执行失败: {}", strategyType, e.getMessage(), e);
      // 触发重试
      throw new RuntimeException("分析执行失败");
    }
  }
}
