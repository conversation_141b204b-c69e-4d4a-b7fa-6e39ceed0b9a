package com.qbook.insight.handler.controller;

import com.qbook.insight.common.annotation.EventLog;
import com.qbook.insight.common.domain.R;
import com.qbook.insight.common.vo.ClientVO;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.service.ClientService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 客户相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "Client Operate", description = "客户操作")
@RestController
@RequestMapping("/client")
public class ClientController {

  @Resource private ClientService clientService;

  @ApiOperation(value = "添加客户")
  @PostMapping("/add")
  @EventLog(name = "添加客户")
  public R add(@RequestBody ClientVO clientVO) {
    return R.affect(clientService.add(clientVO));
  }

  @ApiOperation(value = "删除客户")
  @DeleteMapping("/{id}")
  @EventLog(name = "删除客户")
  public R delete(@PathVariable long id) {
    return R.affect(clientService.delete(id));
  }

  @ApiOperation(value = "修改客户")
  @PutMapping("/{id}")
  @EventLog(name = "修改客户")
  public R update(@PathVariable long id, @RequestBody ClientVO clientVO) {
    return R.affect(clientService.update(id, clientVO));
  }

  @ApiOperation(value = "获取客户列表(分页，包含报告列表)")
  @GetMapping("/page")
  public R page(
      PageParam pageParam,
      @ApiParam("公司名称") @RequestParam(required = false) String corpName,
      @ApiParam("税号") @RequestParam(required = false) String taxId,
      @ApiParam("标签") @RequestParam(required = false) String tags) {
    return R.ok(clientService.page(pageParam, corpName, taxId, tags));
  }
}
