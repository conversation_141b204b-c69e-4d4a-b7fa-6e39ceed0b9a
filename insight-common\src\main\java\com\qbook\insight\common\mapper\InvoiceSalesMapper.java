package com.qbook.insight.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qbook.insight.common.constant.DsName;
import com.qbook.insight.common.entity.InvoiceSales;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 销项Mapper
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-22 上午9:06
 */
@Mapper
@DS(DsName.DATA)
public interface InvoiceSalesMapper extends BaseMapper<InvoiceSales> {

  /** 批量查询已存在的zzfpdm+zzfphm */
  List<String> selectExistingInvoiceKeys(@Param("invoiceKeys") List<String> invoiceKeys);

  /** 批量插入销项数据 */
  int insertBatch(List<InvoiceSales> list);

  /** 获取指定税号发票gatherDatetime的最小值和最大值 */
  Map<String, LocalDateTime> getMinMaxKprqDateByTaxId(String taxId);
}
