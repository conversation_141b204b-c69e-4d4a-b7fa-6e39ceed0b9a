package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.ResultLevel1;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 报告结果
 *
 * <AUTHOR>
 */
@Data
public class Result {

  @ApiModelProperty("版本号")
  @ResultLevel1
  private String version;

  @ApiModelProperty("企业基本信息")
  @ResultLevel1
  private BasicInfo basicInfo;

  @ApiModelProperty("发票分析")
  private InvoiceAnalysis invoiceAnalysis;

  @ApiModelProperty("商品分析")
  private ProductAnalysis productAnalysis;

  @ApiModelProperty("税务分析")
  @ResultLevel1
  private TaxAnalysis taxAnalysis;

  @ApiModelProperty("财务分析")
  @ResultLevel1
  private FinancialAnalysis financialAnalysis;

  @ApiModelProperty("政策享受分析")
  private PolicyBenefits policyBenefits;

  @ApiModelProperty("筹划分析")
  private PlanningAnalysis planningAnalysis;
}
