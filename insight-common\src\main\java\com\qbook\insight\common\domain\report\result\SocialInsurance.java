package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.ResultLevel1;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 社会保险金
 *
 * <AUTHOR>
 */
@Data
public class SocialInsurance {

  @ApiModelProperty("序号")
  @ResultLevel1
  private Integer index;

  @ApiModelProperty("年份")
  @ResultLevel1
  private Integer year;

  @ApiModelProperty("未足额缴纳社会保险金(元)")
  @ResultLevel1
  private BigDecimal insufficientSocialInsurance;
}
