package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 收入调减事项
 *
 * <AUTHOR>
 */
@Data
public class IncomeDeductions {

  @ApiModelProperty("财政拨款、依法收取的行政事业性收费、政府性基金等不征税收入(元)")
  private BigDecimal nonTaxableGovIncome;

  @ApiModelProperty("国债利息免税收入(元)")
  private BigDecimal taxExemptBondInterest;

  @ApiModelProperty("从直接投资于其他居民企业分回的股息、红利等免税收入(元)")
  private BigDecimal taxExemptDividends;

  @ApiModelProperty("符合条件的资源综合利用减计收入(元)")
  private BigDecimal resourceUtilizationDeduction;

  @ApiModelProperty("以前期间销售产生的本期销售退回、折扣、折让(元)")
  private BigDecimal priorPeriodReturns;

  @ApiModelProperty("以前期间错误多计收入在本期调整(元)")
  private BigDecimal priorPeriodOverstatedIncome;
}
