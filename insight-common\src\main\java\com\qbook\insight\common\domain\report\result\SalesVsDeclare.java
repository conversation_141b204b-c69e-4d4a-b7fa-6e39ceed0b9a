package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.DataSource;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发票金额与申报表对比分析
 *
 * <AUTHOR>
 */
@Data
public class SalesVsDeclare {

  @ApiModelProperty("年度")
  private Integer year;

  @ApiModelProperty("申报营业收入(元)")
  @DataSource("申报表(所得税年报、季报)")
  private Float decl;

  @ApiModelProperty("销项不含税金额(元)")
  @DataSource("销项发票")
  private Float sale;

  @ApiModelProperty("申报减开票差额(元)")
  private Float diff;
}
