package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发票风险统计
 *
 * <AUTHOR>
 */
@Data
public class InvoiceRiskStats {

  @ApiModelProperty("企业名称")
  private String corpName;

  @ApiModelProperty("税号")
  private String taxId;

  @ApiModelProperty("总金额(元)")
  private String totalAmount;

  @ApiModelProperty("风险项(多个风险项之间用逗号分隔，包括：税务风险、是否失信被执行、法人是否限高、虚开、税案、注销)")
  private String risks;
}
