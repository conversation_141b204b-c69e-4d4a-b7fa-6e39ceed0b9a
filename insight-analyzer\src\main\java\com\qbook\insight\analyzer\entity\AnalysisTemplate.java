package com.qbook.insight.analyzer.entity;

import com.qbook.insight.common.entity.BaseEntity;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 模板配置实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class AnalysisTemplate extends BaseEntity {
  // 模板名称
  private String name;
  // 描述
  private String description;
  // 策略类型
  // 数据库存储格式：["InvoicePurchaseAnalysisStrategy", "TaxBurdenAnalysisStrategy"]
  private List<String> strategyTypes;
}
