package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 扣除类调整事项(调增项)
 *
 * <AUTHOR>
 */
@Data
public class ExpenseAdjustments {

  @ApiModelProperty("自产产品、财产用于交换、捐赠、偿债、赞助、集资、广告、样品、职工福利视同销售成本(元)")
  private BigDecimal deemedSalesCost;

  @ApiModelProperty("职工薪酬不得扣除金额(元)")
  private BigDecimal nonDeductibleSalaries;

  @ApiModelProperty("职工福利费超限额列支(元)")
  private BigDecimal excessWelfare;

  @ApiModelProperty("工会经费超限额列支(元)")
  private BigDecimal excessUnionFees;

  @ApiModelProperty("职工教育经费超限额列支(元)")
  private BigDecimal excessTrainingFees;

  @ApiModelProperty("业务招待费超限额列支(元)")
  private BigDecimal excessEntertainment;

  @ApiModelProperty("广告和业务宣传费超限额列支(元)")
  private BigDecimal excessAdvertising;

  @ApiModelProperty("公益性捐赠支出超限额列支(元)")
  private BigDecimal excessDonations;

  @ApiModelProperty("非公益性捐赠支出(元)")
  private BigDecimal nonCharitableDonations;

  @ApiModelProperty("向非金融企业和关联方借款利息支出不得扣除金额(元)")
  private BigDecimal nonDeductibleInterest;

  @ApiModelProperty("不允许列支的罚款、罚金、滞纳金(元)")
  private BigDecimal nonDeductiblePenalties;

  @ApiModelProperty("手续费和佣金超额不得扣除金额(元)")
  private BigDecimal excessCommission;

  @ApiModelProperty("与经营活动无关的赞助支出(元)")
  private BigDecimal nonBusinessSponsorship;

  @ApiModelProperty("未经核定的准备金支出(元)")
  private BigDecimal unapprovedReserves;

  @ApiModelProperty("安置残疾人员工资加计扣除金额(元)")
  private BigDecimal disabledStaffDeduction;

  @ApiModelProperty("环保、节能节水、安全生产专用设备投资额抵免应纳税所得额(元)")
  private BigDecimal ecoEquipmentDeduction;

  @ApiModelProperty("创业投资企业投资额70%抵免应纳税所得额(元)")
  private BigDecimal ventureInvestmentDeduction;

  @ApiModelProperty("不征税收入用于支出形成的费用(元)")
  private BigDecimal nonTaxableExpenses;

  @ApiModelProperty("与取得收入无关的支出(元)")
  private BigDecimal irrelevantExpenses;

  @ApiModelProperty("暂估成本尚未取得发票(元)")
  private BigDecimal uninvoicedCosts;

  @ApiModelProperty("未按会计核算制度，多结转成本(元)")
  private BigDecimal overstatedCosts;

  @ApiModelProperty("白条支出的成本费用(元)")
  private BigDecimal unrecordedCosts;

  @ApiModelProperty("库存商品负数(元)")
  private BigDecimal negativeInventory;

  @ApiModelProperty("改变库存商品计价方式多结转(元)")
  private BigDecimal inventoryValuationAdjustment;

  @ApiModelProperty("原材料负数(元)")
  private BigDecimal negativeMaterials;

  @ApiModelProperty("生产成本(元)，此项并不必然导致多结转成本")
  private BigDecimal productionCosts;

  @ApiModelProperty("未按会计核算制度，结转和销项不匹配的成本(元)")
  private BigDecimal mismatchedInventoryCosts;

  @ApiModelProperty("应由个人负担的社会保险费(元)")
  private BigDecimal personalSocialInsurance;

  @ApiModelProperty("利用大数据，分析行业主要材料对比表")
  private List<Object> industryMaterialAnalysis;

  @ApiModelProperty("结论")
  private String conclusion;
}
