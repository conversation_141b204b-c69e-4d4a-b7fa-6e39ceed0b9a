<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>支付页面</title>
</head>
<body>

<h1>微信支付页面</h1>
<p th:text="${message}">Default message</p>
<script th:inline="javascript">
    /*<![CDATA[*/
    // 确保所有参数都已正确获取
    console.log("支付参数:", {
        appId: /*[[${appId}]]*/ '未知appId',
        timeStamp: /*[[${timeStamp}]]*/ '未知timeStamp',
        nonceStr: /*[[${nonceStr}]]*/ '未知nonceStr',
        package: /*[[${package}]]*/ '未知package',
        signType: /*[[${signType}]]*/ '未知signType',
        paySign: /*[[${paySign}]]*/ '未知paySign',
        orderId: /*[[${orderId}]]*/ '未知orderId'
    });
    function onBridgeReady() {
        WeixinJSBridge.invoke('getBrandWCPayRequest', {
                "appId": /*[[${appId}]]*/ '',
                "timeStamp": /*[[${timeStamp}]]*/ '',
                "nonceStr": /*[[${nonceStr}]]*/ '',
                "package": /*[[${package}]]*/ '',
                "signType": /*[[${signType}]]*/ '',
                "paySign": /*[[${paySign}]]*/ ''
            },
            function(res) {
                if (res.err_msg == "get_brand_wcpay_request:ok") {
                    // 支付成功后的逻辑
                    console.log("支付成功");
                } else if (res.err_msg == "get_brand_wcpay_request:cancel") {
                    // 用户取消支付
                    console.log("您已取消支付");
                    // window.location.href = "/pay/cancel?orderId=" + /*[[${orderId}]]*/ '';
                } else {
                    // 支付失败
                    console.log("支付失败，请重试");
                    // window.location.href = "/pay/fail?orderId=" + /*[[${orderId}]]*/ '';
                }
            });
    }

    if (typeof WeixinJSBridge == "undefined") {
        if (document.addEventListener) {
            document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);
        } else if (document.attachEvent) {
            document.attachEvent('WeixinJSBridgeReady', onBridgeReady);
            document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);
        }
        console.log("WeixinJSBridge未定义，等待加载...")
    } else {
        console.log("WeixinJSBridge已定义，准备调起支付...")
        onBridgeReady();
    }
    /*]]>*/
</script>

</body>
</html>