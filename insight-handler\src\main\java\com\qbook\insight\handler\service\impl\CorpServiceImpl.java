package com.qbook.insight.handler.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qbook.insight.common.constant.RMsg;
import com.qbook.insight.common.entity.Corp;
import com.qbook.insight.common.entity.CorpDetail;
import com.qbook.insight.common.entity.CorpPrompt;
import com.qbook.insight.common.enums.BusinessStateEnum;
import com.qbook.insight.common.exception.BizException;
import com.qbook.insight.common.mapper.CorpDetailMapper;
import com.qbook.insight.common.mapper.CorpMapper;
import com.qbook.insight.common.mapper.CorpPromptMapper;
import com.qbook.insight.common.util.HttpClient;
import com.qbook.insight.common.util.StringUtils;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.config.XlbProperties;
import com.qbook.insight.handler.service.CorpService;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 公司操作接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CorpServiceImpl implements CorpService {

  @Resource private CorpMapper corpMapper;
  @Resource private CorpPromptMapper corpPromptMapper;
  @Resource private CorpDetailMapper corpDetailMapper;
  @Resource private XlbProperties xlbProperties;

  @Override
  public int add(Corp corp) {
    return corpMapper.insert(corp);
  }

  @Override
  public int delete(long id) {
    return corpMapper.deleteById(id);
  }

  @Override
  public int update(long id, Corp corp) {
    corp.setId(id);
    return corpMapper.updateById(corp);
  }

  @Override
  public List<CorpPrompt> list(String corpName, String taxId) {
    LambdaQueryWrapper<CorpPrompt> wrapper = new LambdaQueryWrapper<>();
    wrapper.like(StringUtils.isNotBlank(corpName), CorpPrompt::getName, corpName);
    wrapper.eq(StringUtils.isNotBlank(taxId), CorpPrompt::getTaxId, taxId);
    return corpPromptMapper.selectList(wrapper);
  }

  @Override
  public IPage<Corp> page(PageParam pageParam) {
    IPage<Corp> page = new Page<>(pageParam.getPage(), pageParam.getPageSize());
    return corpMapper.selectPage(page, null);
  }

  /** 获取公司基本信息 */
  @Override
  public Corp getCorpInfo(String taxId) {
    Corp corp =
        corpMapper.selectOne(
            new LambdaQueryWrapper<Corp>()
                .eq(StringUtils.isNotBlank(taxId), Corp::getTaxId, taxId));
    log.info("查询到数据库中的公司信息为:{}", corp);
    if (Objects.isNull(corp)) {
      // 根据taxId获取小蓝本提供的企业基本信息
      corp = getXlbCorpInfo(taxId);
      log.info("获取到的公司信息为:{}", corp);
    }
    return corp;
  }

  /** 根据税号获取小蓝本API提供的企业基本信息 */
  public Corp getXlbCorpInfo(String taxId) {
    // 设置小蓝本请求头
    HashMap<String, String> header = new HashMap<>();
    header.put("accessId", xlbProperties.getAccessId());
    header.put("accessToken", xlbProperties.getAccessToken());
    try {
      // 社会信用代码查询公司4070
      String eidUrl = xlbProperties.getCorpCreditCode().concat("?uscc=" + taxId);
      HttpClient.Response eidRes = HttpClient.get(eidUrl, header, 5000);
      String eid =
          JSONUtil.parse(eidRes.getData()).getByPath("search.queryCompanyUscc", String.class);
      log.info("查询到的eid为:{}", eid);

      // 企业基本信息-eid入参1001
      String corpUrl = xlbProperties.getCorpBaseInfo().concat("?eid=" + eid);
      HttpClient.Response corpInfoRes = HttpClient.get(corpUrl, header, 5000);
      log.info("企业基本信息-eid入参返回:{}", corpInfoRes.getData());
      JSON data = JSONUtil.parse(corpInfoRes.getData());
      String baseInfo = data.getByPath("entInfo.base", String.class);
      if (baseInfo == null || baseInfo.isEmpty()) {
        log.warn("小蓝本API返回的公司基本数据为空");
        return null;
      }
      Corp corp = populateCorpFromXlb(data);
      corpMapper.insert(corp);
      CorpDetail corpDetail = populateCorpDetailFromXlb(corp.getId(), data);
      corpDetailMapper.insert(corpDetail);
      return corp;
    } catch (Exception e) {
      throw new BizException(RMsg.ERR_GET_API_DATA);
    }
  }

  /** 从小蓝本API响应中填充 Corp 对象 */
  private Corp populateCorpFromXlb(JSON data) {
    Corp corp = new Corp();
    corp.setTaxId(safeGetString(data, "entInfo.base.creditCode", "未知税号"));
    corp.setName(safeGetString(data, "entInfo.base.entname", null));
    corp.setAddress(safeGetString(data, "entInfo.base.addr", null));
    corp.setSector(safeGetString(data, "entInfo.base.industryphyName", null));
    corp.setRegisterCapital(Convert.toInt(safeGetString(data, "entInfo.base.regcap", null), 0));
    corp.setPaidInCapital(Convert.toInt(safeGetString(data, "entInfo.base.acconam", null), 0));
    corp.setEastabDate(safeGetDate(data, "entInfo.base.esdate"));
    corp.setState(
        BusinessStateEnum.getCodeByDesc(safeGetString(data, "entInfo.base.entStatusNameBi", null)));
    log.info("填充的corp对象为:{}", corp);
    return corp;
  }

  /** 从小蓝本API响应中填充 CorpDetail 对象 */
  private CorpDetail populateCorpDetailFromXlb(Long corpId, JSON data) {
    CorpDetail corpDetail = new CorpDetail();
    corpDetail.setCorpId(corpId);
    corpDetail.setDom(safeGetString(data, "entInfo.base.dom", null));
    corpDetail.setFrname(safeGetString(data, "entInfo.base.frname", null));
    corpDetail.setShortName(safeGetString(data, "entInfo.base.shortName", null));
    corpDetail.setEntTypeName(safeGetString(data, "entInfo.base.enttypeName", null));
    corpDetail.setCapTypeName(safeGetString(data, "entInfo.base.capTypeName", null));
    corpDetail.setOpScope(safeGetString(data, "entInfo.base.opscope", null));
    corpDetail.setDescription(safeGetString(data, "entInfo.base.description", null));
    corpDetail.setRegno(safeGetString(data, "entInfo.base.regno", null));
    corpDetail.setRegCapCurName(safeGetString(data, "entInfo.base.regCapCurName", null));
    corpDetail.setRegOrgName(safeGetString(data, "entInfo.base.regorgName", null));
    corpDetail.setRegOrgProvince(safeGetString(data, "entInfo.base.regorgProvince", null));
    corpDetail.setRegOrgCity(safeGetString(data, "entInfo.base.regorgCity", null));
    corpDetail.setRegOrgCounty(safeGetString(data, "entInfo.base.regorgCounty", null));
    corpDetail.setOrgCode(safeGetString(data, "entInfo.base.orgCode", null));
    corpDetail.setRevcanRea(safeGetString(data, "entInfo.base.revcanRea", null));

    // 安全解析日期字段
    corpDetail.setApprdate(safeGetDate(data, "entInfo.base.apprdate"));
    corpDetail.setRevdate(safeGetDate(data, "entInfo.base.revdate"));
    corpDetail.setCandate(safeGetDate(data, "entInfo.base.candate"));
    corpDetail.setOpFrom(safeGetDate(data, "entInfo.base.opfrom"));
    corpDetail.setOpTo(safeGetDate(data, "entInfo.base.opto"));

    corpDetail.setInvCnt(Convert.toLong(safeGetString(data, "entInfo.base.invCnt", null), 0L));
    corpDetail.setPriCnt(Convert.toLong(safeGetString(data, "entInfo.base.priCnt", null), 0L));
    corpDetail.setSsNum(Convert.toLong(safeGetString(data, "entInfo.base.ssNum", null), 0L));
    corpDetail.setStaffNum(Convert.toLong(safeGetString(data, "entInfo.base.staffNum", null), 0L));
    log.info("填充的corpDetail对象为:{}", corpDetail);
    return corpDetail;
  }

  /** 安全获取字符串字段（带默认值） */
  private String safeGetString(JSON data, String path, String defaultValue) {
    String value = safeGet(data, path, String.class);
    return StringUtils.isBlank(value) ? defaultValue : value;
  }

  /** 安全获取日期字段（带格式化） */
  private Date safeGetDate(JSON data, String path) {
    String dateStr = safeGet(data, path, String.class);
    return parseDateEndOfDay(dateStr);
  }

  /** 安全获取 JSON 中的字段值，失败返回 null，并打印警告日志 */
  private <T> T safeGet(JSON data, String path, Class<T> clazz) {
    try {
      return data.getByPath(path, clazz);
    } catch (Exception e) {
      log.warn("JSON字段解析失败: {}, 路径: {}", clazz.getSimpleName(), path, e);
      return null;
    }
  }

  /** 解析日期字段. yyyy-MM-dd */
  private Date parseDateEndOfDay(String dateStr) {
    if (StringUtils.isBlank(dateStr)) {
      return null;
    }
    try {
      return DateUtil.endOfDay(DateUtil.parse(dateStr, "yyyy-MM-dd"));
    } catch (Exception e) {
      log.warn("日期解析失败: {}", dateStr, e);
      return null;
    }
  }
}
