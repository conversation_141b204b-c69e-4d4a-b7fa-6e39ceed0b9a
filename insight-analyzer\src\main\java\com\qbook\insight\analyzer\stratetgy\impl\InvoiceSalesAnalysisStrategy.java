package com.qbook.insight.analyzer.stratetgy.impl;

import com.qbook.insight.analyzer.domain.AnalysisData;
import com.qbook.insight.analyzer.stratetgy.AnalysisStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 销项分析策略
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-04 17:20
 */
@Component("InvoiceSalesAnalysisStrategy")
public class InvoiceSalesAnalysisStrategy implements AnalysisStrategy {
  private static final Logger log = LoggerFactory.getLogger(InvoiceSalesAnalysisStrategy.class);

  /**
   * 分析策略
   *
   * @param data 数据
   */
  @Override
  public void analyze(String data) {
    log.warn("开始执行策略：销项");

    log.warn("完成执行策略：销项");
  }

  /**
   * 获取分析策略类型
   *
   * @return 分析策略类型
   */
  @Override
  public String getStrategyType() {
    return "InvoiceSalesAnalysisStrategy";
  }

  /**
   * 保存分析结果到数据库
   *
   * @param analyzeData 数据
   */
  @Override
  public void saveResult(AnalysisData analyzeData) {}

  /**
   * 加载策略所需数据
   *
   * @param taskId 任务ID
   * @return 数据对象
   */
  @Override
  public AnalysisData loadData(String taskId) {
    return null;
  }
}
