package com.qbook.insight.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public final class BizException extends RuntimeException {

  // 错误码
  private int code;

  // 错误信息
  private String message;

  public BizException() {
    this.code = 400;
    this.message = "业务异常";
  }

  public BizException(int code, String message) {
    this.code = code;
    this.message = message;
  }

  public BizException(String message) {
    this.code = 400;
    this.message = message;
  }
}
