<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.qbook.insight.common.mapper.InvoicePurchaseMapper">


  <!-- 批量查询已存在的发票键值(zzfpdm+zzfphm) -->
  <select id="selectExistingInvoiceKeys"
    resultType="java.lang.String">
    SELECT CONCAT(zzfpdm, zzfphm) as invoiceKey
    FROM invoice_purchase
    WHERE CONCAT(zzfpdm, zzfphm) IN
    <foreach collection="invoiceKeys" item="item" open="("
      separator="," close=")">
      #{item}
    </foreach>
  </select>

  <insert id="insertBatch">
    INSERT IGNORE INTO
    invoice_purchase (
    task_id, tax_id, zzfpdm, zzfphm, fpkjfxlxdm,
    gmfmc, gmfnsrsbh, kprq, hjje, hjse,
    jshj, fplydm, fppzdm, fpztdm, sflzfp,
    kpr, bz, kprqn, kprqy, gather_datetime,
    deduction, checkout, checkout_datetime, confirm_task_id,
    ofd_file, expand_content, create_time
    )
    VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.taskId}, #{item.taxId}, #{item.zzfpdm}, #{item.zzfphm}, #{item.fpkjfxlxdm},
      #{item.gmfmc}, #{item.gmfnsrsbh}, #{item.kprq}, #{item.hjje},
      #{item.hjse},
      #{item.jshj}, #{item.fplydm}, #{item.fppzdm}, #{item.fpztdm}, #{item.sflzfp},
      #{item.kpr}, #{item.bz}, #{item.kprqn}, #{item.kprqy},
      #{item.gatherDatetime},
      #{item.deduction}, #{item.checkout}, #{item.checkoutDatetime}, #{item.confirmTaskId},
      #{item.ofdFile}, #{item.expandContent}, #{item.createTime}
      )
    </foreach>
  </insert>

  <select id="getKprqsByTaxIdAndDateRange"
    resultType="java.time.LocalDateTime">
    SELECT
    kprq
    FROM
    invoice_purchase
    WHERE
    tax_id = #{taxId}
    AND
    kprq BETWEEN #{startDate} AND #{endDate}
    ORDER BY
    kprq
  </select>

  <select id="existsByTaxIdAndDateRange" resultType="boolean">
    SELECT EXISTS(
    SELECT 1
    FROM invoice_purchase
    WHERE tax_id = #{taxId}
    AND kprq BETWEEN #{startDate} AND #{endDate}
    )
  </select>

  <select id="getMinMaxKprqDateByTaxId" resultType="map">
    SELECT
    MIN(kprq) AS minDate,
    MAX(kprq) AS maxDate
    FROM
    invoice_purchase
    WHERE
    tax_id = #{taxId}
  </select>
</mapper>
