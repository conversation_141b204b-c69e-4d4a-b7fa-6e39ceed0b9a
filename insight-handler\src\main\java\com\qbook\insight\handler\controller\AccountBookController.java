package com.qbook.insight.handler.controller;

import com.qbook.insight.common.domain.R;
import com.qbook.insight.common.entity.account.AccountBook;
import com.qbook.insight.handler.service.AccountBookService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 账套数据相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "AccountBook", description = "账套数据")
@RestController
@RequestMapping("/open/account_book/v1")
public class AccountBookController {

  @Resource private AccountBookService accountBookService;

  @ApiOperation(value = "上传账套数据")
  @PostMapping("/upload")
  public R add(
      @ApiParam("客户端身份标识") @RequestParam String appkey,
      @ApiParam("时间戳") @RequestParam Long timestamp,
      @ApiParam("随机字符串") @RequestParam String nonce,
      @ApiParam("签名值") @RequestParam String sign,
      @ApiParam("数据来源") @RequestParam String datasrc,
      @ApiParam("税号") @RequestParam String taxid,
      @ApiParam("年度") @RequestParam String year,
      @RequestBody AccountBook book) {
    accountBookService.upload(datasrc, taxid, year, book);
    return R.ok();
  }
}
