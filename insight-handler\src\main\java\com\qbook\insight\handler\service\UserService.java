package com.qbook.insight.handler.service;

import com.qbook.insight.common.entity.User;
import com.qbook.insight.common.vo.UserInfoVO;
import com.qbook.insight.handler.vo.LoginParam;
import javax.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;

/** <AUTHOR> */
@Service
public interface UserService {

  /** 登录 */
  User login(LoginParam param);

  /** 获取当前用户信息 */
  User getUserInfo();

  /** 获取当前用户详细信息 */
  UserInfoVO getUserDetailInfo();

  /** 获取当前用户角色列表 */
  String[] getRoles();

  /** 退出登录 */
  void logout(HttpServletRequest request);

  /** 添加用户 */
  int add(User user);

  /** 获取当前用户ID */
  Long getUserId();

  /** 判断当前用户是否具有指定角色 */
  boolean hasAnyRoles(String... roles);
}
