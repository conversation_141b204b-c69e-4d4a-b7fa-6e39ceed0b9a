<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qbook.insight.common.mapper.ClientMapper">

  <select id="selectClientVO">
    select c.*, p.name as corp_name, p.tax_id
    from client c
    left join corp p on c.corp_id = p.id
    where c.user_id = #{cond.userId}
    <if test="cond.corpName != null and cond.corpName != ''">
      and p.name like concat('%', #{cond.corpName}, '%')
    </if>
    <if test="cond.taxId != null and cond.taxId != ''">
      and p.tax_id = #{cond.taxId}
    </if>
    <if test="cond.tags != null and cond.tags != ''">
      and c.tags like concat('%', #{cond.tags}, '%')
    </if>
  </select>
</mapper>
