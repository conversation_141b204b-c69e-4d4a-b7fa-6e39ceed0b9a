package com.qbook.insight.analyzer.service;

import com.qbook.insight.common.util.RedisUtil;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-04 15:35
 */
@SpringBootTest
@Slf4j
class TaskListenerTest {

  @Resource private TaskListener taskListener;
  @Resource private RedisUtil redisUtil;

  @BeforeEach
  void setUp() {}

  @AfterEach
  void tearDown() {}

  @Test
  void redisTest() {
    // log.warn("第一个消息发送开始");
    redisUtil.rpush(
        "task_analyzer",
        "{\"template\":\"Invoice\",\"data\":\"{\\\"companyId\\\":\\\"123456\\\",\\\"period\\\":\\\"2023-06\\\"}\"}");
    // log.warn("第一个消息发送结束");

    // log.warn("第2个消息发送开始");
    redisUtil.rpush(
        "task_analyzer",
        "{\"template\":\"Tax\",\"data\":\"{\\\"companyId\\\":\\\"456789\\\",\\\"period\\\":\\\"2023-06\\\"}\"}");
    // log.warn("第2个消息发送结束");
    try {
      // 模拟耗时操作
      Thread.sleep(10000);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }
    log.warn("倒计时结束，全部结束");
  }
}
