package com.qbook.insight.common.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 指标分类实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorCategory extends BaseEntity {

  @ApiModelProperty("分类名称")
  private String name;

  @ApiModelProperty("级别（1,2,...）")
  private Integer level;

  @ApiModelProperty("描述")
  private String description;

  @ApiModelProperty("状态(1-启用,0-禁用)")
  private Integer status;
}
