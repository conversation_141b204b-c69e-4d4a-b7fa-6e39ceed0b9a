package com.qbook.insight.handler.config;

import com.qbook.insight.common.config.AuthTokenFilter;
import java.util.List;
import javax.annotation.Resource;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.core.GrantedAuthorityDefaults;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.web.filter.CorsFilter;

/**
 * spring security配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableMethodSecurity(securedEnabled = true)
@ConfigurationProperties(prefix = "security")
@Data
public class SecurityConfig {

  @Resource private AuthTokenFilter authTokenFilter;
  @Resource private CorsFilter corsFilter;
  @Resource private ApiDocConfig apiDocConfig;

  // 允许匿名访问的接口列表
  private List<String> permitAllUris;

  @Bean
  public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
    http.csrf(AbstractHttpConfigurer::disable);
    http.authorizeHttpRequests(
        (req) -> {
          permitAllUris.forEach(uri -> req.antMatchers(uri).permitAll());
          if (apiDocConfig.getPermitAllUris() != null) {
            apiDocConfig.getPermitAllUris().forEach(uri -> req.antMatchers(uri).permitAll());
          }
          req.anyRequest().authenticated();
        });
    http.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);
    http.addFilterBefore(authTokenFilter, UsernamePasswordAuthenticationFilter.class);
    http.addFilterAfter(corsFilter, AuthTokenFilter.class);
    http.addFilterAfter(corsFilter, LogoutFilter.class);

    return http.build();
  }

  /** 密码加密方式 */
  @Bean
  public PasswordEncoder encoder() {
    return new BCryptPasswordEncoder();
  }

  /** 设置角色前缀 */
  @Bean
  public GrantedAuthorityDefaults grantedAuthorityDefaults() {
    return new GrantedAuthorityDefaults("");
  }
}
