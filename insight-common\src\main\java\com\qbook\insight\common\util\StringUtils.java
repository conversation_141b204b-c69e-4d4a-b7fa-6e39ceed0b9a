package com.qbook.insight.common.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils {

  /** 下划线 */
  private static final char SEPARATOR = '_';

  /** 驼峰式命名法 例如：user_name->userName */
  public static String toCamelCase(String s) {
    if (s == null || s.isEmpty()) {
      return s;
    }
    if (s.indexOf(SEPARATOR) == -1) {
      char[] chars = s.toCharArray();
      chars[0] = Character.toLowerCase(chars[0]);
      return new String(chars);
    }

    s = s.toLowerCase();
    StringBuilder sb = new StringBuilder(s.length());
    boolean upperCase = false;
    for (int i = 0; i < s.length(); i++) {
      char c = s.charAt(i);

      if (c == SEPARATOR) {
        upperCase = true;
      } else if (upperCase) {
        sb.append(Character.toUpperCase(c));
        upperCase = false;
      } else {
        sb.append(c);
      }
    }
    return sb.toString();
  }

  public static String getHash(String algorithm, byte[] bytes) throws Exception {
    MessageDigest md = MessageDigest.getInstance(algorithm);
    byte[] digest = md.digest(bytes);
    StringBuilder hexString = new StringBuilder();
    for (byte b : digest) {
      String hex = Integer.toHexString(0xff & b);
      if (hex.length() == 1) {
        hexString.append('0');
      }
      hexString.append(hex);
    }
    return hexString.toString();
  }

  public static String getMd5(String str) throws Exception {
    return getHash("MD5", str.getBytes());
  }

  public static String getSha1(String str) throws Exception {
    return getHash("SHA-1", str.getBytes());
  }

  public static boolean hasLength(String str) {
    return str != null && !str.isEmpty();
  }

  public static boolean contains(String str, String... cs) {
    for (String c : cs) {
      if (str.contains(c)) {
        return true;
      }
    }
    return false;
  }

  public static String signParams(Map<String, String> params, String secret) throws Exception {
    // 第一步：参数排序
    String[] keys = params.keySet().toArray(new String[0]);
    Arrays.sort(keys);

    // 第二步：把所有参数名和参数值串在一起
    StringBuilder query = new StringBuilder();
    for (String key : keys) {
      String value = params.get(key);
      if (key != null && value != null) {
        query.append(key).append(value);
      }
    }

    // 第三步：使用HMAC进行编码
    byte[] bytes = encodeHMAC(query.toString(), secret);

    // 第四步：把二进制转化为大写的十六进制
    return byte2hex(bytes);
  }

  public static byte[] encodeHMAC(String data, String secret) throws Exception {
    SecretKey secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacMD5");
    Mac mac = Mac.getInstance(secretKey.getAlgorithm());
    mac.init(secretKey);
    return mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
  }

  public static String byte2hex(byte[] bytes) {
    StringBuilder sign = new StringBuilder();
    for (byte b : bytes) {
      String hex = Integer.toHexString(b & 0xFF);
      if (hex.length() == 1) {
        sign.append("0");
      }
      sign.append(hex.toUpperCase());
    }
    return sign.toString();
  }

  public static void main(String[] args) throws Exception {
    Map<String, String> params = new HashMap<>();
    params.put("appkey", "12345678");
    params.put("timestamp", "1753101937");
    params.put("nonce", "342677");
    String s = signParams(params, "abcdefghijklmnopqrst");
    System.out.println(s);
  }
}
