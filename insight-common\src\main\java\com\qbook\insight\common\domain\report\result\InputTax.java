package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 进项税额方面
 *
 * <AUTHOR>
 */
@Data
public class InputTax {

  @ApiModelProperty("取得不合规发票抵扣进项税额(元)")
  private BigDecimal nonCompliantInvoices;

  @ApiModelProperty("取得不合规发票抵扣进项税清单")
  private NonCompliantInvoiceList nonCompliantInvoiceList;

  @ApiModelProperty("用于不得抵扣项目未转出进项税额(元)")
  private BigDecimal nonDeductibleInputs;

  @ApiModelProperty("用于不得抵扣项目未转出进项税清单")
  private List<Object> nonDeductibleList;

  @ApiModelProperty("非正常损失未作进项税额转出(元)")
  private BigDecimal abnormalLoss;

  @ApiModelProperty("抵扣了特殊项目（4项）进项税额(元)")
  private BigDecimal specialItemDeductions;
}
