package com.qbook.insight.common.entity.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/** 会计科目 */
@Data
public class AccountingSubject {

  @ApiModelProperty("科目编号")
  private String subjectKey;

  @ApiModelProperty("科目名称")
  private String subjectName;

  @ApiModelProperty("科目级次")
  private Integer subjectLevel;

  @ApiModelProperty("辅助核算标志")
  private Integer auxiliaryAccountingFlag;

  @ApiModelProperty("辅助核算项")
  private String auxiliaryAccountingItem;

  @ApiModelProperty("科目类型")
  private String subjectType;

  @ApiModelProperty("计量单位")
  private String unitOfMeasure;

  @ApiModelProperty("余额方向")
  private String balanceDirection;
}
