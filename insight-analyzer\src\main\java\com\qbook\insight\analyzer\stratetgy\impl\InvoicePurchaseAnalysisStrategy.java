package com.qbook.insight.analyzer.stratetgy.impl;

import com.qbook.insight.analyzer.domain.AnalysisData;
import com.qbook.insight.analyzer.stratetgy.AnalysisStrategy;
import com.qbook.insight.common.entity.InvoicePurchase;
import com.qbook.insight.common.mapper.InvoicePurchaseMapper;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 进项分析策略
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-04 15:10
 */
@Component("InvoicePurchaseAnalysisStrategy")
@Slf4j
public class InvoicePurchaseAnalysisStrategy implements AnalysisStrategy {

  private final InvoicePurchaseMapper invoicePurchaseMapper;

  public InvoicePurchaseAnalysisStrategy(InvoicePurchaseMapper invoicePurchaseMapper) {
    this.invoicePurchaseMapper = invoicePurchaseMapper;
  }

  /** 分析策略 */
  @Override
  public void analyze(String data) {
    // AnalyzeData analyzeData = loadData(data);
    // List<InvoicePurchase> purchaseList = analyzeData.getPurchaseList();
    // 将purchaseList中的数据进行计算，最后插入到数据库
    log.warn("开始执行策略: 进项");
    try {
      // 模拟耗时操作
      Thread.sleep(5000);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }
    log.warn("完成执行策略: 进项");
  }

  /**
   * 获取分析策略类型
   *
   * @return 分析策略类型
   */
  @Override
  public String getStrategyType() {
    return "InvoicePurchaseAnalysisStrategy";
  }

  /**
   * 保存分析结果到数据库
   *
   * @param analyzeData 数据
   */
  @Override
  public void saveResult(AnalysisData analyzeData) {}

  /**
   * 加载策略所需数据
   *
   * @param taskId 任务ID
   * @return 数据对象
   */
  @Override
  public AnalysisData loadData(String taskId) {
    InvoicePurchase invoicePurchase = invoicePurchaseMapper.selectById(taskId);
    if (invoicePurchase == null) {
      return null;
    }
    List<InvoicePurchase> invoicePurchaseList = new ArrayList<>();
    invoicePurchaseList.add(invoicePurchase);
    return AnalysisData.builder().purchaseList(invoicePurchaseList).build();
  }
}
