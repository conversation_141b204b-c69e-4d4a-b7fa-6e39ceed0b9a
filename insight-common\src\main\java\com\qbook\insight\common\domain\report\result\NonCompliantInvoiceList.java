package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 取得不合规发票抵扣进项税清单
 *
 * <AUTHOR>
 */
@Data
public class NonCompliantInvoiceList {

  @ApiModelProperty("对方虚开发票受处理的发票清单")
  private List<invoice> fraudulentInvoices;

  @ApiModelProperty("一年以上挂账未支付的发票清单")
  private List<Object> overduePayables;

  @ApiModelProperty("中文名经检索和税目不一致的发票清单")
  private List<Object> mismatchedItems;

  @ApiModelProperty("一年以上不使用的存货对应的发票清单")
  private List<Object> unusedInventory;

  @ApiModelProperty("和销项不匹配，已结转成本的存货对应的发票清单")
  private List<Object> mismatchedCostInventory;
}
