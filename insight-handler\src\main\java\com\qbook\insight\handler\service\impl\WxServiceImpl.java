package com.qbook.insight.handler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qbook.insight.common.constant.RMsg;
import com.qbook.insight.common.constant.Role;
import com.qbook.insight.common.entity.User;
import com.qbook.insight.common.exception.BizException;
import com.qbook.insight.common.mapper.UserMapper;
import com.qbook.insight.common.util.JwtUtil;
import com.qbook.insight.common.util.RedisUtil;
import com.qbook.insight.common.util.SecurityUtil;
import com.qbook.insight.common.util.StringUtils;
import com.qbook.insight.handler.config.WxConfig;
import com.qbook.insight.handler.domain.WxTicket;
import com.qbook.insight.handler.domain.WxToken;
import com.qbook.insight.handler.domain.WxUserInfo;
import com.qbook.insight.handler.service.WxService;
import com.qbook.insight.handler.util.WxUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 微信操作
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-16 下午2:55
 */
@Service
@Slf4j
public class WxServiceImpl implements WxService {

  @Resource private WxUtils wxUtils;
  @Resource private WxConfig wxConfig;
  @Resource private RedisUtil redisUtil;
  @Resource private UserMapper userMapper;
  @Resource private JwtUtil jwtUtil;

  @Override
  public String wxGetNotify(String signature, String timestamp, String nonce, String echo) {
    boolean ok = wxUtils.checkSignature(signature, timestamp, nonce);
    return ok ? echo : "";
  }

  @Override
  public String wxPostCallback(Map<Object, Object> map) {
    log.info("Wx POST callback: {}", map.toString());
    String event = map.get("Event").toString();
    String openid = map.get("FromUserName").toString();
    if (StringUtils.isEmpty(event) || StringUtils.isEmpty(openid)) {
      throw new RuntimeException("event OR openid is empty");
    }

    String qrId = "";
    switch (event.toLowerCase()) {
      case "subscribe":
        qrId = map.get("EventKey").toString().substring(8);
        break;
      case "scan":
        qrId = map.get("EventKey").toString();
        break;
      case "unsubscribe":
        break;
    }
    if (StringUtils.isNotEmpty(qrId)) {
      redisUtil.set(wxConfig.getQrRedisPrefix() + qrId, openid, 60, TimeUnit.SECONDS);
    }
    return "SUCCESS";
  }

  @Override
  public Map<String, String> getQrCode() {
    String qrId = UUID.randomUUID().toString().replace("-", "");
    String qrUrl = getQrUrl(qrId);
    if (Objects.isNull(qrUrl)) {
      throw new BizException(RMsg.ERR_QR_CODE);
    }
    redisUtil.set(
        wxConfig.getQrRedisPrefix() + qrId, "wait", wxConfig.getQrRedisExpire(), TimeUnit.SECONDS);
    Map<String, String> result = new HashMap<>();
    result.put("id", qrId);
    result.put("url", qrUrl);
    return result;
  }

  @Override
  public User login(String qrId) {
    String openid = getOpenid(qrId);
    return openid == null ? null : loginWithOpenid(openid);
  }

  /**
   * 请求登录二维码
   *
   * @param scene 二维码的唯一标识
   * @return 二维码访问地址
   */
  public String getQrUrl(String scene) {
    try {
      WxTicket ticket = wxUtils.getTicket(getToken(), scene);
      return wxUtils.getQrCodeUrl(ticket.getTicket());
    } catch (Exception e) {
      throw new RuntimeException("获取二维码错误");
    }
  }

  /**
   * 获取公众号token，缓存机制
   *
   * @return token
   */
  public String getToken() {
    String token = redisUtil.get("wx:token");
    if (token != null) {
      return token;
    }

    WxToken accessToken = wxUtils.getAccessToken();
    token = accessToken.getAccessToken();
    if (StringUtils.isEmpty(token)) {
      throw new RuntimeException("获取accessToken失败");
    }
    redisUtil.set("wx:token", token, 7000, TimeUnit.SECONDS);
    return token;
  }

  /**
   * 根据openId获取用户信息
   *
   * @param openid 微信用户openid
   * @return 微信用户信息
   */
  public User getUserInfoByOpenid(String openid) {
    User user = userMapper.selectOne(new LambdaQueryWrapper<User>().eq(User::getWxOpenid, openid));
    if (user == null) {
      WxUserInfo userInfo = wxUtils.getUserInfo(getToken(), openid);
      user = new User();
      user.setWxOpenid(userInfo.getOpenid());
      user.setRealName(userInfo.getOpenid());
      user.setRoles(Role.USER);
      userMapper.insert(user);
    }

    return user;
  }

  /**
   * 根据openid进行登录操作
   *
   * @param openid 微信用户openid
   * @return 返回跳转的页面，供页面处理
   */
  public User loginWithOpenid(String openid) {
    if (openid == null || openid.isEmpty()) {
      throw new RuntimeException(RMsg.ERR_NO_OPENID);
    }
    User user = getUserInfoByOpenid(openid);
    if (user.getRealName() != null && user.getRealName().length() > 10) {
      user.setRealName(user.getRealName().substring(0, 10));
    }
    user.setPassword(null);
    SecurityUtil.setCurrentUser(user);
    user.setAccessToken(jwtUtil.generateToken(user));
    return user;
  }

  /**
   * 页面轮询的时候调用，根据二维码标识获取里面存的openid，如果里面还是wait，则返回null
   *
   * @param qrId 二维码标识
   */
  public String getOpenid(String qrId) {
    String value = redisUtil.get(wxConfig.getQrRedisPrefix() + qrId);
    if (value == null) {
      throw new BizException(4002, "二维码已过期");
    }
    if ("wait".equals(value)) {
      return null;
    }
    log.info("Qr-Code scanned，openid:{}", value);
    return value;
  }
}
