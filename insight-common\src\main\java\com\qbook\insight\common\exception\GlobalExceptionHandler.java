package com.qbook.insight.common.exception;

import com.qbook.insight.common.domain.R;
import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

  private R handle(HttpServletRequest request, int code, String message, String exception) {
    log.error("url:{} code:{} message:{}", request.getRequestURI(), code, exception);
    return R.error(code, message);
  }

  /** 拦截业务异常 */
  @ExceptionHandler(BizException.class)
  public R handleBizException(HttpServletRequest request, BizException e) {
    return handle(request, e.getCode(), e.getMessage(), e.getMessage());
  }

  /** 拦截未知的运行时异常 */
  @ExceptionHandler(RuntimeException.class)
  public R handleRuntimeException(HttpServletRequest request, RuntimeException e) {
    return handle(request, 400, "操作失败", e.getMessage());
  }

  /** 系统异常 */
  @ExceptionHandler(Exception.class)
  public R handleException(HttpServletRequest request, Exception e) {
    return handle(request, 400, "操作失败", e.getMessage());
  }

  /** 拦截其他未捕获的运行时异常 */
  @PostConstruct
  public void init() {
    Thread.setDefaultUncaughtExceptionHandler(
        (t, e) -> log.error("Uncaught Exception: {}", e.getMessage()));
  }
}
