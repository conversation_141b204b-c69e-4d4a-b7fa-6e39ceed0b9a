package com.qbook.insight.common.entity.account;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/** 科目余额及发生额 */
@Data
public class SubjectBalance {

  @ApiModelProperty("科目编号")
  private String subjectKey;

  @ApiModelProperty("币种")
  private String currency;

  @ApiModelProperty("辅助核算组")
  private String auxiliaryAccountingGroup;

  @ApiModelProperty("期初余额")
  private BigDecimal initialBalance;

  @ApiModelProperty("期初数量")
  private BigDecimal initialQuantity;

  @ApiModelProperty("期初外币余额")
  private BigDecimal initialForeignCurrencyBalance;

  @ApiModelProperty("借方发生额")
  private BigDecimal debitAmount;

  @ApiModelProperty("借方发生数量")
  private BigDecimal debitQuantity;

  @ApiModelProperty("借方外币发生额")
  private BigDecimal debitForeignCurrencyAmount;

  @ApiModelProperty("贷方发生额")
  private BigDecimal creditAmount;

  @ApiModelProperty("贷方发生数量")
  private BigDecimal creditQuantity;

  @ApiModelProperty("贷方外币发生额")
  private BigDecimal creditForeignCurrencyAmount;

  @ApiModelProperty("期末余额")
  private BigDecimal endingBalance;

  @ApiModelProperty("期末数量")
  private BigDecimal endingQuantity;

  @ApiModelProperty("期末外币余额")
  private BigDecimal endingForeignCurrencyBalance;

  @ApiModelProperty("会计月度")
  private Integer accountingMonth;
}
