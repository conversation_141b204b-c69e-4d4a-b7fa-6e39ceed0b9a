package com.qbook.insight.common.vo;

import com.qbook.insight.common.entity.IndicatorCategory;
import com.qbook.insight.common.entity.IndicatorConfig;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 指标分类实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorVo extends IndicatorCategory {

  @ApiModelProperty("指标计算配置")
  private List<IndicatorConfig> indicatorConfig;
}
