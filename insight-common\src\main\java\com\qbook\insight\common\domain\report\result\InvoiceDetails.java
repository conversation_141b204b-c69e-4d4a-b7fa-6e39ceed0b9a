package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 发票明细分析
 *
 * <AUTHOR>
 */
@Data
public class InvoiceDetails {

  @ApiModelProperty("对方虚开发票受处理的发票清单")
  private List<Invoice> fraudulentInvoices;

  @ApiModelProperty("一年以上挂账未支付的发票清单")
  private List<Object> overduePayables;

  @ApiModelProperty("中文名经检索和税目不一致的发票清单")
  private List<Object> mismatchedItems;

  @ApiModelProperty("一年以上不使用的存货对应的发票清单")
  private List<Object> unusedInventory;

  @ApiModelProperty("和销项不匹配，已结转成本的存货对应的发票清单")
  private List<Object> mismatchedCostInventory;
}
