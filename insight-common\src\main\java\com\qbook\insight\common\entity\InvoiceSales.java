package com.qbook.insight.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 销项发票-发票基础信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceSales {

  @TableId(type = IdType.AUTO)
  @ApiModelProperty(value = "主键ID")
  private Long id;

  @ApiModelProperty(value = "所属任务编号")
  private String taskId;

  @ApiModelProperty(value = "销方税号")
  private String taxId;

  @ApiModelProperty(value = "发票代码")
  private String zzfpdm;

  @ApiModelProperty(value = "发票号码")
  private String zzfphm;

  @ApiModelProperty(value = "数电票号码")
  private String fpkjfxlxdm;

  @ApiModelProperty(value = "购方名称")
  private String gmfmc;

  @ApiModelProperty(value = "购方识别号")
  private String gmfnsrsbh;

  @ApiModelProperty(value = "开票日期")
  private Date kprq;

  @ApiModelProperty(value = "金额")
  private BigDecimal hjje;

  @ApiModelProperty(value = "税额")
  private BigDecimal hjse;

  @ApiModelProperty(value = "价税合计")
  private BigDecimal jshj;

  @ApiModelProperty(value = "发票来源")
  private String fplydm;

  @ApiModelProperty(value = "发票票种")
  private String fppzdm;

  @ApiModelProperty(value = "发票状态")
  private String fpztdm;

  @ApiModelProperty(value = "发票风险等级")
  private String sflzfp;

  @ApiModelProperty(value = "开票人")
  private String kpr;

  @ApiModelProperty(value = "备注")
  private String bz;

  @ApiModelProperty(value = "开票日期年")
  private Integer kprqn;

  @ApiModelProperty(value = "开票日期月")
  private Integer kprqy;

  @ApiModelProperty(value = "采集时间")
  private Date gatherDatetime;

  @ApiModelProperty(value = "原始发票文件")
  private String ofdFile;

  @ApiModelProperty(value = "发票扩充内容（火车票、二手车销售票、机票行程单等）")
  private String expandContent;

  @ApiModelProperty(value = "创建时间")
  private Date createTime;
}
