package com.qbook.insight.handler.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

/**
 * 微信用户信息实体类
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-18 下午5:25
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WxUserInfo {

  /** 用户是否订阅该公众号，值为0时表示此用户没有关注该公众号，拉取不到其余信息 */
  private Integer subscribe;

  /** 用户的唯一标识 */
  private String openid;

  /** 用户昵称 */
  private String nickname;

  /** 用户的性别，值为1时是男性，值为2时是女性，值为0时是未知 */
  private Integer sex;

  /** 用户的语言，简体中文为zh_CN */
  private String language;

  /** 用户所在城市 */
  private String city;

  /** 用户所在省份 */
  private String province;

  /** 用户所在国家 */
  private String country;

  /** 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空 */
  @JsonProperty("headimgurl")
  private String headImgUrl;

  /** 用户关注时间，为时间戳。如果用户曾多次关注，则取最后关注时间 */
  @JsonProperty("subscribe_time")
  @JsonFormat(shape = JsonFormat.Shape.NUMBER)
  private Long subscribeTime;

  /** 公众号运营者对粉丝的备注，公众号运营者可在微信公众平台用户管理界面对粉丝添加备注 */
  private String remark;

  /** 用户所在的分组ID（兼容旧的用户分组接口） */
  @JsonProperty("groupid")
  private Integer groupId;

  /** 用户被打上的标签ID列表 */
  @JsonProperty("tagid_list")
  private List<Integer> tagidList;

  /** 返回用户关注的渠道来源 */
  @JsonProperty("subscribe_scene")
  private String subscribeScene;

  /** 二维码扫码场景（开发者自定义） */
  @JsonProperty("qr_scene")
  private Integer qrScene;

  /** 二维码扫码场景描述（开发者自定义） */
  @JsonProperty("qr_scene_str")
  private String qrSceneStr;
}
