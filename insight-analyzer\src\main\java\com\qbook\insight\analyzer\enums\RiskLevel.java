package com.qbook.insight.analyzer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 风险等级枚举类
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-03 14:25
 */
@Getter
@AllArgsConstructor
public enum RiskLevel {
  LOW(0, "低", "LOW"),
  MEDIUM_LOW(2, "中低", "MEDIUM_LOW"),
  MEDIUM(4, "中", "MEDIUM"),
  MEDIUM_HIGH(6, "中高", "MEDIUM_HIGH"),
  HIGH(8, "高", "HIGH"),
  VERY_HIGH(10, "非常高", "VERY_HIGH");

  private final Integer num;
  private final String description;
  private final String level;

  /**
   * 根据风险等级描述获取对应的枚举对象
   *
   * @param num 风险等级
   * @return 风险等级枚举对象
   */
  public static RiskLevel getByDescription(Integer num) {
    for (RiskLevel riskLevel : values()) {
      if (riskLevel.getNum().equals(num)) {
        return riskLevel;
      }
    }
    return null;
  }
}
