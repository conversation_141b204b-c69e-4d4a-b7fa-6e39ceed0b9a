package com.qbook.insight.handler.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.entity.MemberPackage;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 会员套餐接口
 *
 * <AUTHOR>
 */
@Service
public interface MemberPackageService {

  /** 添加会员套餐 */
  int add(MemberPackage pkg);

  /** 删除会员套餐 */
  int delete(long id);

  /** 修改会员套餐 */
  int update(long id, MemberPackage pkg);

  /** 获取会员套餐详情 */
  MemberPackage getById(long id);

  /** 获取会员套餐列表 */
  List<MemberPackage> list(String name, Integer isActive);

  /** 获取会员套餐列表(分页) */
  IPage<MemberPackage> page(PageParam pageParam, String name, Integer isActive);

  /** 获取激活的会员套餐列表 */
  List<MemberPackage> getActivePlans();
}
