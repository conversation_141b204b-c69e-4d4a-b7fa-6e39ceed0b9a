package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 印花税
 *
 * <AUTHOR>
 */
@Data
public class StampDuty {

  @ApiModelProperty("序号")
  private Integer index;

  @ApiModelProperty("年份")
  private Integer year;

  @ApiModelProperty("税目税率引用错误少申报税款(元)")
  private BigDecimal incorrectTaxCategory;

  @ApiModelProperty("注册资本与资本公积增加少申报税款(元)")
  private BigDecimal unrecordedCapitalIncrease;

  @ApiModelProperty("股权变动事项少申报税款(元)")
  private BigDecimal unrecordedEquityChanges;

  @ApiModelProperty("因查补的隐匿收入少申报税款(元)")
  private BigDecimal unrecordedHiddenIncome;
}
