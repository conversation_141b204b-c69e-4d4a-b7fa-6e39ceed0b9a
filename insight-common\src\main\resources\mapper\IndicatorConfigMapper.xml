<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.qbook.insight.common.mapper.IndicatorConfigMapper">
  <select id="selectConfigList"
    resultType="com.qbook.insight.common.entity.IndicatorConfig">
    SELECT indicator_code,execute_sql,return_type FROM `indicator_config` WHERE
    status = 1
  </select>
</mapper>
