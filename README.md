# 财税风控系统（慧眼）

模块划分:

* 风控Web服务： insight-handler
* 数据收集服务：insight-collector
* 数据分析服务：insight-analyzer
* 报告生成服务：insight-generator
* 公共模块：    insight-common


## 一. 风控Web服务

### 1.1 用户数据隔离方案

* 在数据库层面，凡是涉及到用户数据的表，均添加 `用户ID（user_id）` 字段。
* 普通用户在操作数据时，必须要携带 `用户ID` 字段。`用户ID` 存放在 JWT Token 中，由服务端解析出来后，放入到查询条件中。

### 1.2 接口角色权限控制

通过在 Controller 层添加 `@PreAuthorize` 注解的方式，控制角色的访问权限。该注解支持在类层面和方法层面进行控制。

| 方法          | 参数     | 描述                          |
|-------------|--------|-----------------------------|
| hasRole     | String | 判断用户是否拥有某个角色                |
| lacksRole   | String | 验证用户是否不具备某角色，与 hasRole逻辑相反  |
| hasAnyRoles | String | 验证用户是否具有以下任意一个角色，多个逗号分隔     |
| hasPermi    | String | 验证用户是否具备某权限                 |
| lacksPermi  | String | 验证用户是否不具备某权限，与 hasPermi逻辑相反 |
| hasAnyPermi | String | 验证用户是否具有以下任意一个权限            |

举例如下：

* @PreAuthorize("hasRole(@role.DEVELOPER)")：限制接口只能被开发人员访问
* @PreAuthorize("hasAnyRole(@role.ADMIN, @role.AUDITOR)")：限制接口只能被管理员、审核员访问

## 二. Git提交规范

* feat：添加新功能
* fix：修复问题/BUG
* style：代码样式是相关的，不影响运行结果
* perf：优化/性能改进
* refactor：重构重构
* revert：还原撤消编辑
* test：测试测试相关
* docs：文档/注释
* chore：杂务依赖更新/脚手架配置修改等。
* ci：持续集成
* types：类型定义文件更改