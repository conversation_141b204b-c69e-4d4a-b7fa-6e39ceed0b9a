<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.qbook.insight.common.mapper.ReportResultMapper">

  <sql id="selectReportResult">
    select id,user_id,report_id,result,created_at,updated_at from report_result
  </sql>

  <!-- JSON更新 -->
  <sql id="jsonUpdateFragment">
    <if test="setOperations != null and setOperations.size() > 0">
      ${jsonColumn} = JSON_SET(${jsonColumn}
      <foreach collection="setOperations" item="op" separator=",">
        , #{op.path}
        ,
        <choose>
          <when test="op.json">CAST(#{op.value} AS JSON)</when>
          <otherwise>#{op.value}</otherwise>
        </choose>
      </foreach>
      )
    </if>

    <if test="appendOperations != null and appendOperations.size() > 0">
      <if test="setOperations != null and setOperations.size() > 0">,</if>
      ${jsonColumn} = JSON_ARRAY_APPEND(${jsonColumn}
      <foreach collection="appendOperations" item="op"
        separator=",">
        , #{op.path}, CAST(#{op.value} AS JSON)
      </foreach>
      )
    </if>
  </sql>

  <update id="updateJsonColumn">
    update report_result
    set
    <include refid="jsonUpdateFragment" />
    where report_id = #{report_id}
  </update>

  <select id="selectByReportId">
    select result from report_result where report_id = #{reportId}
    <if test="userId != null">
      and user_id = #{userId}
    </if>
  </select>
</mapper>
