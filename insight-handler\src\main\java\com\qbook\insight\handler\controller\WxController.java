package com.qbook.insight.handler.controller;

import com.qbook.insight.common.domain.R;
import com.qbook.insight.handler.service.WxService;
import java.util.Map;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 微信相关接口
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-16 下午1:51
 */
@RestController
@RequestMapping("/wx")
@RequiredArgsConstructor
@Slf4j
public class WxController {

  @Resource private WxService wxService;

  /** 微信公众号Get回调 */
  @GetMapping("/callback")
  public String wxGetNotify(String signature, String timestamp, String nonce, String echo) {
    return wxService.wxGetNotify(signature, timestamp, nonce, echo);
  }

  /** 微信公众号Post回调 */
  @PostMapping(value = "/callback", produces = MediaType.APPLICATION_XML_VALUE)
  public String wxPostCallback(@RequestBody Map<Object, Object> map) {
    return wxService.wxPostCallback(map);
  }

  /** 获取二维码地址和生成的uuid（用户点击“扫码登录”后调用） */
  @GetMapping("/qrcode")
  public R qrcode() {
    return R.ok(wxService.getQrCode());
  }

  /**
   * 展示二维码后，页面需要轮询调用此请求，判定用户是否扫描并关注
   *
   * @param qrId 二维码标识
   * @return 扫描结果
   */
  @GetMapping("/check/{qrId}")
  public R login(@PathVariable("qrId") String qrId) {
    return R.ok(wxService.login(qrId));
  }
}
