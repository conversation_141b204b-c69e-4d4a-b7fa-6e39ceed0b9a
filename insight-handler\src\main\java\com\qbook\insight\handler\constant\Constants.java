package com.qbook.insight.handler.constant;

import java.util.Locale;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {
  /** UTF-8 字符集 */
  public static final String UTF8 = "UTF-8";

  /** GBK 字符集 */
  public static final String GBK = "GBK";

  /** 系统语言 */
  public static final Locale DEFAULT_LOCALE = Locale.SIMPLIFIED_CHINESE;

  /** http请求 */
  public static final String HTTP = "http://";

  /** https请求 */
  public static final String HTTPS = "https://";

  /** 通用成功标识 */
  public static final String SUCCESS = "0";

  /** 通用失败标识 */
  public static final String FAIL = "1";

  /** 登录成功 */
  public static final String LOGIN_SUCCESS = "Success";

  /** 注销 */
  public static final String LOGOUT = "Logout";

  /** 注册 */
  public static final String REGISTER = "Register";

  /** 登录失败 */
  public static final String LOGIN_FAIL = "Error";

  /** 系统用户授权缓存 */
  public static final String SYS_AUTH_CACHE = "sys-authCache";

  /** 参数管理 cache name */
  public static final String SYS_CONFIG_CACHE = "sys-config";

  /** 参数管理 cache key */
  public static final String SYS_CONFIG_KEY = "sys_config:";

  /** 字典管理 cache name */
  public static final String SYS_DICT_CACHE = "sys-dict";

  /** 字典管理 cache key */
  public static final String SYS_DICT_KEY = "sys_dict:";

  /** 资源映射路径 前缀 */
  public static final String RESOURCE_PREFIX = "/profile";

  /** RMI 远程方法调用 */
  public static final String LOOKUP_RMI = "rmi:";

  /** LDAP 远程方法调用 */
  public static final String LOOKUP_LDAP = "ldap:";

  /** LDAPS 远程方法调用 */
  public static final String LOOKUP_LDAPS = "ldaps:";

  /** 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加） */
  public static final String[] JOB_WHITELIST_STR = {"com.xihuanjun.quartz.task"};

  /** 定时任务违规的字符 */
  public static final String[] JOB_ERROR_STR = {
    "java.net.URL",
    "javax.naming.InitialContext",
    "org.yaml.snakeyaml",
    "org.springframework",
    "org.apache",
    "com.xihuanjun.common.utils.file",
    "com.xihuanjun.common.config",
    "com.xihuanjun.generator"
  };
}
