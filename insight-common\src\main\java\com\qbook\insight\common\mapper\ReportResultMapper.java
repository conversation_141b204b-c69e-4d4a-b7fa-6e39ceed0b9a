package com.qbook.insight.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qbook.insight.common.entity.ReportResult;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 报告结果 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ReportResultMapper extends BaseMapper<ReportResult> {

  int updateJsonColumn(Map<String, Object> params);

  String selectByReportId(@Param("reportId") Long reportId, @Param("userId") Long userId);
}
