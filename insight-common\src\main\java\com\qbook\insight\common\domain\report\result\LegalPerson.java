package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.DataSource;
import com.qbook.insight.common.annotation.ResultLevel1;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 法人信息
 *
 * <AUTHOR>
 */
@Data
public class LegalPerson {

  @ApiModelProperty("法人姓名")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private String name;

  @ApiModelProperty("法人年龄")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private Integer age;

  @ApiModelProperty("法人证件")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private String idNumber;

  @ApiModelProperty("法人证件类型")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息")
  private String idType;

  @ApiModelProperty("法人籍贯")
  @ResultLevel1
  @DataSource("电子税务局:纳税人信息(根据身份证号判断)")
  private String origin;
}
