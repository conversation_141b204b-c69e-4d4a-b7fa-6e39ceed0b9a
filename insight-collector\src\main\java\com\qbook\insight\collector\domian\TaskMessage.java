package com.qbook.insight.collector.domian;

import lombok.Data;

/**
 * MQ任务消息实体类
 *
 * <AUTHOR>
 */
@Data
public class TaskMessage {
  // 任务ID
  private String taskId;
  // 任务所属系统
  private String ownerSystem;
  // 结果队列
  private String resultQueue;
  // 任务所属年份
  private Integer ownerYear;
  // 任务所属月份
  private Integer ownerMonth;
  // jgkey
  private String jgkey;
  // 公司名称
  private String name;
  // 纳税人识别号
  private String taxId;
  // 登录纳税人识别号
  private String loginTaxId;
  // 登录用户ID
  private String operateId;
  // 登录密码
  private String password;
  // 任务类型
  private Integer taskType;
  // 任务内容
  private String taskContent;
  // 省份
  private String province;
}
