package com.qbook.insight.common.entity.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/** 电子账簿 */
@Data
public class AccountLedger {

  @ApiModelProperty("电子账簿编号")
  private String ledgerNo;

  @ApiModelProperty("电子账簿名称")
  private String ledgerName;

  @ApiModelProperty("会计核算单位")
  private String accountingUnit;

  @ApiModelProperty("组织机构代码")
  private String organizationCode;

  @ApiModelProperty("单位性质")
  private String unitNature;

  @ApiModelProperty("行业")
  private String industry;

  @ApiModelProperty("开发单位")
  private String developer;

  @ApiModelProperty("版本号")
  private String version;

  @ApiModelProperty("年度")
  private String year;

  @ApiModelProperty("本位币")
  private String baseCurrency;

  @ApiModelProperty("科目结构")
  private String accountStructure;
}
