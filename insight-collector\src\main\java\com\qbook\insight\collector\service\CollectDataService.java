package com.qbook.insight.collector.service;

import static com.qbook.insight.common.enums.CollectDataType.INVOICE;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qbook.insight.collector.constant.MqTaskQueue;
import com.qbook.insight.collector.constant.MqTaskStatus;
import com.qbook.insight.collector.domian.TaskContent;
import com.qbook.insight.collector.domian.TaskMessage;
import com.qbook.insight.common.entity.*;
import com.qbook.insight.common.enums.CollectDataType;
import com.qbook.insight.common.exception.BizException;
import com.qbook.insight.common.mapper.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 采集数据服务
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-17 下午5:20
 */
@Service
@Slf4j
public class CollectDataService {

  @Resource private CorpMapper corpMapper;
  @Resource private CorpDetailMapper corpDetailMapper;
  @Resource private ClientMapper clientMapper;
  @Resource private ReportTemplateMapper reportTemplateMapper;
  @Resource private CollectorTaskMapper collectorTaskMapper;
  @Resource private InvoicePurchaseMapper invoicePurchaseMapper;
  @Resource private InvoiceSalesMapper invoiceSalesMapper;

  /** 采集任务 */
  public void collectTask(Report report) {
    Integer templateId = report.getTemplateId();
    if (templateId == null) {
      return;
    }
    ReportTemplate reportTemplate = reportTemplateMapper.selectById(templateId);
    if (reportTemplate == null) {
      throw new BizException("报告模板不存在");
    }
    String collectorTypes = reportTemplate.getCollectorTypes();
    if (collectorTypes == null) {
      return;
    }
    String[] types = collectorTypes.split(",");
    for (String type : types) {
      CollectDataType collectDataType = CollectDataType.getByCode(type);
      switch (collectDataType) {
        case INVOICE:
          log.info("开始处理发票采集");
          processInvoiceTask(report);
          break;
        case ACCOUNT:
          log.info("开始处理账套采集");
          // processAccountTask(report);
          break;
        case DECLARATION:
          log.info("开始处理申报表采集");
          // processDeclarationTask(report);
          break;
        default:
          log.error("未知数据类型:{}", collectDataType);
          break;
      }
    }
  }

  /** 发票采集 */
  private void processInvoiceTask(Report report) {
    List<TaskMessage> taskMessages = buildInvoiceTaskData(report);
    taskMessages.forEach(
        taskMessage -> {
          log.info("当前任务:{}", JSONUtil.toJsonStr(taskMessage));
          // 发送消息
          MqSender.send(MqTaskQueue.TASK_QUEUE, JSONUtil.toJsonStr(taskMessage));
          // 标记任务
          CollectorTask collectorTask = new CollectorTask();
          collectorTask.setTaskId(taskMessage.getTaskId());
          collectorTask.setTaxId(taskMessage.getTaxId());
          collectorTask.setCollectorType(taskMessage.getTaskType());
          collectorTask.setStatus(MqTaskStatus.START);
          collectorTask.setTaskStart(DateUtil.parse(DateUtil.now()));
          String taskContent = taskMessage.getTaskContent();
          TaskContent taskContentObj = JSONUtil.toBean(taskContent, TaskContent.class);
          collectorTask.setDataStart(DateUtil.parse(taskContentObj.getStartDate()));
          collectorTask.setDataEnd(DateUtil.parse(taskContentObj.getEndDate()));
          collectorTaskMapper.insert(collectorTask);
        });
  }

  /** 构建发票采集任务数据 */
  private List<TaskMessage> buildInvoiceTaskData(Report report) {
    try {
      List<TaskMessage> list = new ArrayList<>();
      Long corpId = report.getCorpId();
      Corp corp = corpMapper.selectById(corpId);
      if (corp == null) {
        throw new BizException("公司不存在");
      }
      CorpDetail corpDetail =
          corpDetailMapper.selectOne(
              new LambdaQueryWrapper<CorpDetail>().eq(CorpDetail::getCorpId, corpId));
      if (corpDetail == null) {
        throw new BizException("公司详情不存在");
      }
      Client client =
          clientMapper.selectOne(new LambdaQueryWrapper<Client>().eq(Client::getCorpId, corpId));
      if (client == null) {
        throw new BizException("公司客户不存在");
      }
      String taxId = corp.getTaxId();
      if (taxId == null) {
        throw new BizException("公司税号不存在");
      }
      // 构建三年阶段任务内容
      List<TaskContent> taskContents = buildThreeYearTaskContents();
      List<TaskContent> unCollectedTimeRanges = taskContents;
      Long invoiceCount =
          invoicePurchaseMapper.selectCount(
              new LambdaQueryWrapper<InvoicePurchase>().eq(InvoicePurchase::getTaxId, taxId));
      if (invoiceCount > 0) {
        // 获取未采集的阶段时间
        unCollectedTimeRanges = getUnCollectedTimeRange(taxId, taskContents);
      }

      for (TaskContent taskContent : unCollectedTimeRanges) {
        TaskMessage taskMessage =
            getTaskMessages(corp, corpDetail, client, Integer.parseInt(INVOICE.getCode()));
        taskMessage.setTaskContent(JSONUtil.toJsonStr(taskContent));
        list.add(taskMessage);
      }
      return list;
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /** 获取发票采集任务消息 */
  private TaskMessage getTaskMessages(
      Corp corp, CorpDetail corpDetail, Client client, Integer taskType) {
    try {
      String ownerSystem = "风控系统";
      TaskMessage taskMessage = new TaskMessage();
      // 生成唯一32位ID
      UUID uuid = UUID.randomUUID();
      String taskId = uuid.toString().replace("-", "");
      taskMessage.setTaskId(taskId);
      taskMessage.setOwnerSystem(ownerSystem);
      taskMessage.setResultQueue(MqTaskQueue.FKSERVICE_RESULT_QUEUE);
      taskMessage.setOwnerYear(DateUtil.year(DateUtil.date()));
      taskMessage.setOwnerMonth(DateUtil.month(DateUtil.date()) + 1);
      taskMessage.setJgkey("");
      taskMessage.setName(Optional.ofNullable(corp.getName()).orElse(""));
      taskMessage.setTaxId(Optional.ofNullable(corp.getTaxId()).orElse(""));
      taskMessage.setLoginTaxId(Optional.ofNullable(client.getLoginTaxId()).orElse(""));
      taskMessage.setOperateId(Optional.ofNullable(client.getLoginPhone()).orElse(""));
      taskMessage.setPassword(Optional.ofNullable(client.getLoginPassword()).orElse(""));
      taskMessage.setTaskType(taskType);
      taskMessage.setProvince(Optional.ofNullable(corpDetail.getRegOrgProvince()).orElse(""));
      return taskMessage;
    } catch (BizException e) {
      throw new RuntimeException(e);
    }
  }

  /** 获取任务信息 按照三年的账期进行任务 */
  public TaskContent getTaskContent(LocalDate startDate, LocalDate endDate) {
    try {
      TaskContent taskContent = new TaskContent();
      taskContent.setStartDate(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
      taskContent.setEndDate(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
      return taskContent;
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /** 构建任务列表 */
  public List<TaskContent> buildThreeYearTaskContents() {
    List<TaskContent> taskContents = new ArrayList<>();
    LocalDate now = LocalDate.now();
    // 当前年份
    LocalDate startOfYear = now.withDayOfYear(1).plusDays(1);
    LocalDate endOfYear = now.withDayOfYear(now.lengthOfYear());

    // 去年
    LocalDate lastYearStart = startOfYear.minusYears(1);
    LocalDate lastYearEnd = endOfYear.minusYears(1);
    // 前年
    LocalDate twoYearsAgoStart = startOfYear.minusYears(2);
    LocalDate twoYearsAgoEnd = endOfYear.minusYears(2);
    // 添加三年任务
    taskContents.add(getTaskContent(startOfYear, now));
    taskContents.add(getTaskContent(lastYearStart, lastYearEnd));
    taskContents.add(getTaskContent(twoYearsAgoStart, twoYearsAgoEnd));
    return taskContents;
  }

  /** 获取数据库未采集的时间段 */
  public List<TaskContent> getUnCollectedTimeRange(String taxId, List<TaskContent> taskContents) {
    try {
      List<TaskContent> uncollectedRanges = new ArrayList<>();

      // 查询数据库中该税号最早和最晚的采集时间
      Map<String, LocalDateTime> purchMinMaxMap =
          invoicePurchaseMapper.getMinMaxKprqDateByTaxId(taxId);
      Map<String, LocalDateTime> saleMinMaxMap = invoiceSalesMapper.getMinMaxKprqDateByTaxId(taxId);
      // 获取销项的最早和最晚的采集时间
      LocalDateTime minSaleTime = saleMinMaxMap.get("minDate");
      LocalDateTime maxSaleTime = saleMinMaxMap.get("maxDate");
      // 获取进项的最早和最晚的采集时间
      LocalDateTime minPurTime = purchMinMaxMap.get("minDate");
      LocalDateTime maxPurTime = purchMinMaxMap.get("maxDate");

      // 计算整体最小和最大时间
      LocalDateTime overallMinTime;
      LocalDateTime overallMaxTime;

      if (minPurTime != null && minSaleTime != null) {
        overallMinTime = minPurTime.isBefore(minSaleTime) ? minPurTime : minSaleTime;
      } else {
        overallMinTime = minPurTime != null ? minPurTime : minSaleTime;
      }

      if (maxPurTime != null && maxSaleTime != null) {
        overallMaxTime = maxPurTime.isAfter(maxSaleTime) ? maxPurTime : maxSaleTime;
      } else {
        overallMaxTime = maxPurTime != null ? maxPurTime : maxSaleTime;
      }

      for (TaskContent taskContent : taskContents) {
        TaskContent newContent = new TaskContent();
        LocalDate startDate = LocalDate.parse(taskContent.getStartDate());
        LocalDate endDate = LocalDate.parse(taskContent.getEndDate());

        // 如果数据库中没有采集过数据，整个时间段都算未采集
        if (overallMaxTime == null) {
          uncollectedRanges.add(taskContent);
          continue;
        }

        // 如果startDate、endDate在overallMinTime-overallMaxTime之内，则跳过
        if (!startDate.isBefore(overallMinTime.toLocalDate())
            && !endDate.isAfter(overallMaxTime.toLocalDate())) {
          continue;
        }

        // 如果endDate在overallMaxTime之后，则将StartDate设置为overallMaxTime
        LocalDate newStart = overallMaxTime.toLocalDate().plusDays(1);

        // 新的的开始时间和结束时间超过366天，则从结束日期往前推365天作为新的开始日期
        long intervalDays = ChronoUnit.DAYS.between(newStart, endDate);
        if (intervalDays > 366) {
          newStart = endDate.minusDays(365);
        }

        newContent.setStartDate(newStart.format(DateTimeFormatter.ISO_DATE));
        newContent.setEndDate(endDate.format(DateTimeFormatter.ISO_DATE));
        uncollectedRanges.add(newContent);
      }

      return uncollectedRanges;
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
}
