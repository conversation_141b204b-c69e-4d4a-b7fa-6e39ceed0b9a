<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.qbook.insight.handler.mapper.MemberOrderMapper">

  <select id="pageVO"
    resultType="com.qbook.insight.handler.vo.MemberOrderVO">
    select
    plan_id,order_id,mo.amount,status,payment_method,mo.created_at,paid_at,mp.name
    as plan_name
    from member_order mo
    left join member_package mp on mo.plan_id =mp.id
    where mo.user_id = #{userId}
    <if test="status != null and status != ''">
      and status = #{status}
    </if>
    <if test="paymentMethod != null and paymentMethod != ''">
      and payment_method = #{paymentMethod}
    </if>
    order by mo.created_at desc
  </select>

</mapper>
