package com.qbook.insight.generator.utill;

import java.awt.*;
import java.io.*;
import java.math.BigInteger;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.util.*;
import java.util.List;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.docx4j.Docx4J;
import org.docx4j.fonts.IdentityPlusMapper;
import org.docx4j.fonts.Mapper;
import org.docx4j.fonts.PhysicalFonts;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.FontTablePart;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.toc.TocGenerator;
import org.docx4j.wml.Style;
import org.docx4j.wml.Styles;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtils;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.axis.CategoryAxis;
import org.jfree.chart.axis.NumberAxis;
import org.jfree.chart.labels.PieSectionLabelGenerator;
import org.jfree.chart.labels.StandardPieSectionLabelGenerator;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.plot.PiePlot;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.chart.renderer.category.BarRenderer;
import org.jfree.chart.renderer.category.LineAndShapeRenderer;
import org.jfree.chart.renderer.category.StandardBarPainter;
import org.jfree.chart.ui.RectangleInsets;
import org.jfree.chart.util.Rotation;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

/** Word模板处理工具类 用于处理税务风控报告Word模板，替换占位符，并转换为PDF */
@Slf4j
public class WordTemplateToPdfUtil {

  private static final String FONT_PATH =
      Paths.get("insight-generator", "src", "main", "resources", "fonts", "simsun.ttc").toString();
  private static final Font DEFAULT_FONT;
  public static final int DEFAULT_FONT_SIZE = 20;

  static {
    Font font;
    try {
      File fontFile = new File(FONT_PATH);
      if (fontFile.exists()) {
        font = Font.createFont(Font.TRUETYPE_FONT, fontFile);
        GraphicsEnvironment.getLocalGraphicsEnvironment().registerFont(font);
        font = font.deriveFont(Font.PLAIN, DEFAULT_FONT_SIZE);
        log.debug("成功加载自定义字体: {}", FONT_PATH);
      } else {
        log.warn("字体文件不存在: {}，使用系统默认字体", FONT_PATH);
        font = new Font(Font.SANS_SERIF, Font.PLAIN, DEFAULT_FONT_SIZE);
      }
    } catch (IOException | FontFormatException e) {
      log.warn("加载自定义字体失败，使用系统默认字体", e);
      font = new Font(Font.SANS_SERIF, Font.PLAIN, DEFAULT_FONT_SIZE);
    }
    DEFAULT_FONT = font;
  }

  /** 文本样式类，用于定义文档中文本的样式属性 */
  @Getter
  public static class TextStyle {
    /** 文本内容 */
    private final String text;
    /** 文本颜色（十六进制格式） */
    private String color;
    /** 超链接URL */
    private String hyperlink;

    public TextStyle(String text) {
      this.text = text;
    }

    public TextStyle(String text, String color) {
      this.text = text;
      this.color = color;
    }

    public static TextStyle create(String text) {
      return new TextStyle(text);
    }

    public TextStyle withColor(String color) {
      this.color = color;
      return this;
    }

    public TextStyle withHyperlink(String url) {
      this.hyperlink = url;
      return this;
    }
  }

  /** 表格数据类，用于存储表格数据 */
  @Getter
  public static class TableData {
    /** 表格内容数据 */
    private final List<List<String>> data;
    /** 列宽比例，和为1 */
    private final double[] columnWidthRatios;

    public TableData(List<List<String>> data, double[] columnWidthRatios) {
      this.data = data;
      // 验证并规范化列宽比例
      if (columnWidthRatios == null || columnWidthRatios.length != data.get(0).size()) {
        // 如果未指定比例或比例数量不匹配，使用平均分配
        this.columnWidthRatios = new double[data.get(0).size()];
        Arrays.fill(this.columnWidthRatios, 1.0 / data.get(0).size());
      } else {
        // 规范化比例，确保总和为1
        double sum = Arrays.stream(columnWidthRatios).sum();
        this.columnWidthRatios =
            Arrays.stream(columnWidthRatios).map(ratio -> ratio / sum).toArray();
      }
    }
  }

  /** 图表数据类，用于存储和管理图表的所有相关数据 */
  @Getter
  public static class ChartData {
    /** 图表类型 */
    private final ChartType type;
    /** 图表标题 */
    private final String title;
    /** 分类轴（X轴）标签 */
    private final String categoryLabel;
    /** 数值轴（Y轴）标签 */
    private final String valueLabel;
    /** 图表宽度（像素） */
    private final int width;
    /** 图表高度（像素） */
    private final int height;
    /** 图表数据，格式：{系列名称 -> {分类名称 -> 数值}} */
    private final Map<String, Map<String, Double>> categoryData;

    public ChartData(
        ChartType type,
        String title,
        String categoryLabel,
        String valueLabel,
        int width,
        int height) {
      this.type = type;
      this.title = title;
      this.categoryLabel = categoryLabel;
      this.valueLabel = valueLabel;
      this.width = width;
      this.height = height;
      this.categoryData = new HashMap<>();
    }

    /**
     * 添加新的数据系列
     *
     * @param seriesName 系列名称
     */
    public void addSeries(String seriesName) {
      categoryData.put(seriesName, new LinkedHashMap<>());
    }

    /**
     * 添加数据点到指定系列
     *
     * @param seriesName 系列名称
     * @param category 分类名称
     * @param value 数值
     */
    public void addDataPoint(String seriesName, String category, double value) {
      if (!categoryData.containsKey(seriesName)) {
        // 使用LinkedHashMap以保持插入顺序
        categoryData.put(seriesName, new LinkedHashMap<>());
      }
      categoryData.get(seriesName).put(category, value);

      // 如果是饼图数据，每次添加数据后都进行排序
      if (type == ChartType.PIE_CHART) {
        Map<String, Double> seriesData = categoryData.get(seriesName);
        // 将数据转换为列表并按值降序排序
        List<Map.Entry<String, Double>> sortedEntries = new ArrayList<>(seriesData.entrySet());
        sortedEntries.sort((e1, e2) -> Double.compare(e2.getValue(), e1.getValue()));

        // 清空原有数据并按排序后的顺序重新添加
        seriesData.clear();
        for (Map.Entry<String, Double> entry : sortedEntries) {
          seriesData.put(entry.getKey(), entry.getValue());
        }
      }
    }
  }

  /**
   * 图表类型枚举，定义支持的图表类型 BAR_CHART: 柱状图，适用于分类数据的比较 LINE_CHART: 折线图，适用于趋势数据的展示 PIE_CHART: 饼图，适用于占比数据的展示
   */
  public enum ChartType {
    BAR_CHART,
    LINE_CHART,
    PIE_CHART
  }

  /** 常用颜色常量 */
  public static final class Colors {
    // 基础颜色
    public static final String RED = "FF0000";
    public static final String GREEN = "00FF00";
    public static final String BLUE = "0000FF";
    public static final String BLACK = "000000";

    // 文档常用颜色
    public static final String LIGHT_RED = "FF9999"; // 浅红色
    public static final String LIGHT_GREEN = "99FF99"; // 浅绿色
    public static final String LIGHT_BLUE = "9999FF"; // 浅蓝色
    public static final String DARK_RED = "800000"; // 深红色
    public static final String DARK_GREEN = "008000"; // 深绿色
    public static final String DARK_BLUE = "000080"; // 深蓝色

    // 强调色
    public static final String WARNING_RED = "FF4444"; // 警告红
    public static final String SUCCESS_GREEN = "44BB44"; // 成功绿
    public static final String INFO_BLUE = "4444FF"; // 信息蓝
    public static final String GRAY = "808080"; // 灰色
    public static final String LIGHT_GRAY = "F2F2F2"; // 浅灰色

    // 风险等级颜色
    public static final String HIGH_RISK = "FF4444"; // 高风险-红色
    public static final String MEDIUM_RISK = "FFAA33"; // 中风险-橙色
    public static final String LOW_RISK = "FF9999"; // 低风险-浅红色
    public static final String FREE_RISK = "44BB44"; // 无风险-绿色

    // 财务数据颜色
    public static final String POSITIVE_VALUE = "44BB44"; // 正值-绿色
    public static final String NEGATIVE_VALUE = "FF4444"; // 负值-红色
    public static final String NEUTRAL_VALUE = "4444FF"; // 中性值-蓝色

    // 图表颜色系列
    private static final Color[] CHART_COLORS =
        new Color[] {
          new Color(65, 105, 225), // 皇家蓝
          new Color(46, 139, 87), // 海洋绿
          new Color(205, 92, 92), // 印度红
          new Color(147, 112, 219), // 中等紫色
          new Color(60, 179, 113), // 中等海洋绿
          new Color(238, 130, 238), // 紫罗兰
          new Color(30, 144, 255), // 道奇蓝
          new Color(255, 165, 0), // 橙色
          new Color(106, 90, 205), // 石板蓝
          new Color(218, 112, 214), // 兰花紫
          new Color(0, 139, 139), // 深青色
          new Color(255, 99, 71) // 番茄色
        };

    /**
     * 获取图表颜色
     *
     * @param index 颜色索引
     * @return 对应的颜色
     */
    public static Color getChartColor(int index) {
      return CHART_COLORS[index % CHART_COLORS.length];
    }
  }

  /** 占位符替换信息类，用于存储文档中占位符的位置和替换样式信息 */
  private static class PlaceholderReplacement {
    /** 占位符在文本中的起始位置 */
    final int startIndex;
    /** 占位符在文本中的结束位置 */
    final int endIndex;
    /** 替换文本的样式 */
    final TextStyle style;

    PlaceholderReplacement(int startIndex, int endIndex, TextStyle style) {
      this.startIndex = startIndex;
      this.endIndex = endIndex;
      this.style = style;
    }
  }

  /** 文本运行信息类，用于存储Word文档中文本运行(Run)的完整样式信息 */
  private static class RunInfo {
    /** 文本运行的起始位置 */
    final int startIndex;
    /** 文本运行的结束位置 */
    final int endIndex;
    /** 文本颜色（十六进制格式） */
    final String color;
    /** 文本内容 */
    final String text;
    /** 基础字体族 */
    final String fontFamily;
    /** 东亚文字字体 */
    final String eastAsiaFont;
    /** ASCII字符字体 */
    final String asciiFont;
    /** 西文字体 */
    final String hAnsiFont;
    /** 字号大小 */
    final Integer fontSize;
    /** 是否加粗 */
    final Boolean isBold;
    /** 是否斜体 */
    final Boolean isItalic;

    RunInfo(
        int startIndex,
        int endIndex,
        String color,
        String text,
        String fontFamily,
        String eastAsiaFont,
        String asciiFont,
        String hAnsiFont,
        Integer fontSize,
        Boolean isBold,
        Boolean isItalic) {
      this.startIndex = startIndex;
      this.endIndex = endIndex;
      this.color = color;
      this.text = text;
      this.fontFamily = fontFamily;
      this.eastAsiaFont = eastAsiaFont;
      this.asciiFont = asciiFont;
      this.hAnsiFont = hAnsiFont;
      this.fontSize = fontSize;
      this.isBold = isBold;
      this.isItalic = isItalic;
    }
  }

  /**
   * 从模板生成PDF并替换占位符
   *
   * @param textPlaceholders 文本占位符数据
   * @param tablePlaceholders 表格占位符数据
   * @param chartPlaceholders 图表占位符数据
   * @param templateFilePath 模板路径
   * @return PDF文件的字节数组
   */
  public static byte[] generatePdfFromTemplate(
      Map<String, Object> textPlaceholders,
      Map<String, TableData> tablePlaceholders,
      Map<String, ChartData> chartPlaceholders,
      String templateFilePath)
      throws Exception {

    log.debug("开始处理Word模板并生成PDF");

    try (InputStream templateStream =
        WordTemplateToPdfUtil.class.getResourceAsStream(templateFilePath)) {
      if (templateStream == null) {
        throw new FileNotFoundException("模板文件不存在: " + templateFilePath);
      }

      XWPFDocument document = new XWPFDocument(templateStream);

      // 替换各类占位符
      replaceTextPlaceholders(document, textPlaceholders);
      if (tablePlaceholders != null && !tablePlaceholders.isEmpty()) {
        replaceTablePlaceholders(document, tablePlaceholders);
      }
      if (chartPlaceholders != null && !chartPlaceholders.isEmpty()) {
        replaceChartPlaceholders(document, chartPlaceholders);
      }

      ByteArrayOutputStream docxOutputStream = new ByteArrayOutputStream();
      document.write(docxOutputStream);
      docxOutputStream.close();
      byte[] docxBytes = docxOutputStream.toByteArray();

      // 更新目录
      WordprocessingMLPackage wordMLPackage =
          WordprocessingMLPackage.load(new ByteArrayInputStream(docxBytes));
      TocGenerator tocGenerator = new TocGenerator(wordMLPackage);
      tocGenerator.updateToc(false);
      updateTocStyles(wordMLPackage);

      // 设置字体映射
      extractedFontMappings(wordMLPackage);

      // 转换为PDF
      ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
      Docx4J.toPDF(wordMLPackage, pdfOutputStream);

      return pdfOutputStream.toByteArray();
    } catch (Exception e) {
      log.error("生成PDF时发生错误", e);
      throw e;
    }
  }

  private static void extractedFontMappings(WordprocessingMLPackage wordMLPackage)
      throws Exception {

    Mapper fontMapper = new IdentityPlusMapper();

    fontMapper.put("宋体", PhysicalFonts.get("SimSun"));
    fontMapper.put("黑体", PhysicalFonts.get("SimHei"));
    fontMapper.put("楷体", PhysicalFonts.get("KaiTi"));
    fontMapper.put("仿宋", PhysicalFonts.get("FangSong"));
    fontMapper.put("新宋体", PhysicalFonts.get("NSimSun"));
    fontMapper.put("华文行楷", PhysicalFonts.get("STXingkai"));
    fontMapper.put("华文仿宋", PhysicalFonts.get("STFangsong"));
    fontMapper.put("幼圆", PhysicalFonts.get("YouYuan"));
    fontMapper.put("华文宋体", PhysicalFonts.get("STSong"));
    fontMapper.put("华文中宋", PhysicalFonts.get("STZhongsong"));
    fontMapper.put("等线", PhysicalFonts.get("dengxian bold"));
    fontMapper.put("等线 Light", PhysicalFonts.get("DengXian Light"));
    fontMapper.put("华文琥珀", PhysicalFonts.get("STHupo"));
    fontMapper.put("华文隶书", PhysicalFonts.get("STLiti"));
    fontMapper.put("华文新魏", PhysicalFonts.get("STXinwei"));
    fontMapper.put("华文彩云", PhysicalFonts.get("STCaiyun"));
    fontMapper.put("华文细黑", PhysicalFonts.get("STXihei"));
    fontMapper.put("方正姚体", PhysicalFonts.get("FZYaoti"));
    fontMapper.put("方正舒体", PhysicalFonts.get("FZShuTi"));
    fontMapper.put("宋体扩展", PhysicalFonts.get("simsun-extB"));
    fontMapper.put("仿宋_GB2312", PhysicalFonts.get("FangSong_GB2312"));
    fontMapper.put("微软雅黑", PhysicalFonts.get("Microsoft YaHei UI"));

    wordMLPackage.setFontMapper(fontMapper);

    // 添加字体替换规则
    MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
    if (documentPart.getFontTablePart() == null) {
      documentPart.addTargetPart(new FontTablePart());
    }
  }

  /** 更新目录样式移除蓝色和下划线 */
  private static void updateTocStyles(WordprocessingMLPackage wordMLPackage) {
    try {
      MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
      Styles styles = documentPart.getStyleDefinitionsPart().getJaxbElement();

      // 修改TOC样式
      for (Style style : styles.getStyle()) {
        String styleId = style.getStyleId();
        if (styleId != null && (styleId.startsWith("TOC") || styleId.equals("Hyperlink"))) {
          // 移除超链接样式
          if (style.getRPr() != null) {
            style.getRPr().setColor(null); // 移除颜色
            style.getRPr().setU(null); // 移除下划线
          }
        }
      }
    } catch (Exception e) {
      log.warn("更新目录样式时发生错误", e);
    }
  }

  /** 替换文档中的所有文本占位符 */
  private static void replaceTextPlaceholders(
      XWPFDocument document, Map<String, Object> placeholders) {
    log.debug("开始替换文本占位符...");

    // 处理主文档段落
    for (XWPFParagraph paragraph : document.getParagraphs()) {
      replacePlaceholdersInParagraph(paragraph, placeholders);
    }

    // 处理表格中的段落
    for (XWPFTable table : document.getTables()) {
      for (XWPFTableRow row : table.getRows()) {
        for (XWPFTableCell cell : row.getTableCells()) {
          for (XWPFParagraph paragraph : cell.getParagraphs()) {
            replacePlaceholdersInParagraph(paragraph, placeholders);
          }
        }
      }
    }

    // 处理页眉页脚
    processHeaderFooter(document, placeholders);

    log.debug("文本占位符替换完成");
  }

  /** 处理页眉页脚中的占位符 */
  private static void processHeaderFooter(XWPFDocument document, Map<String, Object> placeholders) {
    // 处理页眉
    for (XWPFHeader header : document.getHeaderList()) {
      for (XWPFParagraph paragraph : header.getParagraphs()) {
        replacePlaceholdersInParagraph(paragraph, placeholders);
      }
      processTableInSection(header.getTables(), placeholders);
    }

    // 处理页脚
    for (XWPFFooter footer : document.getFooterList()) {
      for (XWPFParagraph paragraph : footer.getParagraphs()) {
        replacePlaceholdersInParagraph(paragraph, placeholders);
      }
      processTableInSection(footer.getTables(), placeholders);
    }
  }

  /** 处理文档部分中的表格 */
  private static void processTableInSection(
      List<XWPFTable> tables, Map<String, Object> placeholders) {
    for (XWPFTable table : tables) {
      for (XWPFTableRow row : table.getRows()) {
        for (XWPFTableCell cell : row.getTableCells()) {
          for (XWPFParagraph paragraph : cell.getParagraphs()) {
            replacePlaceholdersInParagraph(paragraph, placeholders);
          }
        }
      }
    }
  }

  /** 替换段落中的占位符 */
  private static void replacePlaceholdersInParagraph(
      XWPFParagraph paragraph, Map<String, Object> placeholders) {
    try {
      // 获取完整的段落文本和runs
      List<XWPFRun> runs = paragraph.getRuns();
      if (runs == null || runs.isEmpty()) {
        return;
      }

      // 构建完整的段落文本和run映射
      StringBuilder fullText = new StringBuilder();
      List<RunInfo> runInfos = new ArrayList<>();
      int currentPosition = 0;

      for (XWPFRun run : runs) {
        String runText = run.getText(0);
        if (runText != null) {
          fullText.append(runText);
          runInfos.add(
              new RunInfo(
                  currentPosition,
                  currentPosition + runText.length(),
                  run.getColor(),
                  runText,
                  run.getFontFamily(),
                  run.getCTR().getRPr().getRFonts().getEastAsia(),
                  run.getCTR().getRPr().getRFonts().getAscii(),
                  run.getCTR().getRPr().getRFonts().getHAnsi(),
                  run.getCTR().getRPr().getSz() != null
                      ? run.getCTR().getRPr().getSz().getVal().intValue() / 2
                      : null,
                  run.isBold(),
                  run.isItalic()));
          currentPosition += runText.length();
        }
      }

      String paragraphText = fullText.toString();

      // 查找所有需要替换的占位符
      List<PlaceholderReplacement> replacements = findReplacements(paragraphText, placeholders);

      // 如果没有找到占位符，直接返回
      if (replacements.isEmpty()) {
        return;
      }

      // 按照位置排序，从前向后替换
      replacements.sort(Comparator.comparingInt(a -> a.startIndex));

      // 清除所有现有的runs
      while (!paragraph.getRuns().isEmpty()) {
        paragraph.removeRun(0);
      }

      // 处理每个替换
      applyReplacements(paragraph, paragraphText, replacements, runInfos);

    } catch (Exception e) {
      log.error("替换段落中的占位符时发生错误", e);
    }
  }

  private static List<PlaceholderReplacement> findReplacements(
      String text, Map<String, Object> placeholders) {
    List<PlaceholderReplacement> replacements = new ArrayList<>();
    for (Map.Entry<String, Object> entry : placeholders.entrySet()) {
      String placeholder = "{{" + entry.getKey() + "}}";
      int startIndex = text.indexOf(placeholder);
      if (startIndex >= 0) {
        Object value = entry.getValue();
        TextStyle style =
            value instanceof TextStyle
                ? (TextStyle) value
                : new TextStyle(value != null ? value.toString() : "");
        replacements.add(
            new PlaceholderReplacement(startIndex, startIndex + placeholder.length(), style));
      }
    }
    return replacements;
  }

  private static void applyReplacements(
      XWPFParagraph paragraph,
      String paragraphText,
      List<PlaceholderReplacement> replacements,
      List<RunInfo> runInfos) {
    int currentPos = 0;
    for (PlaceholderReplacement replacement : replacements) {
      if (currentPos < replacement.startIndex) {
        String beforeText = paragraphText.substring(currentPos, replacement.startIndex);
        if (!beforeText.isEmpty()) {
          addTextWithOriginalStyle(paragraph, beforeText, runInfos, currentPos);
        }
      }

      // 查找占位符所在的原始样式
      RunInfo originalStyle = null;
      for (RunInfo runInfo : runInfos) {
        if (replacement.startIndex >= runInfo.startIndex
            && replacement.startIndex < runInfo.endIndex) {
          originalStyle = runInfo;
          break;
        }
      }

      XWPFRun newRun = paragraph.createRun();
      applyStyle(newRun, replacement.style, originalStyle);

      currentPos = replacement.endIndex;
    }

    if (currentPos < paragraphText.length()) {
      String afterText = paragraphText.substring(currentPos);
      if (!afterText.isEmpty()) {
        addTextWithOriginalStyle(paragraph, afterText, runInfos, currentPos);
      }
    }
  }

  private static void addTextWithOriginalStyle(
      XWPFParagraph paragraph, String text, List<RunInfo> runInfos, int position) {
    XWPFRun newRun = paragraph.createRun();
    newRun.setText(text);

    // 查找对应的原始样式
    runInfos.stream()
        .filter(runInfo -> position >= runInfo.startIndex && position < runInfo.endIndex)
        .findFirst()
        .ifPresent(
            runInfo -> {
              // 复制颜色
              if (runInfo.color != null) {
                newRun.setColor(runInfo.color);
              }

              // 复制基本字体设置
              if (runInfo.fontFamily != null) {
                newRun.setFontFamily(runInfo.fontFamily);
              }

              // 复制详细字体设置
              CTRPr rpr =
                  newRun.getCTR().isSetRPr()
                      ? newRun.getCTR().getRPr()
                      : newRun.getCTR().addNewRPr();
              CTFonts fonts = rpr.isSetRFonts() ? rpr.getRFonts() : rpr.addNewRFonts();

              if (runInfo.eastAsiaFont != null) {
                fonts.setEastAsia(runInfo.eastAsiaFont);
              }
              if (runInfo.asciiFont != null) {
                fonts.setAscii(runInfo.asciiFont);
              }
              if (runInfo.hAnsiFont != null) {
                fonts.setHAnsi(runInfo.hAnsiFont);
              }

              // 复制字体大小
              if (runInfo.fontSize != null) {
                newRun.setFontSize(runInfo.fontSize);
              }

              // 复制加粗和斜体设置
              if (runInfo.isBold != null) {
                newRun.setBold(runInfo.isBold);
              }
              if (runInfo.isItalic != null) {
                newRun.setItalic(runInfo.isItalic);
              }
            });
  }

  /** 应用文本样式 */
  private static void applyStyle(XWPFRun run, TextStyle style, RunInfo originalStyle) {
    try {
      // 首先应用原始样式
      if (originalStyle != null) {
        // 应用字体
        if (originalStyle.fontFamily != null) {
          run.setFontFamily(originalStyle.fontFamily);
        }
        if (originalStyle.fontSize != null) {
          run.setFontSize(originalStyle.fontSize);
        }
        if (originalStyle.isBold != null) {
          run.setBold(originalStyle.isBold);
        }
        if (originalStyle.isItalic != null) {
          run.setItalic(originalStyle.isItalic);
        }

        // 应用字体设置
        CTRPr rpr = run.getCTR().isSetRPr() ? run.getCTR().getRPr() : run.getCTR().addNewRPr();
        CTFonts fonts = rpr.isSetRFonts() ? rpr.getRFonts() : rpr.addNewRFonts();

        if (originalStyle.eastAsiaFont != null) {
          fonts.setEastAsia(originalStyle.eastAsiaFont);
        }
        if (originalStyle.asciiFont != null) {
          fonts.setAscii(originalStyle.asciiFont);
        }
        if (originalStyle.hAnsiFont != null) {
          fonts.setHAnsi(originalStyle.hAnsiFont);
        }

        // 应用原始颜色（如果没有指定新的颜色）
        if (style.getColor() == null && originalStyle.color != null) {
          run.setColor(originalStyle.color);
        }
      }

      // 设置文本内容
      run.setText(style.getText(), 0);

      // 如果有超链接，创建超链接
      if (style.getHyperlink() != null) {
        applyHyperlinkStyle(run, style);
      } else if (style.getColor() != null) {
        // 只有在明确指定了新颜色时才覆盖
        run.setColor(style.getColor());
      }
    } catch (Exception e) {
      log.error("应用样式时发生错误", e);
      // 确保至少显示文本
      run.setText(style.getText(), 0);
    }
  }

  /** 应用超链接样式 */
  private static void applyHyperlinkStyle(XWPFRun run, TextStyle style) {
    try {
      XWPFParagraph paragraph = (XWPFParagraph) run.getParent();
      String url = style.getHyperlink();

      // 创建关系ID
      String id =
          paragraph
              .getDocument()
              .getPackagePart()
              .addExternalRelationship(url, XWPFRelation.HYPERLINK.getRelation())
              .getId();

      // 创建超链接
      CTP ctp = paragraph.getCTP();
      CTHyperlink hyperlink = ctp.addNewHyperlink();
      hyperlink.setId(id);

      // 只设置tooltip，不设置anchor
      hyperlink.setTooltip(url);

      // 创建文本运行
      CTR ctr = hyperlink.addNewR();

      // 设置文本
      CTText ctText = ctr.addNewT();
      ctText.setStringValue(style.getText());

      // 设置样式
      CTRPr rpr = ctr.addNewRPr();

      // 复制原始运行的样式
      CTRPr originalRpr = run.getCTR().getRPr();
      if (originalRpr != null) {
        rpr.set(originalRpr);
      }

      // 设置颜色（如果指定了新颜色）
      String color = style.getColor() != null ? style.getColor() : Colors.BLUE;
      CTColor ctColor = rpr.isSetColor() ? rpr.getColor() : rpr.addNewColor();
      ctColor.setVal(color);

      // 设置下划线
      rpr.addNewU().setVal(STUnderline.SINGLE);

      // 添加特殊标记以在PDF中保持超链接
      CTOnOff webHidden = rpr.addNewWebHidden();
      webHidden.setVal(STOnOff.Enum.forString("on"));

      // 移除原始运行
      paragraph.removeRun(paragraph.getRuns().indexOf(run));

    } catch (Exception e) {
      log.error("创建超链接时发生错误", e);
      // 如果创建超链接失败，至少显示文本
      run.setText(style.getText(), 0);
      if (style.getColor() != null) {
        run.setColor(style.getColor());
      }
    }
  }

  /**
   * 替换表格占位符
   *
   * @param document Word文档对象
   * @param tablePlaceholders 表格数据映射
   */
  private static void replaceTablePlaceholders(
      XWPFDocument document, Map<String, TableData> tablePlaceholders) {
    log.debug("开始替换表格占位符...");

    int pageWidth = getPageWidth(document);
    log.debug("获取到页面宽度: {} twips", pageWidth);

    List<XWPFParagraph> paragraphs = document.getParagraphs();
    for (int i = 0; i < paragraphs.size(); i++) {
      XWPFParagraph paragraph = paragraphs.get(i);
      String text = getTextFromParagraph(paragraph);

      if (text.isEmpty()) {
        continue;
      }

      for (Map.Entry<String, TableData> entry : tablePlaceholders.entrySet()) {
        String placeholderKey = "{{table:" + entry.getKey() + "}}";
        if (text.contains(placeholderKey)) {
          processTablePlaceholder(document, paragraph, entry.getValue(), pageWidth);
          break;
        }
      }
    }

    log.debug("表格占位符替换完成");
  }

  /** 处理表格占位符 */
  private static void processTablePlaceholder(
      XWPFDocument document, XWPFParagraph paragraph, TableData tableData, int pageWidth) {
    try {
      int pos = document.getPosOfParagraph(paragraph);
      if (pos < 0) {
        log.warn("无法确定段落位置，跳过处理");
        return;
      }

      XWPFParagraph breakParagraph = document.insertNewParagraph(paragraph.getCTP().newCursor());

      // 创建表格
      XWPFTable table = document.insertNewTbl(breakParagraph.getCTP().newCursor());
      initializeTableStructure(table, tableData, pageWidth);
      setTableStyle(table);
      fillTableData(table, tableData);
      clearParagraphContent(paragraph);
    } catch (Exception e) {
      log.error("处理表格占位符时出错", e);
      throw new RuntimeException("处理表格占位符失败", e);
    }
  }

  /**
   * 初始化表格结构，包括创建行列和设置基本尺寸
   *
   * @param table 表格对象
   * @param tableData 表格数据
   * @param pageWidth 页面宽度
   */
  private static void initializeTableStructure(
      XWPFTable table, TableData tableData, int pageWidth) {

    List<List<String>> data = tableData.getData();
    double[] columnWidthRatios = tableData.getColumnWidthRatios();

    // 计算表格基本尺寸
    int requiredRows = data.size();
    int requiredCols = data.get(0).size();
    int tableWidth = (int) (pageWidth * 0.9);

    // 创建所需的行和列
    while (table.getRows().size() < requiredRows) {
      table.createRow();
    }
    for (XWPFTableRow row : table.getRows()) {
      while (row.getTableCells().size() < requiredCols) {
        row.createCell();
      }
    }

    // 设置表格宽度和列宽
    table.setWidth(tableWidth);
    CTTblGrid grid = table.getCTTbl().addNewTblGrid();
    for (int i = 0; i < requiredCols; i++) {
      // 根据比例设置列宽
      int colWidth = (int) (tableWidth * columnWidthRatios[i]);
      grid.addNewGridCol().setW(BigInteger.valueOf(colWidth));
    }
  }

  /**
   * 设置表格样式
   *
   * @param table 表格对象
   */
  private static void setTableStyle(XWPFTable table) {
    // 设置表格内框
    table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
    table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");

    // 设置单元格边距
    table.setCellMargins(100, 100, 100, 100);

    // 设置表格布局为固定布局
    CTTbl ctTbl = table.getCTTbl();
    CTTblPr tblPr = ctTbl.getTblPr() != null ? ctTbl.getTblPr() : ctTbl.addNewTblPr();
    CTTblLayoutType tblLayout =
        tblPr.getTblLayout() != null ? tblPr.getTblLayout() : tblPr.addNewTblLayout();
    tblLayout.setType(STTblLayoutType.FIXED);

    // 设置行样式
    for (XWPFTableRow row : table.getRows()) {
      // 设置行高为自动
      CTRow ctRow = row.getCtRow();
      CTTrPr trPr = ctRow.isSetTrPr() ? ctRow.getTrPr() : ctRow.addNewTrPr();
      CTHeight height = trPr.addNewTrHeight();
      height.setHRule(STHeightRule.AUTO);

      // 设置单元格样式
      for (XWPFTableCell cell : row.getTableCells()) {
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

        // 设置单元格自动换行
        CTTc ctTc = cell.getCTTc();
        CTTcPr tcPr = ctTc.isSetTcPr() ? ctTc.getTcPr() : ctTc.addNewTcPr();
        tcPr.addNewNoWrap().setVal(STOnOff.OFF);

        // 设置段落样式
        for (XWPFParagraph cellParagraph : cell.getParagraphs()) {
          cellParagraph.setAlignment(ParagraphAlignment.CENTER);
          // 设置段落自动换行
          CTP ctp = cellParagraph.getCTP();
          CTPPr pPr = ctp.isSetPPr() ? ctp.getPPr() : ctp.addNewPPr();
          pPr.addNewWordWrap().setVal(STOnOff.ON);
        }
      }
    }
  }

  /**
   * 填充表格数据并设置单元格内容样式
   *
   * @param table 表格对象
   * @param tableData 表格数据
   */
  private static void fillTableData(XWPFTable table, TableData tableData) {
    List<List<String>> data = tableData.getData();
    for (int rowIdx = 0; rowIdx < data.size(); rowIdx++) {
      XWPFTableRow row = table.getRow(rowIdx);
      List<String> rowData = data.get(rowIdx);

      for (int colIdx = 0; colIdx < rowData.size(); colIdx++) {
        XWPFTableCell cell = row.getCell(colIdx);
        setCellContent(cell, rowData.get(colIdx), rowIdx == 0);
      }
    }
  }

  /**
   * 设置单元格内容和样式
   *
   * @param cell 单元格对象
   * @param content 单元格内容
   * @param isHeader 是否为表头单元格
   */
  private static void setCellContent(XWPFTableCell cell, String content, boolean isHeader) {
    // 获取或创建单元格段落
    XWPFParagraph cellParagraph =
        cell.getParagraphs().isEmpty() ? cell.addParagraph() : cell.getParagraphs().get(0);

    // 清除现有内容
    while (!cellParagraph.getRuns().isEmpty()) {
      cellParagraph.removeRun(0);
    }

    // 创建新的文本运行并设置内容
    XWPFRun run = cellParagraph.createRun();
    run.setText(content);
    run.setFontSize(10);
    run.setFontFamily("宋体");

    // 设置中文字体,解决中文乱码
    CTRPr rpr = run.getCTR().isSetRPr() ? run.getCTR().getRPr() : run.getCTR().addNewRPr();
    CTFonts fonts = rpr.isSetRFonts() ? rpr.getRFonts() : rpr.addNewRFonts();
    fonts.setEastAsia("宋体");
    fonts.setAscii("宋体");
    fonts.setHAnsi("宋体");

    cellParagraph.setAlignment(ParagraphAlignment.CENTER);

    CTP ctp = cellParagraph.getCTP();
    CTPPr pPr = ctp.isSetPPr() ? ctp.getPPr() : ctp.addNewPPr();
    pPr.addNewWordWrap().setVal(STOnOff.ON);

    // 设置单元格自动换行
    CTTc ctTc = cell.getCTTc();
    CTTcPr tcPr = ctTc.isSetTcPr() ? ctTc.getTcPr() : ctTc.addNewTcPr();
    tcPr.addNewNoWrap().setVal(STOnOff.OFF);

    if (isHeader) {
      run.setBold(true);
      cell.setColor(Colors.LIGHT_GRAY);
    }
  }

  /** 替换图表占位符 */
  private static void replaceChartPlaceholders(
      XWPFDocument document, Map<String, ChartData> chartPlaceholders) {
    log.debug("开始替换图表占位符...");

    List<XWPFParagraph> paragraphs = document.getParagraphs();
    for (int i = 0; i < paragraphs.size(); i++) {
      XWPFParagraph paragraph = paragraphs.get(i);
      String text = getTextFromParagraph(paragraph);

      if (text.isEmpty()) {
        continue;
      }

      for (Map.Entry<String, ChartData> entry : chartPlaceholders.entrySet()) {
        String placeholderKey = "{{chart:" + entry.getKey() + "}}";
        if (text.contains(placeholderKey)) {
          processChartPlaceholder(document, paragraph, entry.getValue());
        }
      }
    }

    log.debug("图表占位符替换完成");
  }

  /** 生成图表图片 */
  private static byte[] generateChartImageBytes(ChartData chartData) throws IOException {
    log.debug("生成图表, 类型: {}", chartData.getType());

    // 使用固定的高分辨率尺寸来生成图表
    final int HIGH_RES_WIDTH = 1200; // 固定的图表生成宽度
    final int HIGH_RES_HEIGHT = 800; // 固定的图表生成高度

    JFreeChart chart;
    switch (chartData.getType()) {
      case BAR_CHART:
        chart = createBarChart(chartData);
        break;
      case LINE_CHART:
        chart = createLineChart(chartData);
        break;
      case PIE_CHART:
        chart = createPieChart(chartData);
        break;
      default:
        throw new IllegalArgumentException("不支持的图表类型: " + chartData.getType());
    }

    // 将图表转换为字节数组，使用PNG格式以保持高质量
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    ChartUtils.writeChartAsPNG(baos, chart, HIGH_RES_WIDTH, HIGH_RES_HEIGHT);
    return baos.toByteArray();
  }

  /** 创建柱状图 */
  private static JFreeChart createBarChart(ChartData chartData) {
    DefaultCategoryDataset dataset = new DefaultCategoryDataset();

    // 添加数据
    for (Map.Entry<String, Map<String, Double>> entry : chartData.getCategoryData().entrySet()) {
      String seriesName = entry.getKey();
      Map<String, Double> values = entry.getValue();
      for (Map.Entry<String, Double> valueEntry : values.entrySet()) {
        dataset.addValue(valueEntry.getValue(), seriesName, valueEntry.getKey());
      }
    }

    // 创建图表
    JFreeChart chart =
        ChartFactory.createBarChart(
            chartData.getTitle(),
            chartData.getCategoryLabel(),
            chartData.getValueLabel(),
            dataset,
            PlotOrientation.VERTICAL,
            true,
            true,
            false);

    // 设置图表样式
    customizeBarChart(chart);

    return chart;
  }

  /** 创建折线图 */
  private static JFreeChart createLineChart(ChartData chartData) {
    DefaultCategoryDataset dataset = new DefaultCategoryDataset();

    // 添加数据
    for (Map.Entry<String, Map<String, Double>> entry : chartData.getCategoryData().entrySet()) {
      String seriesName = entry.getKey();
      Map<String, Double> values = entry.getValue();
      for (Map.Entry<String, Double> valueEntry : values.entrySet()) {
        dataset.addValue(valueEntry.getValue(), seriesName, valueEntry.getKey());
      }
    }

    // 创建图表
    JFreeChart chart =
        ChartFactory.createLineChart(
            chartData.getTitle(),
            chartData.getCategoryLabel(),
            chartData.getValueLabel(),
            dataset,
            PlotOrientation.VERTICAL,
            true,
            true,
            false);

    // 设置图表样式
    customizeLineChart(chart);

    return chart;
  }

  /** 创建饼图 */
  private static JFreeChart createPieChart(ChartData chartData) {
    DefaultPieDataset<String> dataset = new DefaultPieDataset<>();

    // 饼图只使用第一个系列的数据
    if (!chartData.getCategoryData().isEmpty()) {
      Map<String, Double> firstSeries = chartData.getCategoryData().values().iterator().next();

      // 将数据转换为列表并按值降序排序
      List<Map.Entry<String, Double>> sortedEntries = new ArrayList<>(firstSeries.entrySet());
      sortedEntries.sort((e1, e2) -> Double.compare(e2.getValue(), e1.getValue()));

      // 按排序后的顺序添加数据
      for (Map.Entry<String, Double> entry : sortedEntries) {
        dataset.setValue(entry.getKey(), entry.getValue());
      }
    }

    // 创建图表
    JFreeChart chart =
        ChartFactory.createPieChart(chartData.getTitle(), dataset, true, true, false);

    // 设置图表样式
    customizePieChart(chart);

    return chart;
  }

  /** 自定义柱状图样式 */
  private static void customizeBarChart(JFreeChart chart) {
    // 设置图表背景
    chart.setBackgroundPaint(Color.WHITE);

    // 设置标题字体
    chart.getTitle().setFont(DEFAULT_FONT.deriveFont(Font.BOLD, DEFAULT_FONT_SIZE + 10));

    // 获取绘图区域
    CategoryPlot plot = chart.getCategoryPlot();
    plot.setBackgroundPaint(Color.WHITE);
    plot.setDomainGridlinePaint(Color.LIGHT_GRAY);
    plot.setRangeGridlinePaint(Color.LIGHT_GRAY);
    plot.setOutlineVisible(false);

    // 设置柱状图渲染器
    BarRenderer renderer = (BarRenderer) plot.getRenderer();
    renderer.setDefaultItemLabelFont(DEFAULT_FONT.deriveFont(Font.PLAIN, DEFAULT_FONT_SIZE));
    renderer.setDefaultItemLabelsVisible(true);
    BarRenderer.setDefaultShadowsVisible(false);
    renderer.setDrawBarOutline(false);
    renderer.setShadowVisible(false);

    // 设置纯色显示，去除渐变和立体效果
    renderer.setBarPainter(new StandardBarPainter());

    // 自动设置系列颜色
    int seriesCount = plot.getDataset().getRowCount();
    for (int i = 0; i < seriesCount; i++) {
      renderer.setSeriesPaint(i, Colors.getChartColor(i));
    }

    // 设置数值标签
    renderer.setDefaultItemLabelsVisible(true);
    renderer.setDefaultPositiveItemLabelPosition(
        new org.jfree.chart.labels.ItemLabelPosition(
            org.jfree.chart.labels.ItemLabelAnchor.OUTSIDE12,
            org.jfree.chart.ui.TextAnchor.BOTTOM_CENTER));

    // 设置数值标签的格式和字体
    renderer.setDefaultItemLabelGenerator(
        new org.jfree.chart.labels.StandardCategoryItemLabelGenerator());
    renderer.setDefaultItemLabelFont(DEFAULT_FONT.deriveFont(Font.PLAIN, DEFAULT_FONT_SIZE));
    renderer.setDefaultItemLabelPaint(Color.BLACK);

    // 设置轴标签字体
    CategoryAxis domainAxis = plot.getDomainAxis();
    domainAxis.setLabelFont(DEFAULT_FONT.deriveFont(Font.BOLD, DEFAULT_FONT_SIZE));
    domainAxis.setTickLabelFont(DEFAULT_FONT.deriveFont(Font.PLAIN, DEFAULT_FONT_SIZE));

    NumberAxis rangeAxis = (NumberAxis) plot.getRangeAxis();
    rangeAxis.setLabelFont(DEFAULT_FONT.deriveFont(Font.BOLD, DEFAULT_FONT_SIZE));
    rangeAxis.setTickLabelFont(DEFAULT_FONT.deriveFont(Font.PLAIN, DEFAULT_FONT_SIZE));

    // 设置图例字体
    chart.getLegend().setItemFont(DEFAULT_FONT.deriveFont(Font.PLAIN, DEFAULT_FONT_SIZE));

    // 设置边距
    chart.setPadding(new RectangleInsets(10, 10, 10, 10));
  }

  /** 自定义折线图样式 */
  private static void customizeLineChart(JFreeChart chart) {
    // 设置图表背景
    chart.setBackgroundPaint(Color.WHITE);

    // 设置标题字体
    chart.getTitle().setFont(DEFAULT_FONT.deriveFont(Font.BOLD, DEFAULT_FONT_SIZE + 10));

    // 获取绘图区域
    CategoryPlot plot = chart.getCategoryPlot();
    plot.setBackgroundPaint(Color.WHITE);
    plot.setDomainGridlinePaint(Color.LIGHT_GRAY);
    plot.setRangeGridlinePaint(Color.LIGHT_GRAY);
    plot.setOutlineVisible(false);

    // 设置折线图渲染器
    LineAndShapeRenderer renderer = (LineAndShapeRenderer) plot.getRenderer();
    renderer.setDefaultShapesVisible(true);
    renderer.setDefaultLinesVisible(true);
    renderer.setDefaultItemLabelFont(DEFAULT_FONT.deriveFont(Font.PLAIN, DEFAULT_FONT_SIZE));
    renderer.setDefaultItemLabelsVisible(true);

    // 设置数值标签
    renderer.setDefaultItemLabelsVisible(true);
    renderer.setDefaultPositiveItemLabelPosition(
        new org.jfree.chart.labels.ItemLabelPosition(
            org.jfree.chart.labels.ItemLabelAnchor.OUTSIDE12,
            org.jfree.chart.ui.TextAnchor.BOTTOM_CENTER));

    // 设置数值标签的格式和字体
    renderer.setDefaultItemLabelGenerator(
        new org.jfree.chart.labels.StandardCategoryItemLabelGenerator());
    renderer.setDefaultItemLabelFont(DEFAULT_FONT.deriveFont(Font.PLAIN, DEFAULT_FONT_SIZE));
    renderer.setDefaultItemLabelPaint(Color.BLACK);

    // 设置轴标签字体
    CategoryAxis domainAxis = plot.getDomainAxis();
    domainAxis.setLabelFont(DEFAULT_FONT.deriveFont(Font.BOLD, DEFAULT_FONT_SIZE));
    domainAxis.setTickLabelFont(DEFAULT_FONT.deriveFont(Font.PLAIN, DEFAULT_FONT_SIZE));

    NumberAxis rangeAxis = (NumberAxis) plot.getRangeAxis();
    rangeAxis.setLabelFont(DEFAULT_FONT.deriveFont(Font.BOLD, DEFAULT_FONT_SIZE));
    rangeAxis.setTickLabelFont(DEFAULT_FONT.deriveFont(Font.PLAIN, DEFAULT_FONT_SIZE));

    // 设置图例字体
    chart.getLegend().setItemFont(DEFAULT_FONT.deriveFont(Font.PLAIN, DEFAULT_FONT_SIZE));

    // 设置边距
    chart.setPadding(new RectangleInsets(10, 10, 10, 10));
  }

  /** 自定义饼图样式 */
  private static void customizePieChart(JFreeChart chart) {
    // 设置图表背景
    chart.setBackgroundPaint(Color.WHITE);

    // 设置标题字体
    chart.getTitle().setFont(DEFAULT_FONT.deriveFont(Font.BOLD, DEFAULT_FONT_SIZE + 10));

    // 获取绘图区域
    PiePlot<?> plot = (PiePlot<?>) chart.getPlot();
    plot.setBackgroundPaint(Color.WHITE);
    plot.setOutlineVisible(false);
    plot.setLabelFont(DEFAULT_FONT.deriveFont(Font.PLAIN, DEFAULT_FONT_SIZE));
    plot.setLabelBackgroundPaint(new Color(255, 255, 255, 200));
    plot.setLabelOutlinePaint(null);
    plot.setLabelShadowPaint(null);
    plot.setShadowPaint(null);

    // 设置标签生成器
    PieSectionLabelGenerator labelGenerator =
        new StandardPieSectionLabelGenerator(
            "{0}: {1} ({2})", new DecimalFormat("0.##"), new DecimalFormat("0.00%"));
    plot.setLabelGenerator(labelGenerator);

    // 设置图例字体
    chart.getLegend().setItemFont(DEFAULT_FONT.deriveFont(Font.PLAIN, DEFAULT_FONT_SIZE));

    // 设置边距
    chart.setPadding(new RectangleInsets(10, 10, 10, 10));

    // 设置起始角度和方向
    plot.setStartAngle(90);
    plot.setDirection(Rotation.CLOCKWISE);
  }

  /** 处理图表占位符 */
  private static void processChartPlaceholder(
      XWPFDocument document, XWPFParagraph paragraph, ChartData chartData) {
    try {
      // 生成图表图片
      byte[] chartImageBytes = generateChartImageBytes(chartData);

      // 清空段落内容
      clearParagraphContent(paragraph);

      // 设置段落属性
      paragraph.setAlignment(ParagraphAlignment.CENTER);
      paragraph.setSpacingBefore(0);
      paragraph.setSpacingAfter(0);

      // 创建run并设置基本属性
      XWPFRun run = paragraph.createRun();
      run.setText("");
      run.setTextPosition(0);

      // 使用ChartData中的宽高来控制Word中的图片尺寸
      int widthEMU = (int) (chartData.getWidth() * 914400 / 96.0); // 将像素转换为EMU（96 DPI）
      int heightEMU = (int) (chartData.getHeight() * 914400 / 96.0);

      // 获取页面宽度
      double pageWidthInches = 8.27;
      double pageWidthEMUs = pageWidthInches * 914400;
      double maxWidthEMUs = pageWidthEMUs * 0.85;

      // 如果图片宽度超过最大宽度，按比例缩放
      if (widthEMU > maxWidthEMUs) {
        double scale = maxWidthEMUs / widthEMU;
        widthEMU = (int) maxWidthEMUs;
        heightEMU = (int) (heightEMU * scale);
      }

      // 添加图片
      ByteArrayInputStream imageStream = new ByteArrayInputStream(chartImageBytes);
      run.addPicture(imageStream, XWPFDocument.PICTURE_TYPE_PNG, "chart.png", widthEMU, heightEMU);
      imageStream.close();

      // 设置图片属性
      run.getCTR().addNewRPr().addNewNoProof();

      // 在当前段落后添加一个空行
      XWPFParagraph newParagraph = document.insertNewParagraph(paragraph.getCTP().newCursor());
      newParagraph.setSpacingBefore(0);
      newParagraph.setSpacingAfter(0);
      newParagraph.setAlignment(ParagraphAlignment.CENTER);
    } catch (Exception e) {
      log.error("处理图表占位符时出错", e);
      throw new RuntimeException("处理图表占位符失败", e);
    }
  }

  /** 安全地清空段落内容 */
  private static void clearParagraphContent(XWPFParagraph paragraph) {
    try {
      int size = paragraph.getRuns().size();
      for (int i = size - 1; i >= 0; i--) {
        paragraph.removeRun(i);
      }
    } catch (Exception e) {
      log.warn("清空段落内容时出错", e);
    }
  }

  /** 获取页面宽度（以twips为单位） */
  private static int getPageWidth(XWPFDocument document) {
    try {
      CTSectPr sectPr = document.getDocument().getBody().getSectPr();
      if (sectPr != null) {
        CTPageSz pageSize = sectPr.getPgSz();
        if (pageSize != null) {
          return pageSize.getW().intValue();
        }
      }
      return 11906; // 默认A4纸宽度
    } catch (Exception e) {
      log.warn("获取页面宽度失败，使用默认值", e);
      return 11906;
    }
  }

  /** 安全地获取段落文本 */
  private static String getTextFromParagraph(XWPFParagraph paragraph) {
    try {
      StringBuilder text = new StringBuilder();
      List<XWPFRun> runs = paragraph.getRuns();
      if (runs != null) {
        for (XWPFRun run : runs) {
          String runText = run.getText(0);
          if (runText != null) {
            text.append(runText);
          }
        }
      }
      return text.toString();
    } catch (Exception e) {
      log.warn("获取段落文本时出错", e);
      return "";
    }
  }
}
