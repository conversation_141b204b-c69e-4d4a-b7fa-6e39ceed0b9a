package com.qbook.insight.handler.service.impl;

import com.qbook.insight.common.domain.R;
import com.qbook.insight.common.entity.BaseEntity;
import com.qbook.insight.common.entity.Column;
import com.qbook.insight.common.entity.Table;
import com.qbook.insight.common.mapper.ColumnMapper;
import com.qbook.insight.common.mapper.TableMapper;
import com.qbook.insight.common.util.GenUtil;
import com.qbook.insight.common.util.StringUtils;
import com.qbook.insight.common.vo.CodeGenParam;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.service.GenService;
import java.io.StringWriter;
import java.util.*;
import javax.annotation.Resource;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.springframework.stereotype.Service;

/**
 * 代码生成接口实现
 *
 * <AUTHOR>
 */
@Service
public class GenServiceImpl implements GenService {

  // 模板列表
  public static final List<String> templates =
      Arrays.asList(
          "vm/backend/entity.java.vm",
          "vm/backend/mapper.java.vm",
          "vm/backend/mapper.xml.vm",
          "vm/backend/controller.java.vm",
          "vm/backend/service.java.vm");

  @Resource private TableMapper tableMapper;
  @Resource private ColumnMapper columnMapper;

  public GenServiceImpl() {
    // 初始化渲染引擎
    Properties p = new Properties();
    p.setProperty(
        "resource.loader.file.class",
        "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
    p.setProperty(Velocity.INPUT_ENCODING, "UTF-8");
    Velocity.init(p);
  }

  @Override
  public List<Table> getTables() {
    return tableMapper.selectList();
  }

  @Override
  public List<Column> getColumnsByTableName(String tableName) {
    return columnMapper.getColumnsByTableName(tableName);
  }

  @Override
  public Map<String, String> codePreview(CodeGenParam param) {
    List<Column> columns = getColumnsByTableName(param.getTableName());
    GenUtil.organizeColumns(columns);

    VelocityContext context = new VelocityContext();
    String baseEntityPackage = BaseEntity.class.getPackage().getName();
    String baseEntityPath = baseEntityPackage.substring(0, baseEntityPackage.lastIndexOf("."));
    context.put("baseEntityPath", baseEntityPath);
    context.put("packagePath", param.getPackagePath());
    context.put("tableName", param.getTableName());
    context.put("author", param.getAuthor());
    context.put("className", param.getClassName());
    context.put("classChineseName", param.getClassChineseName());
    context.put("columns", columns);
    context.put("rClassName", R.class.getName());
    context.put("pageParamClassName", PageParam.class.getName());
    context.put("classInstanceName", StringUtils.toCamelCase(param.getTableName()));

    Map<String, String> result = new HashMap<>();
    for (String template : templates) {
      StringWriter sw = new StringWriter();
      Template tpl = Velocity.getTemplate(template, "UTF-8");
      tpl.merge(context, sw);
      result.put(template, sw.toString());
    }
    return result;
  }
}
