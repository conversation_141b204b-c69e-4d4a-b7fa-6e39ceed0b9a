package com.qbook.insight.handler.controller;

import com.qbook.insight.common.domain.R;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.entity.MemberOrder;
import com.qbook.insight.handler.service.MemberOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 订单相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "Member Order Operate", description = "订单操作")
@RestController
@RequestMapping("/member/order")
public class MemberOrderController {

  @Resource private MemberOrderService memberOrderService;

  @ApiOperation(value = "创建支付订单")
  @PostMapping("/create")
  public R add(@RequestBody MemberOrder order) {
    return R.ok(memberOrderService.add(order));
  }

  @ApiOperation(value = "根据订单号获取订单详情")
  @GetMapping("/check/{orderId}")
  public R checkByOrderId(@PathVariable String orderId) {
    return R.ok(memberOrderService.checkByOrderId(orderId));
  }

  @ApiOperation(value = "获取支付订单详情")
  @GetMapping("/{id}")
  public R getById(@PathVariable long id) {
    return R.ok(memberOrderService.getById(id));
  }

  @ApiOperation(value = "获取支付订单列表(分页)")
  @GetMapping("/page")
  public R page(
      PageParam pageParam,
      @ApiParam("订单状态") @RequestParam(required = false) String status,
      @ApiParam("支付方式") @RequestParam(required = false) String paymentMethod) {
    return R.ok(memberOrderService.pageVO(pageParam, status, paymentMethod));
  }
}
