package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 政策享受分析
 *
 * <AUTHOR>
 */
@Data
public class PolicyBenefits {

  @ApiModelProperty("行业补贴")
  private String industrySubsidy;

  @ApiModelProperty("人才补贴")
  private String talentSubsidy;

  @ApiModelProperty("房租补贴")
  private String rentSubsidy;

  @ApiModelProperty("机器补贴")
  private String equipmentSubsidy;

  @ApiModelProperty("上市补贴")
  private String ipoSubsidy;

  @ApiModelProperty("其他补助")
  private String otherSubsidies;
}
