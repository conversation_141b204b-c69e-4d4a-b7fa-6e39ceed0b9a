# <center>第三方平台对接文档
## <center>(浙江快书)

## 版本

| 日期         | 版本号 | 作者     | 备注 |
|------------|-----|--------|----|
| 2025.07.21 | 1.0 | fangxm | 初版 |


<br><br><br>浙江快书开放平台基于HTTP协议对外提供服务能力。第三方平台使用符合 RESTful 规范的 HTTP 请求进行调用。

## 1. 接口通用要求

## 1.1 公共请求参数

| 字段名       | 字段类型   | 是否必填  | 描述                             |
|:----------|--------|:-----:|--------------------------------|
| appkey    | String |   Y   | 客户端身份标识 (由接口提供方提供)             |
| timestamp | Long   |   Y   | 时间戳 (Unix 10位秒时间戳，最大误差不超过30分钟) |
| nonce     | String |   Y   | 随机字符串 (6字节)                    |
| sign      | String |   Y   | 签名值 (签名方法见下文)                  |

## 1.2 接口签名

为了防止API调用过程中被恶意篡改，调用任何一个API都需要携带签名。平台给每一个第三方提供独立的 `appkey` 和 `secret`。其中 `appkey` 用于作为接口参数，`secret` 用于接口签名。服务端会根据请求参数，对签名进行验证，签名不合法的请求将会被拒绝。目前，服务端采用 `HMAC_SHA256` 签名算法。签名过程如下：

* 对所有API请求参数（包括公共参数和业务参数，但除去sign参数和byte[]类型的参数），根据参数名称的ASCII码表的顺序排序，将其对应的值进行拼接。
* 把所有参数名和参数值串在一起。
* 使用HMAC进行编码。
* 把二进制转化为大写的十六进制。

示例参数名和值如下：
  * appkey=12345678
  * timestamp=1753101937
  * nonce=342677

则对参数名进行排序后，参数名和参数值组成的字符串为："appkey12345678nonce342677timestamp1753101937"。假设用于签名的密钥为 “abcdefghijklmnopqrst”，则对应的签名字段 `sign` 的值为 "AE4E7DD0C8589AF49748A4A9B6694935"。

Java 签名示例代码如下：

```java
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public static String signParams(Map<String, String> params, String secret) throws Exception {
  // 第一步：参数排序
  String[] keys = params.keySet().toArray(new String[0]);
  Arrays.sort(keys);

  // 第二步：把所有参数名和参数值串在一起
  StringBuilder query = new StringBuilder();
  for (String key : keys) {
    String value = params.get(key);
    if (key != null && value != null) {
      query.append(key).append(value);
    }
  }

  // 第三步：使用HMAC进行编码
  byte[] bytes = encodeHMAC(query.toString(), secret);

  // 第四步：把二进制转化为大写的十六进制
  return byte2hex(bytes);
}

public static byte[] encodeHMAC(String data, String secret) throws Exception {
  SecretKey secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacMD5");
  Mac mac = Mac.getInstance(secretKey.getAlgorithm());
  mac.init(secretKey);
  return mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
}

public static String byte2hex(byte[] bytes) {
  StringBuilder sign = new StringBuilder();
  for (byte b : bytes) {
    String hex = Integer.toHexString(b & 0xFF);
    if (hex.length() == 1) {
      sign.append("0");
    }
    sign.append(hex.toUpperCase());
  }
  return sign.toString();
}
```

## 1.3 公共响应参数

平台响应包采用 JSON 格式，包含的字段如下：

| 字段名     | 字段类型   |  是否必填  | 描述   |
|:--------|--------|:------:|------|
| code    | Int    |   Y    | 错误码  |
| message | String |   Y    | 错误信息 |
| data    | String |   N    | 数据   |

## 1.4 基础错误码

| 错误码 | 说明    |
|:----|-------|
| 200 | 正常    |
| 400 | 参数错误  |
| 401 | 没有权限  |
| 500 | 服务端错误 |

## 1.5 调用地址

* 正式环境：https://fk.quickbook.com.cn


## 2. 接口定义

### 2.1 账套数据上传

* 接口地址：/api/open/account_book/v1/upload
* 请求方法：POST
* 请求参数:

  | 字段名     | 字段类型   | 是否必填 | 描述   |
  |:--------|:-------|:----:|------|
  | datasrc | String |  Y   | 数据来源 |
  | taxid   | String |  Y   | 税号   |
  | year    | String |  Y   | 年度   |
 * Body（application/json）:

  | 字段名                | 字段类型                | 是否必填 | 描述           |
  |:-------------------|:--------------------|:----:|--------------|
  | accountLedger      | AccountLedger       |  Y   | 电子账簿         |
  | accountingSubjects | AccountingSubject[] |  Y   | 会计科目（列表）     |
  | departmentInfos    | DepartmentInfo[]    |  N   | 部门（列表）       |
  | employeeInfos      | EmployeeInfo[]      |  N   | 人员（列表）       |
  | businessUnits      | BusinessUnits[]     |  Y   | 往来单位（列表）     |
  | projectInfos       | ProjectInfo[]       |  N   | 项目（列表）       |
  | inventoryInfos     | InventoryInfo[]     |  Y   | 存货（列表）       |
  | subjectBalances    | SubjectBalance[]    |  Y   | 科目余额及发生额（列表） |
  | accountingVouchers | AccountingVoucher[] |  Y   | 记账凭证（列表）     |

  其中，`电子账簿` (AccountLedger) 包含以下字段：
  
  | 字段名              | 字段类型   | 是否必填 | 描述     |
  |:-----------------|:-------|:----:|--------|
  | ledgerNo         | String |  Y   | 电子账簿编号 |
  | ledgerName       | String |  Y   | 电子账簿名称 |
  | accountingUnit   | String |  Y   | 会计核算单位 |
  | organizationCode | String |  Y   | 组织机构代码 |
  | unitNature       | String |  Y   | 单位性质   |
  | industry         | String |  Y   | 行业     |
  | developer        | String |  Y   | 开发单位   |
  | version          | String |  Y   | 版本号    |
  | year             | String |  Y   | 年度     |
  | baseCurrency     | String |  Y   | 本位币    |
  | accountStructure | String |  Y   | 科目结构   |

  `会计科目` (AccountingSubject) 包含以下字段：

  | 字段名                     | 字段类型    | 是否必填 | 描述     |
  |:------------------------|:--------|:----:|--------|
  | subjectKey              | String  |  Y   | 科目编号   |
  | subjectName             | String  |  Y   | 科目名称   |
  | subjectLevel            | Integer |  Y   | 科目级次   |
  | auxiliaryAccountingFlag | Integer |  Y   | 辅助核算标志 |
  | auxiliaryAccountingItem | String  |  Y   | 辅助核算项  |
  | subjectType             | String  |  Y   | 科目类型   |
  | unitOfMeasure           | String  |  Y   | 计量单位   |
  | balanceDirection        | String  |  Y   | 余额方向   |

  `部门` (DepartmentInfo) 包含以下字段：
  
  | 字段名            | 字段类型   | 是否必填 | 描述   |
  |:---------------|:-------|:----:|------|
  | departmentCode | String |  Y   | 部门编号 |
  | departmentName | String |  Y   | 部门名称 |

  `人员` (EmployeeInfo) 包含以下字段：

  | 字段名          | 字段类型   | 是否必填 | 描述   |
  |:-------------|:-------|:----:|------|
  | employeeCode | String |  Y   | 人员编号 |
  | employeeName | String |  Y   | 人员名称 |
  | department   | String |  Y   | 所属部门 |

  `往来单位` (BusinessUnits) 包含以下字段：

  | 字段名          | 字段类型   | 是否必填 | 描述   |
  |:-------------|:-------|:----:|------|
  | businessCode | String |  Y   | 单位编号 |
  | businessName | String |  Y   | 单位名称 |
  | category     | String |  Y   | 类别   |

  `项目` (ProjectInfo) 包含以下字段：

  | 字段名          | 字段类型   | 是否必填 | 描述   |
  |:-------------|:-------|:----:|------|
  | projectCode  | String |  Y   | 项目编号 |
  | projectName  | String |  Y   | 项目名称 |
  | category     | String |  Y   | 项目分类 |
  | mainCategory | String |  Y   | 项目大类 |

  `存货` (InventoryInfo) 包含以下字段：

  | 字段名               | 字段类型   | 是否必填 | 描述   |
  |:------------------|:-------|:----:|------|
  | inventoryCode     | String |  Y   | 存货编号 |
  | inventoryName     | String |  Y   | 存货名称 |
  | specification     | String |  Y   | 规格型号 |
  | unitOfMeasurement | String |  Y   | 计量单位 |

  `科目余额及发生额` (SubjectBalance) 包含以下字段：

  | 字段名                           | 字段类型    | 是否必填 | 描述      |
  |:------------------------------|:--------|:----:|---------|
  | subjectKey                    | String  |  Y   | 科目编号    |
  | currency                      | String  |  Y   | 币种      |
  | auxiliaryAccountingGroup      | String  |  Y   | 辅助核算组   |
  | initialBalance                | String  |  Y   | 期初余额    |
  | initialQuantity               | String  |  Y   | 期初数量    |
  | initialForeignCurrencyBalance | String  |  Y   | 期初外币余额  |
  | debitAmount                   | String  |  Y   | 借方发生额   |
  | debitQuantity                 | String  |  Y   | 借方发生数量  |
  | debitForeignCurrencyAmount    | String  |  Y   | 借方外币发生额 |
  | creditAmount                  | String  |  Y   | 贷方发生额   |
  | creditQuantity                | String  |  Y   | 贷方发生数量  |
  | creditForeignCurrencyAmount   | String  |  Y   | 贷方外币发生额 |
  | endingBalance                 | String  |  Y   | 期末余额    |
  | endingQuantity                | String  |  Y   | 期末数量    |
  | endingForeignCurrencyBalance  | String  |  Y   | 期末外币余额  |
  | accountingMonth               | Integer |  Y   | 会计月度    |

  `记账凭证` (AccountingVoucher) 包含以下字段：

  | 字段名                         | 字段类型    | 是否必填 | 描述     |
  |:----------------------------|:--------|:----:|--------|
  | voucherDate                 | String  |  Y   | 凭证日期   |
  | voucherType                 | String  |  Y   | 凭证种类   |
  | voucherNumber               | String  |  Y   | 凭证编号   |
  | lineNumber                  | Integer |  Y   | 行号     |
  | summary                     | String  |  Y   | 摘要     |
  | subjectKey                  | String  |  Y   | 科目编号   |
  | debitAmount                 | String  |  Y   | 借方金额   |
  | creditAmount                | String  |  Y   | 贷方金额   |
  | currency                    | String  |  Y   | 币种     |
  | debitForeignCurrencyAmount  | String  |  Y   | 借方外币金额 |
  | creditForeignCurrencyAmount | String  |  Y   | 贷方外币金额 |
  | exchangeRate                | String  |  Y   | 汇率     |
  | quantity                    | String  |  Y   | 数量     |
  | unitPrice                   | String  |  Y   | 单价     |
  | auxiliaryAccountingGroup    | String  |  Y   | 辅助核算组  |
  | settlementMethod            | String  |  Y   | 结算方式   |
  | billType                    | String  |  Y   | 票据类型   |
  | billNumber                  | String  |  Y   | 票据号    |
  | billDate                    | String  |  Y   | 票据日期   |
  | attachmentNumber            | Integer |  Y   | 附件数    |
  | creator                     | String  |  Y   | 制单人员   |
  | reviewer                    | String  |  Y   | 审核人员   |
  | accountants                 | String  |  Y   | 记账人员   |
  | cashier                     | String  |  Y   | 出纳人员   |
  | accountingFlag              | String  |  Y   | 记账标志   |

  * 示例数据：

```json
{
  "accountLedger": {
    "ledgerNo": "93565",
    "ledgerName": "xxxx有限公司",
    "accountingUnit": "xxxx有限公司",
    "organizationCode": "**********",
    "unitNature": "企业单位",
    "industry": "小企业会计准则",
    "developer": "亿企赢网络科技有限公司",
    "version": "4.0",
    "year": "2025",
    "baseCurrency": "CNY",
    "accountStructure": "4,2,2"
  },
  "accountingSubjects": [
    {
      "subjectKey": "1001",
      "subjectName": "库存现金",
      "subjectLevel": 1,
      "auxiliaryAccountingFlag": 0,
      "auxiliaryAccountingItem": "",
      "subjectType": "资产类",
      "unitOfMeasure": "",
      "balanceDirection": "借"
    }
  ],
  "departmentInfos": [
    {
      "departmentCode": "001",
      "departmentName": "研发部"
    }
  ],
  "employeeInfos": [
    {
      "employeeCode": "1001",
      "employeeName": "张三",
      "department": "研发部"
    }
  ],
  "businessUnits": [
    {
      "businessCode": "C1",
      "businessName": "xx公司",
      "category": "供应商"
    }
  ],
  "projectInfos": [
    {
      "projectCode": "P1",
      "projectName": "XX开发项目",
      "category": "研发",
      "mainCategory": "研发"
    }
  ],
  "inventoryInfos": [
    {
      "inventoryCode": "00001",
      "inventoryName": "风机",
      "specification": "",
      "unitOfMeasurement": "台"
    }
  ],
  "subjectBalances": [
    {
      "subjectKey": "1001",
      "currency": "CNY",
      "auxiliaryAccountingGroup": "",
      "initialBalance": "0.00",
      "initialQuantity": "0.000000",
      "initialForeignCurrencyBalance": "0.00",
      "debitAmount": "0.00",
      "debitQuantity": "0.000000",
      "debitForeignCurrencyAmount": "0.00",
      "creditAmount": "0.00",
      "creditQuantity": "0.000000",
      "creditForeignCurrencyAmount": "0.00",
      "endingBalance": "0.00",
      "endingQuantity": "0.000000",
      "endingForeignCurrencyBalance": "0.00",
      "accountingMonth": 7
    }
  ],
  "accountingVouchers": [
    {
      "voucherDate": "********",
      "voucherType": "记",
      "voucherNumber": "001",
      "lineNumber": 1,
      "summary": "销售",
      "subjectKey": "112201",
      "debitAmount": "30000.00",
      "creditAmount": "0.00",
      "currency": "CNY",
      "debitForeignCurrencyAmount": "0.00",
      "creditForeignCurrencyAmount": "0.00",
      "exchangeRate": "1.000000",
      "quantity": "0.000000",
      "unitPrice": "0.0000",
      "auxiliaryAccountingGroup": "c00001",
      "settlementMethod": "",
      "billType": "",
      "billNumber": "",
      "billDate": "",
      "attachmentNumber": 2,
      "creator": "admin",
      "reviewer": "",
      "accountants": "",
      "cashier": "",
      "accountingFlag": "1"
    }
  ]
}
```