package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 资产类调整事项
 *
 * <AUTHOR>
 */
@Data
public class AssetAdjustments {

  @ApiModelProperty("资产折旧、摊销调整额(元)")
  private BigDecimal depreciationAdjustment;

  @ApiModelProperty("不允许列支的资产准备金(元)")
  private BigDecimal nonDeductibleReserves;

  @ApiModelProperty("各项资产损失纳税调整金额(元)")
  private BigDecimal assetLossAdjustment;
}
