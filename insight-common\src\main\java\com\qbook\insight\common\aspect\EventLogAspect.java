package com.qbook.insight.common.aspect;

import com.qbook.insight.common.annotation.EventLog;
import com.qbook.insight.common.entity.User;
import com.qbook.insight.common.mapper.EventLogMapper;
import com.qbook.insight.common.util.IpUtil;
import com.qbook.insight.common.util.SecurityUtil;
import java.util.concurrent.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.NamedThreadLocal;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 事件日志处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
@ConditionalOnProperty(name = "component.eventlog-aspect", havingValue = "true")
public class EventLogAspect {

  @Resource private EventLogMapper eventLogMapper;

  private final ExecutorService executor =
      new ThreadPoolExecutor(
          1,
          1,
          0L,
          TimeUnit.SECONDS,
          new ArrayBlockingQueue<>(30),
          new ThreadPoolExecutor.DiscardPolicy());

  // 存储开始时间
  private static final ThreadLocal<Long> beginThreadLocal = new NamedThreadLocal<>("BeginTime");

  // 拦截请求开始
  @Before(value = "@annotation(eventLog)")
  @SuppressWarnings("unused")
  public void boBefore(JoinPoint joinPoint, EventLog eventLog) {
    beginThreadLocal.set(System.currentTimeMillis());
  }

  // 拦截正常结束
  @AfterReturning(value = "@annotation(eventLog)")
  @SuppressWarnings("unused")
  public void afterReturning(JoinPoint joinPoint, EventLog eventLog) {
    saveEventLog(eventLog, 0);
  }

  // 拦截异常结束
  @AfterThrowing(value = "@annotation(eventLog)")
  @SuppressWarnings("unused")
  public void afterThrowing(JoinPoint joinPoint, EventLog eventLog) {
    saveEventLog(eventLog, 1);
  }

  // 记录事件日志
  private void saveEventLog(EventLog eventLog, int ret) {
    try {
      com.qbook.insight.common.entity.EventLog log = new com.qbook.insight.common.entity.EventLog();
      User user = SecurityUtil.getCurrentUser();
      if (user == null) {
        return;
      }

      ServletRequestAttributes attributes =
          (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
      String clientIp;
      if (attributes != null) {
        HttpServletRequest request = attributes.getRequest();
        clientIp = IpUtil.getIpAddr(request);
      } else {
        clientIp = "0.0.0.0";
      }

      log.setUserId(user.getId());
      log.setEvent(eventLog.name());
      log.setResult(ret);
      log.setIp(clientIp);
      log.setLocation("");
      log.setCostTime(System.currentTimeMillis() - beginThreadLocal.get());

      executor.execute(() -> eventLogMapper.insert(log));
    } finally {
      beginThreadLocal.remove();
    }
  }
}
