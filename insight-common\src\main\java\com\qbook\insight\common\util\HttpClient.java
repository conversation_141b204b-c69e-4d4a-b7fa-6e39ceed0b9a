package com.qbook.insight.common.util;

import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

/**
 * HTTP 客户端工具类
 *
 * <AUTHOR>
 */
public class HttpClient {

  public static final String HTTP_PROTOCOL = "http";

  private static final CloseableHttpClient httpClient;

  @Data
  public static class Response {
    private String protocolVersion;
    private int statusCode;
    private String reasonPhrase;
    Map<String, String> headers;
    private String data;
  }

  static {
    PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
    connManager.setMaxTotal(200);
    connManager.setDefaultMaxPerRoute(20);
    httpClient = HttpClients.custom().setConnectionManager(connManager).build();
  }

  private static RequestConfig getRequestConfig(int timeout) {
    return RequestConfig.custom()
        .setSocketTimeout(timeout)
        .setConnectTimeout(timeout)
        .setConnectionRequestTimeout(timeout)
        .build();
  }

  private static Response getResponse(HttpUriRequest request, Map<String, String> headers)
      throws Exception {
    if (headers != null) {
      for (Map.Entry<String, String> entry : headers.entrySet()) {
        request.setHeader(entry.getKey(), entry.getValue());
      }
    }

    try (CloseableHttpResponse response = httpClient.execute(request)) {
      Response ret = new Response();

      ret.setProtocolVersion(response.getProtocolVersion().toString());
      ret.setStatusCode(response.getStatusLine().getStatusCode());
      ret.setReasonPhrase(response.getStatusLine().getReasonPhrase());

      for (Header header : response.getAllHeaders()) {
        if (ret.headers == null) {
          ret.headers = new HashMap<>();
        }
        ret.headers.put(header.getName(), header.getValue());
      }

      HttpEntity entity = response.getEntity();
      if (entity != null) {
        ret.setData(EntityUtils.toString(entity));
      } else {
        ret.setData("");
      }

      return ret;
    }
  }

  /**
   * 执行HTTP get 方法获取结果
   *
   * @param url URL
   * @param timeout 超时时间（毫秒）
   * @return HTTP response
   * @throws Exception Exception
   */
  public static Response get(String url, Map<String, String> headers, int timeout)
      throws Exception {
    HttpGet httpGet = new HttpGet(url);
    httpGet.setConfig(getRequestConfig(timeout));
    return getResponse(httpGet, headers);
  }

  /**
   * 执行HTTP post 方法获取结果
   *
   * @param url URL
   * @param data Post数据（若没有数据，传入null）
   * @param headers HTTP header 键值对集合
   * @param timeout 超时时间（毫秒）
   * @return HTTP response
   * @throws Exception Exception
   */
  public static Response post(String url, String data, Map<String, String> headers, int timeout)
      throws Exception {
    HttpPost httpPost = new HttpPost(url);
    httpPost.setConfig(getRequestConfig(timeout));
    if (data != null) {
      httpPost.setEntity(new StringEntity(data, "utf-8"));
    }
    return getResponse(httpPost, headers);
  }
}
