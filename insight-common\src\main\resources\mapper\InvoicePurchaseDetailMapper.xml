<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.qbook.insight.common.mapper.InvoicePurchaseDetailMapper">

  <!-- 批量查询已存在的发票键值(zzfpdm+zzfphm) -->
  <select id="selectExistingInvoiceKeys"
    resultType="java.lang.String">
    SELECT CONCAT(zzfpdm, zzfphm) as invoiceKey
    FROM invoice_purchase_detail
    WHERE CONCAT(zzfpdm, zzfphm) IN
    <foreach collection="invoiceKeys" item="item" open="("
      separator="," close=")">
      #{item}
    </foreach>
  </select>

  <insert id="insertBatch">
    INSERT IGNORE INTO invoice_purchase_detail (
    task_id, tax_id, zzfpdm, zzfphm, fpkjfxlxdm,
    gmfmc, gmfnsrsbh, kprq, ssflbm, tdywlx,
    hwmc, easy_hwmc, ggxh, dw, dw_symbol,
    xssl, dj, hjje, sl, hjse,
    jshj, fplydm, fppzdm, fpztdm, sflzfp,
    kpr, bz, kprqn, kprqy, gather_datetime,
    create_time
    ) VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.taskId}, #{item.taxId}, #{item.zzfpdm}, #{item.zzfphm}, #{item.fpkjfxlxdm},
      #{item.gmfmc}, #{item.gmfnsrsbh}, #{item.kprq}, #{item.ssflbm},
      #{item.tdywlx},
      #{item.hwmc}, #{item.easyHwmc}, #{item.ggxh}, #{item.dw}, #{item.dwSymbol},
      #{item.xssl}, #{item.dj}, #{item.hjje}, #{item.sl}, #{item.hjse},
      #{item.jshj}, #{item.fplydm}, #{item.fppzdm}, #{item.fpztdm},
      #{item.sflzfp},
      #{item.kpr}, #{item.bz}, #{item.kprqn}, #{item.kprqy}, #{item.gatherDatetime},
      #{item.createTime}
      )
    </foreach>
  </insert>
</mapper>
