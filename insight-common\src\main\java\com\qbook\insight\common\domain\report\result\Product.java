package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 商品
 *
 * <AUTHOR>
 */
@Data
public class Product {

  @ApiModelProperty("序号")
  private Integer index;

  @ApiModelProperty("商品名称")
  private String name;

  @ApiModelProperty("单位")
  private String unit;

  @ApiModelProperty("数量")
  private Float quantity;

  @ApiModelProperty("金额")
  private BigDecimal amount;

  @ApiModelProperty("均价")
  private Double avgPrice;

  @ApiModelProperty("占比%")
  private Double percentage;
}
