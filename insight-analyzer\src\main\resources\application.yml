---
component:
  redis: true
  db: true
spring.profiles.default: "dev"
spring.application.name: "insight-analyzer"
spring.jackson:
  date-format: "yyyy-MM-dd HH:mm:ss"
  time-zone: "Asia/Shanghai"
mybatis-plus:
  mapper-locations: "classpath:mapper/**/*.xml"
  type-aliases-package: "com.qbook.insight.**.entity"
  global-config:
    banner: false
  configuration:
    cache-enabled: true
    log-impl: "org.apache.ibatis.logging.slf4j.Slf4jImpl"
    map-underscore-to-camel-case: true
