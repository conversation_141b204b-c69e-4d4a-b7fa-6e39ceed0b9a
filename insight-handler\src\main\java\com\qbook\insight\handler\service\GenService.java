package com.qbook.insight.handler.service;

import com.qbook.insight.common.entity.Column;
import com.qbook.insight.common.entity.Table;
import com.qbook.insight.common.vo.CodeGenParam;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;

/**
 * 代码生成接口
 *
 * <AUTHOR>
 */
@Service
public interface GenService {

  /** 获取表名列表 */
  List<Table> getTables();

  /** 获取指定表的字段 */
  List<Column> getColumnsByTableName(String tableName);

  /** 预览代码 */
  Map<String, String> codePreview(CodeGenParam param);
}
