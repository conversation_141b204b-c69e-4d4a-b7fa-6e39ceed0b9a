package com.qbook.insight.collector.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.hash.BloomFilter;
import com.qbook.insight.collector.domian.TaskReturnData;
import com.qbook.insight.collector.util.BloomFilterUtil;
import com.qbook.insight.common.entity.InvoicePurchase;
import com.qbook.insight.common.entity.InvoicePurchaseDetail;
import com.qbook.insight.common.entity.InvoiceSales;
import com.qbook.insight.common.entity.InvoiceSalesDetail;
import com.qbook.insight.common.mapper.InvoicePurchaseDetailMapper;
import com.qbook.insight.common.mapper.InvoicePurchaseMapper;
import com.qbook.insight.common.mapper.InvoiceSalesDetailMapper;
import com.qbook.insight.common.mapper.InvoiceSalesMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 处理发票数据服务
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-23 下午5:37
 */
@Service
@Slf4j
public class HandleInvoiceService {
  @Resource private InvoicePurchaseMapper invoicePurchaseMapper;
  @Resource private InvoiceSalesMapper invoiceSalesMapper;
  @Resource private InvoicePurchaseDetailMapper invoicePurchaseDetailMapper;
  @Resource private InvoiceSalesDetailMapper invoiceSalesDetailMapper;

  @DSTransactional
  public void processTaskResult(TaskReturnData taskReturnData) {
    try {
      if (taskReturnData == null) {
        log.info("任务结果为空");
        return;
      }
      String taxId = taskReturnData.getTaxId();
      if (taxId == null) {
        log.info("税号为空");
        return;
      }
      String taskResult = taskReturnData.getTaskResult();
      JSONObject ticketCollectResult =
          JSONUtil.parseObj(taskResult).getJSONObject("ticketCollectResult");
      List<JSONObject> buyTicketBaseList =
          ticketCollectResult.getJSONArray("buyTicketBaseList").toList(JSONObject.class);
      List<JSONObject> buyTicketTotalList =
          ticketCollectResult.getJSONArray("buyTicketTotalList").toList(JSONObject.class);
      List<JSONObject> saleTicketBaseList =
          ticketCollectResult.getJSONArray("saleTicketBaseList").toList(JSONObject.class);
      List<JSONObject> saleTicketTotalList =
          ticketCollectResult.getJSONArray("saleTicketTotalList").toList(JSONObject.class);

      // 处理发票数据
      handleInvoiceData(
          taxId, buyTicketBaseList, buyTicketTotalList, saleTicketBaseList, saleTicketTotalList);
    } catch (Exception e) {
      throw new RuntimeException("处理发票数据失败", e);
    }
  }

  /**
   * 处理发票数据
   *
   * @param taxId 税号
   * @param buyTicketBaseList 进项发票基础数据列表
   * @param buyTicketTotalList 进项发票详细数据列表
   * @param saleTicketBaseList 销项发票基础数据列表
   * @param saleTicketTotalList 销项发票详细数据列表
   */
  public void handleInvoiceData(
      String taxId,
      List<JSONObject> buyTicketBaseList,
      List<JSONObject> buyTicketTotalList,
      List<JSONObject> saleTicketBaseList,
      List<JSONObject> saleTicketTotalList) {

    // 处理进项数据
    handleInvoiceType(
        taxId,
        buyTicketBaseList,
        InvoicePurchase.class,
        invoicePurchaseMapper::selectExistingInvoiceKeys,
        invoicePurchaseMapper::insertBatch);

    // 处理销项数据
    handleInvoiceType(
        taxId,
        saleTicketBaseList,
        InvoiceSales.class,
        invoiceSalesMapper::selectExistingInvoiceKeys,
        invoiceSalesMapper::insertBatch);

    // 处理进项详细数据
    handleInvoiceType(
        taxId,
        buyTicketTotalList,
        InvoicePurchaseDetail.class,
        invoicePurchaseDetailMapper::selectExistingInvoiceKeys,
        invoicePurchaseDetailMapper::insertBatch);

    // 处理销项详细数据
    handleInvoiceType(
        taxId,
        saleTicketTotalList,
        InvoiceSalesDetail.class,
        invoiceSalesDetailMapper::selectExistingInvoiceKeys,
        invoiceSalesDetailMapper::insertBatch);
  }

  /**
   * 通用发票类型处理方法
   *
   * @param taxId 税号
   * @param dataList 数据列表
   * @param entityClass 实体类
   * @param queryFunction 查询已存在的发票键值的函数
   * @param insertFunction 批量插入函数
   */
  private <T> void handleInvoiceType(
      String taxId,
      List<JSONObject> dataList,
      Class<T> entityClass,
      java.util.function.Function<List<String>, List<String>> queryFunction,
      java.util.function.Consumer<List<T>> insertFunction) {
    if (dataList.isEmpty()) {
      log.info("发票列表数据为空");
      return;
    }
    // 检查该税号是否已有数据
    boolean hasExistingData = checkExistingData(taxId, entityClass);

    if (!hasExistingData) {
      // 首次插入，无需去重
      List<T> entityList = convertToBean(dataList, entityClass);
      batchInsert(entityList, insertFunction);
    } else {
      // 非首次插入，需要去重
      processInvoiceData(dataList, entityClass, queryFunction, insertFunction);
    }
  }

  /**
   * 检查指定税号是否已有发票数据
   *
   * @param taxId 税号
   * @param entityClass 实体类
   */
  private boolean checkExistingData(String taxId, Class<?> entityClass) {
    if (entityClass == InvoicePurchase.class) {
      return invoicePurchaseMapper.selectCount(
              new LambdaQueryWrapper<InvoicePurchase>().eq(InvoicePurchase::getTaxId, taxId))
          > 0;
    } else if (entityClass == InvoiceSales.class) {
      return invoiceSalesMapper.selectCount(
              new LambdaQueryWrapper<InvoiceSales>().eq(InvoiceSales::getTaxId, taxId))
          > 0;
    } else if (entityClass == InvoicePurchaseDetail.class) {
      return invoicePurchaseDetailMapper.selectCount(
              new LambdaQueryWrapper<InvoicePurchaseDetail>()
                  .eq(InvoicePurchaseDetail::getTaxId, taxId))
          > 0;
    } else if (entityClass == InvoiceSalesDetail.class) {
      return invoiceSalesDetailMapper.selectCount(
              new LambdaQueryWrapper<InvoiceSalesDetail>().eq(InvoiceSalesDetail::getTaxId, taxId))
          > 0;
    }
    return false;
  }

  /**
   * 通用发票数据处理方法
   *
   * @param dataList 数据列表
   * @param queryFunction 查询已存在的发票键值的函数
   * @param entityClass 实体类
   * @param insertFunction 批量插入函数
   */
  private <T> void processInvoiceData(
      List<JSONObject> dataList,
      Class<T> entityClass,
      java.util.function.Function<List<String>, List<String>> queryFunction,
      java.util.function.Consumer<List<T>> insertFunction) {
    List<JSONObject> deduplicatedList = deduplicateByInvoiceKey(dataList, queryFunction);
    List<T> entityList = convertToBean(deduplicatedList, entityClass);
    batchInsert(entityList, insertFunction);
  }

  /**
   * 根据zzfpdm+zzfphm字段，通过布隆过滤器去重
   *
   * @param jsonObjectList 数据列表
   * @param queryFunction 查询已存在的发票键值的函数
   */
  public List<JSONObject> deduplicateByInvoiceKey(
      List<JSONObject> jsonObjectList,
      java.util.function.Function<List<String>, List<String>> queryFunction) {
    if (jsonObjectList == null || jsonObjectList.isEmpty()) {
      return new ArrayList<>();
    }

    // 提取所有zzfpdm+zzfphm组合
    List<String> allInvoiceKeys =
        jsonObjectList.stream()
            .map(this::generateInvoiceKey)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

    // 获取布隆过滤器
    BloomFilter<String> existingInvoiceKeys =
        getExistingInvoiceKeysFilter(allInvoiceKeys, queryFunction);

    // 过滤掉已存在的数据
    return jsonObjectList.stream()
        .filter(json -> !existingInvoiceKeys.mightContain(generateInvoiceKey(json)))
        .collect(Collectors.toList());
  }

  /**
   * 获取数据库中已存在的发票键值的布隆过滤器
   *
   * @param invoiceKeys 所有发票键值
   * @param queryFunction 查询已存在的发票键值的函数
   */
  @SuppressWarnings("UnstableApiUsage")
  public BloomFilter<String> getExistingInvoiceKeysFilter(
      List<String> invoiceKeys,
      java.util.function.Function<List<String>, List<String>> queryFunction) {
    // 创建布隆过滤器，预估元素数量和误判率
    BloomFilter<String> bloomFilter =
        BloomFilterUtil.createStringBloomFilter(Math.max(invoiceKeys.size(), 10000L), 0.01);

    if (invoiceKeys.isEmpty()) {
      return bloomFilter;
    }

    // 分批查询数据库中已存在的发票键值
    int batchSize = 100;
    for (int i = 0; i < invoiceKeys.size(); i += batchSize) {
      int endIndex = Math.min(i + batchSize, invoiceKeys.size());
      List<String> subList = invoiceKeys.subList(i, endIndex);

      // 使用传入的函数查询数据库中已存在的发票键值
      List<String> existingBatch = queryFunction.apply(subList);
      // 添加到布隆过滤器
      existingBatch.forEach(bloomFilter::put);
    }

    return bloomFilter;
  }

  /**
   * 生成发票唯一键：zzfpdm + zzfphm
   *
   * @param json json对象
   * @return 发票唯一键：fpkjfxlxdm
   */
  private String generateInvoiceKey(JSONObject json) {
    if (json == null) {
      return null;
    }
    String zzfpdm = json.getStr("zzfpdm");
    String zzfphm = json.getStr("zzfphm");
    if (zzfpdm == null || zzfphm == null) {
      return null;
    }
    return zzfpdm + zzfphm;
  }

  /**
   * json集合内容转换为实体类列表
   *
   * @param list 数据列表
   * @param beanClass 实体类
   */
  public <T> List<T> convertToBean(List<JSONObject> list, Class<T> beanClass) {
    return list.stream().map(json -> JSONUtil.toBean(json, beanClass)).collect(Collectors.toList());
  }

  /**
   * 分批批量插入数据
   *
   * @param list 数据列表
   * @param insertFunction 插入函数
   */
  private <T> void batchInsert(List<T> list, java.util.function.Consumer<List<T>> insertFunction) {
    int batchSize = 1000;
    if (list == null || list.isEmpty()) {
      return;
    }

    int totalSize = list.size();
    int fromIndex = 0;
    int toIndex = Math.min(batchSize, totalSize);

    while (fromIndex < totalSize) {
      List<T> subList = list.subList(fromIndex, toIndex);
      try {
        insertFunction.accept(subList);
      } catch (Exception e) {
        log.error("批量插入失败，数据批次：{}", subList, e);
        throw new RuntimeException("批量插入失败", e);
      }
      fromIndex = toIndex;
      toIndex = Math.min(fromIndex + batchSize, totalSize);
    }
  }
}
