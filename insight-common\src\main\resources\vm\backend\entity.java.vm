package ${packagePath}.entity;

#if(${packagePath} != ${baseEntityPath})
import ${baseEntityPath}.entity.BaseEntity;
#end
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

#if(!${classChineseName})
  #set($classChineseName=${className})
#end
/**
 * ${classChineseName}实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ${className} extends BaseEntity {

#foreach ($column in $columns)
  @ApiModelProperty("$column.columnComment")
  private $column.javaType $column.javaField;

#end
}
