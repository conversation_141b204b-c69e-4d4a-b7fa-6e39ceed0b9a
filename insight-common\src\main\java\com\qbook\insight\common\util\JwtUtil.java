package com.qbook.insight.common.util;

import com.qbook.insight.common.entity.User;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Date;
import javax.annotation.PostConstruct;
import javax.crypto.SecretKey;
import javax.servlet.http.HttpServletRequest;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * JWT 工具类
 *
 * <AUTHOR>
 */
@Component
@Data
@ConditionalOnProperty(name = "component.jwt", havingValue = "true")
public class JwtUtil {

  // 令牌自定义标识
  @Value("${jwt.header:Authorization}")
  private String headerFlag;

  // 令牌秘钥
  @Value("${jwt.secret}")
  private String secret;

  // 过期时间
  @Value("${jwt.expiration:180m}")
  private Duration expiration;

  // 提前刷新时间
  @Value("${jwt.refresh-early:20m}")
  private Duration refreshEarly;

  // 密钥
  private SecretKey key;

  private static final String CLAIM_KEY_ID = "id";
  private static final String CLAIM_KEY_ROLES = "roles";
  private static final String CLAIM_KEY_REAL_NAME = "realName";
  private final ThreadLocal<User> userThreadLocal = new ThreadLocal<>();

  @PostConstruct
  public void init() {
    this.key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
  }

  // Get token from HTTP request
  public String getToken(HttpServletRequest request) {
    String header = request.getHeader(headerFlag);
    if (header != null && header.startsWith("Bearer ")) {
      return header.substring(7);
    }
    return null;
  }

  // Generate token
  public String generateToken(User user) {
    return Jwts.builder()
        .subject(user.getUsername())
        .claims()
        .add(CLAIM_KEY_ID, user.getId())
        .add(CLAIM_KEY_ROLES, user.getRoles())
        .add(CLAIM_KEY_REAL_NAME, user.getRealName())
        .and()
        .issuedAt(new Date())
        .expiration(new Date(System.currentTimeMillis() + expiration.toMillis()))
        .signWith(key)
        .compact();
  }

  // Get subject from token
  public User getUser(String token) {
    try {
      Claims claims = Jwts.parser().verifyWith(key).build().parseSignedClaims(token).getPayload();
      User user = userThreadLocal.get();
      if (user == null) {
        user = new User();
        user.setUsername(claims.getSubject());
      }
      user.setUsername(claims.getSubject());
      user.setId(Long.parseLong(claims.get(CLAIM_KEY_ID).toString()));
      user.setRoles(claims.get(CLAIM_KEY_ROLES).toString());
      user.setRealName(claims.get(CLAIM_KEY_REAL_NAME).toString());

      long expireTime = claims.getExpiration().getTime();
      long currentTime = System.currentTimeMillis();
      if (expireTime - currentTime <= refreshEarly.toMillis()) {
        user.setAccessToken(generateToken(user));
      }

      return user;
    } catch (Exception ignore) {
      return null;
    } finally {
      userThreadLocal.remove();
    }
  }
}
