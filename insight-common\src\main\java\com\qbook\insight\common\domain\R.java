package com.qbook.insight.common.domain;

import com.qbook.insight.common.constant.RCode;
import com.qbook.insight.common.constant.RMsg;
import lombok.Data;

/**
 * REST API 返回结果
 *
 * <AUTHOR>
 */
@Data
public class R {

  // 错误码
  private int code;

  // 错误信息
  private String message;

  // 数据
  private Object data;

  public R(int code, String message, Object data) {
    this.code = code;
    this.message = message;
    this.data = data;
  }

  private static R result(int code, String message, Object data) {
    return new R(code, message, data);
  }

  public static R ok() {
    return result(RCode.SUCCESS, RMsg.SUCCESS, null);
  }

  public static R ok(Object data) {
    return result(RCode.SUCCESS, RMsg.SUCCESS, data);
  }

  public static R error(int code) {
    return result(code, null, null);
  }

  public static R error(int code, String message) {
    return result(code, message, null);
  }

  public static R error(int code, String message, Object data) {
    return result(code, message, data);
  }

  public static R affect(int rows) {
    return rows > 0 ? ok() : error(RCode.ERROR, "操作失败");
  }

  public static R affect(boolean result) {
    return result ? ok() : error(RCode.ERROR, "操作失败");
  }
}
