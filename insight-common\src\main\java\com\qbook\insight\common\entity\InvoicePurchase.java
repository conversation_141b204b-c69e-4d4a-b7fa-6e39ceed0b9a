package com.qbook.insight.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 进项发票-发票基础信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InvoicePurchase {

  @TableId(type = IdType.AUTO)
  @ApiModelProperty("记录ID")
  private Long id;

  @ApiModelProperty("所属任务编号")
  private String taskId;

  @ApiModelProperty("销方税号")
  private String taxId;

  @ApiModelProperty("发票代码")
  private String zzfpdm;

  @ApiModelProperty("发票号码")
  private String zzfphm;

  @ApiModelProperty("数电票号码")
  private String fpkjfxlxdm;

  @ApiModelProperty("购方名称")
  private String gmfmc;

  @ApiModelProperty("购方识别号")
  private String gmfnsrsbh;

  @ApiModelProperty("开票日期")
  private Date kprq;

  @ApiModelProperty("金额")
  private BigDecimal hjje;

  @ApiModelProperty("税额")
  private BigDecimal hjse;

  @ApiModelProperty("价税合计")
  private BigDecimal jshj;

  @ApiModelProperty("发票来源")
  private String fplydm;

  @ApiModelProperty("发票票种")
  private String fppzdm;

  @ApiModelProperty("发票状态")
  private String fpztdm;

  @ApiModelProperty("发票风险等级")
  private String sflzfp;

  @ApiModelProperty("开票人")
  private String kpr;

  @ApiModelProperty("备注")
  private String bz;

  @ApiModelProperty("开票日期年")
  private Integer kprqn;

  @ApiModelProperty("开票日期月")
  private Integer kprqy;

  @ApiModelProperty("采集时间")
  private Date gatherDatetime;

  @ApiModelProperty("是否可以抵扣")
  private String deduction;

  @ApiModelProperty("是否被勾选确认")
  private String checkout;

  @ApiModelProperty("勾选日期")
  private Date checkoutDatetime;

  @ApiModelProperty("勾选任务ID")
  private String confirmTaskId;

  @ApiModelProperty("原始发票文件")
  private String ofdFile;

  @ApiModelProperty("发票扩充内容")
  private String expandContent;

  @ApiModelProperty("创建时间")
  private Date createTime;
}
