package com.qbook.insight.common.util;

import cn.hutool.core.bean.BeanPath;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ReflectUtil;
import com.qbook.insight.common.domain.report.result.Result;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用 Result 构造器
 *
 * <AUTHOR>
 */
@Slf4j
public class UniversalResultBuilder {

  public static Result buildResult(Map<String, Object> resultMap) {
    if (CollUtil.isEmpty(resultMap)) {
      return new Result();
    }

    Result result = new Result();

    try {
      PathAnalysisResult analysis = analyzeFieldPaths(Result.class, resultMap.keySet());

      analysis
          .getSimpleFields()
          .forEach(
              fieldPath -> {
                Object value = resultMap.get(fieldPath);
                try {
                  new BeanPath(fieldPath).set(result, value);
                } catch (Exception e) {
                  log.warn("设置简单字段失败: {}, 值: {}", fieldPath, value, e);
                }
              });

      for (Map.Entry<String, Set<String>> entry : analysis.getListFieldGroups().entrySet()) {
        String listPath = entry.getKey();
        List<Object> listData = mergeListData(Result.class, listPath, entry.getValue(), resultMap);
        new BeanPath(listPath).set(result, listData);
      }

    } catch (Exception e) {
      log.error("构造Result对象失败", e);
    }

    return result;
  }

  /** 分析字段路径结构 */
  private static PathAnalysisResult analyzeFieldPaths(Class<?> rootClass, Set<String> fieldPaths) {
    PathAnalysisResult result = new PathAnalysisResult();

    for (String path : fieldPaths) {
      String listFieldPath = findListFieldPath(rootClass, path);
      if (listFieldPath != null) {
        result.addListField(listFieldPath, path);
      } else {
        result.addSimpleField(path);
      }
    }

    return result;
  }

  /** 查找路径中包含的 List 字段路径 */
  private static String findListFieldPath(Class<?> rootClass, String fieldPath) {
    String[] parts = fieldPath.split("\\.");
    Class<?> current = rootClass;
    StringBuilder pathBuilder = new StringBuilder();

    for (String part : parts) {
      if (pathBuilder.length() > 0) pathBuilder.append(".");
      pathBuilder.append(part);

      try {
        Field field = ReflectUtil.getField(current, part);
        if (field == null) return null;

        Class<?> type = field.getType();
        if (List.class.isAssignableFrom(type)) {
          return pathBuilder.toString();
        }

        current = type;
      } catch (Exception e) {
        return null;
      }
    }

    return null;
  }

  /** 合并 List 类型字段数据 */
  private static List<Object> mergeListData(
      Class<?> rootClass,
      String listFieldPath,
      Set<String> relatedPaths,
      Map<String, Object> resultMap) {

    Class<?> elementType = getListElementType(rootClass, listFieldPath);
    if (elementType == null) {
      log.warn("无法识别 List 元素类型: {}", listFieldPath);
      return Collections.emptyList();
    }

    // 判断处理方式：单一路径或多路径无子字段,直接赋值；多路径有子字段,年份分组
    boolean needsYearGrouping = needsYearGrouping(rootClass, listFieldPath, relatedPaths);

    if (!needsYearGrouping) {
      // 直接赋值：单一路径数据或多路径无子字段数据
      return handleDirectListData(listFieldPath, relatedPaths, resultMap, elementType);
    }

    // 年份分组：多路径有子字段的复杂税务数据
    return handleYearGroupedData(listFieldPath, relatedPaths, resultMap, elementType);
  }

  /** 判断是否需要年份分组 */
  private static boolean needsYearGrouping(
      Class<?> rootClass, String listFieldPath, Set<String> relatedPaths) {
    // 单一路径直接赋值
    if (relatedPaths.size() <= 1) {
      return false;
    }

    // 检查是否有子字段路径: 多路径且有子字段时才需要年份分组;多路径但无子字段时直接赋值
    return relatedPaths.stream()
        .anyMatch(path -> !path.equals(listFieldPath) && path.startsWith(listFieldPath + "."));
  }

  /** 处理直接List数据（不需要年份分组） */
  private static List<Object> handleDirectListData(
      String listFieldPath,
      Set<String> relatedPaths,
      Map<String, Object> resultMap,
      Class<?> elementType) {

    // 首先尝试从主路径获取数据
    Object data = resultMap.get(listFieldPath);

    // 如果主路径没有数据，尝试从相关路径获取
    if (!(data instanceof List)) {
      for (String path : relatedPaths) {
        if (path.startsWith(listFieldPath + ".")) {
          data = resultMap.get(path);
          if (data instanceof List) {
            break;
          }
        }
      }
    }

    if (!(data instanceof List)) {
      log.warn("路径 {} 的数据不是List类型: {}", listFieldPath, data);
      return Collections.emptyList();
    }

    List<Map<String, Object>> list = (List<Map<String, Object>>) data;
    return list.stream()
        .map(item -> BeanUtil.fillBeanWithMap(item, ReflectUtil.newInstance(elementType), true))
        .collect(Collectors.toList());
  }

  /** 处理需要年份分组的数据 */
  private static List<Object> handleYearGroupedData(
      String listFieldPath,
      Set<String> relatedPaths,
      Map<String, Object> resultMap,
      Class<?> elementType) {

    Map<Integer, Map<String, Object>> yearDataMap = new HashMap<>();

    for (String path : relatedPaths) {
      Object data = resultMap.get(path);
      if (!(data instanceof List)) continue;

      List<Map<String, Object>> list = (List<Map<String, Object>>) data;

      for (Map<String, Object> item : list) {
        Integer year = Convert.toInt(item.get("year"));
        if (year == null) continue;

        yearDataMap.computeIfAbsent(year, k -> new HashMap<>());
        Map<String, Object> yearData = yearDataMap.get(year);

        if (path.equals(listFieldPath)) {
          yearData.putAll(item);
        } else {
          String subPath =
              path.length() > listFieldPath.length()
                  ? path.substring(listFieldPath.length() + 1)
                  : null;
          if (subPath != null) {
            Object val = item.get(subPath.substring(subPath.lastIndexOf('.') + 1));

            // 确保年份信息被保留
            if (!yearData.containsKey("year")) {
              yearData.put("year", year);
            }

            setValueInMap(yearData, subPath, val);
          }
        }
      }
    }

    return yearDataMap.keySet().stream()
        .sorted()
        .map(
            year ->
                BeanUtil.fillBeanWithMap(
                    yearDataMap.get(year), ReflectUtil.newInstance(elementType), true))
        .collect(Collectors.toList());
  }

  /** 提取 List 元素类型 */
  private static Class<?> getListElementType(Class<?> rootClass, String listPath) {
    try {
      String[] parts = listPath.split("\\.");
      Class<?> current = rootClass;

      for (String part : parts) {
        Field field = ReflectUtil.getField(current, part);
        if (field == null) return null;

        if (List.class.isAssignableFrom(field.getType())) {
          Type genType = field.getGenericType();
          if (genType instanceof ParameterizedType) {
            ParameterizedType paramType = (ParameterizedType) genType;
            return (Class<?>) paramType.getActualTypeArguments()[0];
          }
        }

        current = field.getType();
      }
    } catch (Exception e) {
      log.warn("获取 List 元素类型失败: {}", listPath, e);
    }

    return null;
  }

  /** 在 Map 中设置嵌套字段值 */
  private static void setValueInMap(Map<String, Object> map, String path, Object value) {
    String[] parts = path.split("\\.");
    Map<String, Object> current = map;

    for (int i = 0; i < parts.length - 1; i++) {
      current = (Map<String, Object>) current.computeIfAbsent(parts[i], k -> new HashMap<>());
    }

    current.put(parts[parts.length - 1], value);
  }

  /** 路径分析结构 */
  @Getter
  private static class PathAnalysisResult {
    private final Set<String> simpleFields = new HashSet<>();
    private final Map<String, Set<String>> listFieldGroups = new HashMap<>();

    public void addSimpleField(String fieldPath) {
      simpleFields.add(fieldPath);
    }

    public void addListField(String listFieldPath, String fullFieldPath) {
      listFieldGroups.computeIfAbsent(listFieldPath, k -> new HashSet<>()).add(fullFieldPath);
    }
  }
}
