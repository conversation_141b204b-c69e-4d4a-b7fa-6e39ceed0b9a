package com.qbook.insight.collector.service;

import com.qbook.insight.common.util.StringUtils;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

/**
 * MQ 队列监听器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MqReceiver {

  @Resource private HandleCollectDataService handleCollectDataService;

  @RabbitListener(queues = "fkservice_result_queue")
  public void listen(String result) {
    log.info("{} {}", "[RESULT]", result);
    if (!StringUtils.hasLength(result)) {
      return;
    }
    try {
      handleCollectDataService.handleCollectData(result);
    } catch (Exception e) {
      log.error("任务处理失败: {}", result, e);
      handleCollectDataService.handleErrorCollectData(result, e);
    }
  }
}
