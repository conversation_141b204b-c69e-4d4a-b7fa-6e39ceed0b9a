package com.qbook.insight.handler.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qbook.insight.common.entity.Report;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.common.vo.ReportVO;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 报告操作接口
 *
 * <AUTHOR>
 */
@Service
public interface ReportService {

  /** 添加报告 */
  int add(Report report);

  /** 删除报告 */
  int delete(long id);

  /** 修改报告审核状态 */
  int update(Long id, Report report);

  /** 获取报告列表 */
  List<Report> list();

  /** 获取报告列表(分页) */
  IPage<ReportVO> page(PageParam pageParam, String realName, String corpName, String taxId);

  /** 根据公司ID获取获取报告列表 */
  List<Report> selectByClientIds(List<Long> idList);

  /** 删除指定客户的所有报告 */
  void deleteByClientId(Long clientId);
}
