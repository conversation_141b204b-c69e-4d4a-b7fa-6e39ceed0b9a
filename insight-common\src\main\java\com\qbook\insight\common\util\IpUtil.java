package com.qbook.insight.common.util;

import javax.servlet.http.HttpServletRequest;

/**
 * IP 工具类
 *
 * <AUTHOR>
 */
public class IpUtil {

  /**
   * 获取IP
   *
   * @param request 请求对象
   * @return IP地址
   */
  public static String getIpAddr(HttpServletRequest request) {
    if (request == null) {
      return "unknown";
    }
    String ip = request.getHeader("x-forwarded-for");
    if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getHeader("Proxy-Client-IP");
    }
    if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getHeader("X-Forwarded-For");
    }
    if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getHeader("WL-Proxy-Client-IP");
    }
    if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getHeader("X-Real-IP");
    }

    if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
      ip = request.getRemoteAddr();
    }

    return "0:0:0:0:0:0:0:1".equals(ip) ? "127.0.0.1" : getMultistageReverseProxyIp(ip);
  }

  /**
   * 从多级反向代理中获得第一个非unknown IP地址
   *
   * @param ip 获得的IP地址
   * @return 第一个非unknown IP地址
   */
  public static String getMultistageReverseProxyIp(String ip) {
    if (ip != null && ip.indexOf(",") > 0) {
      final String[] ips = ip.trim().split(",");
      for (String subIp : ips) {
        if (!isUnknown(subIp)) {
          ip = subIp;
          break;
        }
      }
    }
    return StringUtils.substring(ip, 0, 255);
  }

  public static boolean isUnknown(String checkString) {
    return StringUtils.isBlank(checkString) || "unknown".equalsIgnoreCase(checkString);
  }
}
