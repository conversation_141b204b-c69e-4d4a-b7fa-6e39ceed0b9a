package com.qbook.insight.handler.controller;

import com.qbook.insight.handler.service.WxPayService;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

/**
 * 微信支付相关接口
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-08 下午4:56
 */
@Controller
@RequestMapping("/wxPay")
@Slf4j
public class WxPayController {

  @Resource private WxPayService wxPayService;

  /**
   * 重定向至微信内置浏览器进行支付
   *
   * @param orderId 订单id
   */
  @GetMapping("/wxPayCode/{orderId}")
  @ResponseBody
  public void redirectWxPay(@PathVariable("orderId") String orderId) {
    wxPayService.redirectWxPay(orderId);
  }

  /**
   * 微信回调支付页面
   *
   * @param request 请求
   * @param model 模型
   * @return 模板名称
   */
  @PostMapping("/oauth2Callback")
  public String showPayPage(HttpServletRequest request, Model model) {
    return wxPayService.showPayPage(request, model);
  }

  /** 微信支付回调 */
  @PostMapping("/notify")
  public String wxGetNotify(HttpServletRequest request) {
    return wxPayService.wxGetNotify(request);
  }
}
