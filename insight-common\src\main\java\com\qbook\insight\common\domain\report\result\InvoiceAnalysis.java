package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.DataSource;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 发票分析
 *
 * <AUTHOR>
 */
@Data
public class InvoiceAnalysis {

  @ApiModelProperty("进项发票分析")
  @DataSource("进项发票")
  private InvoicePurchaseAnalysis purchaseInvoice;

  @ApiModelProperty("销项发票分析")
  @DataSource("销项发票")
  private InvoiceSalesAnalysis salesInvoice;

  @ApiModelProperty("进项发票总金额（元）")
  @DataSource("进项发票")
  private Float purchaseTotalAmount;

  @ApiModelProperty("销项发票总金额（元）")
  @DataSource("销项发票")
  private Float salesTotalAmount;

  @ApiModelProperty("进项是否大于销项")
  @DataSource("进项发票、销项发票")
  private Boolean isPurchaseGTSalesTotal;

  @ApiModelProperty("进项税额（元）")
  @DataSource("进项发票")
  private Float purchaseTax;

  @ApiModelProperty("销项税额（元）")
  @DataSource("销项发票")
  private Float salesTax;

  @ApiModelProperty("'销项税额' 远小于 '进项税额'")
  @DataSource("进项发票、销项发票")
  private Boolean isSalesMLTPurchaseTax;

  @ApiModelProperty("是否有销无进 (少进)")
  @DataSource("进项发票、销项发票")
  private Boolean isOnlySales;

  @ApiModelProperty("发票金额与申报表对比分析")
  private List<SalesVsDeclare> salesVsDeclares;

  @ApiModelProperty("大额咨询类发票明细 (人工分析)")
  private List<Object> largeConsultingInvoices;

  @ApiModelProperty("百货、超市、药店、商场等发票列表 (人工分析)")
  private List<Object> retailSupermarketInvoices;
}
