package com.qbook.insight.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qbook.insight.common.entity.Corp;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 公司Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CorpMapper extends BaseMapper<Corp> {
  Map<String, Object> selectCorpInfoById(@Param("id") Long id);
}
