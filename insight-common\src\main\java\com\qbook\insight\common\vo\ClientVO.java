package com.qbook.insight.common.vo;

import com.qbook.insight.common.entity.Client;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientVO extends Client {

  @ApiModelProperty("公司名称")
  private String corpName;

  @ApiModelProperty("社会信用代码(税号)")
  private String taxId;
}
