---
component:
  redis: true
  db: true
  sql-page: true
  jwt: true
  cors: true
  eventlog-aspect: true
spring.profiles.default: "dev"
spring.application.name: "insight-handler"
spring.jackson:
  date-format: "yyyy-MM-dd HH:mm:ss"
  time-zone: "Asia/Shanghai"
mybatis-plus:
  mapper-locations: "classpath*:mapper/**/*.xml"
  type-aliases-package: "com.qbook.insight.**.entity"
  global-config:
    banner: false
  configuration:
    cache-enabled: true
    log-impl: "org.apache.ibatis.logging.slf4j.Slf4jImpl"
    map-underscore-to-camel-case: true
server:
  forward-headers-strategy: "framework"
  port: 8080
  servlet.context-path: "/api"
spring.mvc.pathmatch.matching-strategy: "ant_path_matcher"
security:
  permit-all-uris:
  - "/auth/login"
  - "/auth/logout"
  - "/wx/callback"
  - "/wx/qrcode"
  - "/wx/check/**"
  - "/wxPay/wxPayCode/**"
  - "/wxPay/oauth2Callback"
  - "/open/**"
