package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 房产税
 *
 * <AUTHOR>
 */
@Data
public class PropertyTax {

  @ApiModelProperty("序号")
  private Integer index;

  @ApiModelProperty("年份")
  private Integer year;

  @ApiModelProperty("土地原值、改扩建费用未计入房产原值而少申报税款(元)")
  private BigDecimal landValueOmission;

  @ApiModelProperty("从租从价计算错误而少申报税款(元)")
  private BigDecimal calculationError;

  @ApiModelProperty("无偿使用关联方房产未代扣代缴税款(元)")
  private BigDecimal unpaidRelatedPartyRent;
}
