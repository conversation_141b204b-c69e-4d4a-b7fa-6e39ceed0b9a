package com.qbook.insight.handler.service.impl;

import cn.hutool.json.JSONUtil;
import com.qbook.insight.common.entity.account.AccountBook;
import com.qbook.insight.handler.service.AccountBookService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 账套数据相关接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AccountBookServiceImpl implements AccountBookService {

  @Override
  public void upload(String datasrc, String taxid, String year, AccountBook book) {
    log.info(
        "[账套上传] datasrc:{} taxid:{} year:{} book:{}",
        datasrc,
        taxid,
        year,
        JSONUtil.toJsonStr(book));
  }
}
