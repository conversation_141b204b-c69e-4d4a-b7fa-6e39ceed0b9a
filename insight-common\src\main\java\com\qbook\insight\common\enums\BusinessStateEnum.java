package com.qbook.insight.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 经营状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessStateEnum {
  UNKNOWN("未知", -1),
  IN_OPERATION("在营", 0),
  OPENED("开业", 1),
  REGISTERED("在册", 2),
  LOGOUT("注销", 3);

  @Getter private final String desc;
  private final int code;

  /** 根据描述获取编码 */
  public static int getCodeByDesc(String desc) {
    for (BusinessStateEnum state : values()) {
      if (state.getDesc().equals(desc)) {
        return state.getCode();
      }
    }
    return UNKNOWN.getCode();
  }

  /** 是否为有效状态 */
  public static boolean isValid(String desc) {
    return !UNKNOWN.getDesc().equals(desc);
  }
}
