package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 收入调增事项
 *
 * <AUTHOR>
 */
@Data
public class IncomeAdjustments {

  @ApiModelProperty("自产产品、财产用于交换、捐赠、偿债、赞助、集资、广告、样品、职工福利视同销售收入(元)")
  private Integer deemedSalesIncome;

  @ApiModelProperty("下脚料、包装物未确认收入(元)")
  private BigDecimal unrecordedScrapIncome;

  @ApiModelProperty("未按权责发生制确认收到的租金、利息等收入(元)")
  private BigDecimal unrecordedRentInterestIncome;

  @ApiModelProperty("预收账款未及时确认收入(元)")
  private BigDecimal unrecordedAdvanceReceipts;

  @ApiModelProperty("资金占用视同收入(元)")
  private BigDecimal deemedInterestIncome;
}
