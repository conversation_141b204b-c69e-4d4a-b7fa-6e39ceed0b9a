package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 消费税
 *
 * <AUTHOR>
 */
@Data
public class ConsumptionTax {

  @ApiModelProperty("序号")
  private Integer index;

  @ApiModelProperty("年份")
  private Integer year;

  @ApiModelProperty("税率引用错误少申报税款(元)")
  private BigDecimal incorrectTaxRate;

  @ApiModelProperty("赠品未视同销售少申报税款(元)")
  private BigDecimal unrecordedGifts;
}
