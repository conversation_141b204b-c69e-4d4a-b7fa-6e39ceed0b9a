package com.qbook.insight.common.vo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 用户信息返回对象
 *
 * <AUTHOR>
 */
@Data
public class UserInfoVO {

  @ApiModelProperty("用户名")
  private String username;

  @ApiModelProperty("用户昵称")
  private String realName;

  @ApiModelProperty("头像")
  private String avatar;

  @ApiModelProperty("性别(1:男 2:女)")
  private Integer sex;

  @ApiModelProperty("城市")
  private String city;

  @ApiModelProperty("省份")
  private String province;

  @ApiModelProperty("账号创建时间")
  private Date createdAt;

  @ApiModelProperty("最近登录时间")
  private Date loginTime;

  @ApiModelProperty("最近退出时间")
  private Date logoutTime;
}
