package com.qbook.insight.common.entity;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公司实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Corp extends BaseEntity {

  @ApiModelProperty("公司名称")
  private String name;

  @ApiModelProperty("社会信用代码(税号)")
  private String taxId;

  @ApiModelProperty("公司地址")
  private String address;

  @ApiModelProperty("注册日期")
  private Date eastabDate;

  @ApiModelProperty("经营状态")
  private Integer state;

  @ApiModelProperty("注册资本")
  private Integer registerCapital;

  @ApiModelProperty("实缴资本")
  private Integer paidInCapital;

  @ApiModelProperty("所属行业")
  private String sector;
}
