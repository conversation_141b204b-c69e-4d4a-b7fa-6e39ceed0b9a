package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 销项税额方面
 *
 * <AUTHOR>
 */
@Data
public class OutputTax {

  @ApiModelProperty("预收款挂账未确认收入少申报税款(元)")
  private BigDecimal unrecordedAdvanceReceipts;

  @ApiModelProperty("下脚料收入未确认收入申报纳税(元)")
  private BigDecimal unrecordedScrapIncome;

  @ApiModelProperty("视同销售未按规定申报纳税(元)")
  private BigDecimal unrecordedDeemedSales;

  @ApiModelProperty("价外收入未按规定并入销售额申报纳税(元)")
  private BigDecimal unrecordedSurcharges;

  @ApiModelProperty("销售折扣折让处理不当少申报税款(元)")
  private BigDecimal incorrectDiscounts;

  @ApiModelProperty("混合销售行为税率选择不当少申报纳税(元)")
  private BigDecimal incorrectTaxRate;

  @ApiModelProperty("资金占用视同销售少申报纳税(元)")
  private BigDecimal deemedInterestSales;

  @ApiModelProperty("开具红字发票清单")
  private List<Object> creditNoteList;
}
