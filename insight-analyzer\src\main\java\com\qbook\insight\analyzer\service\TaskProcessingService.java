package com.qbook.insight.analyzer.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qbook.insight.analyzer.entity.AnalysisTemplate;
import com.qbook.insight.analyzer.mapper.AnalysisTemplateMapper;
import java.util.ArrayList;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 任务处理服务
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-04 14:17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TaskProcessingService {

  private final TaskAnalysisService analysisTaskService;
  private final AnalysisTemplateMapper analysisTemplateMapper;

  public void processTask(String data) {
    ObjectMapper objectMapper = new ObjectMapper();
    try {
      //  获取模板名称
      JsonNode jsonNode = objectMapper.readTree(data);
      String template = jsonNode.get("template").asText();
      String analyzeData = jsonNode.get("data").asText();

      //  获取模板
      // AnalysisTemplate analysisTemplate = analysisTemplateMapper.selectByName(template);
      // if (Objects.isNull(analysisTemplate)) {
      //     throw new IllegalArgumentException("未找到分析模板: " + analysisTemplate);
      // }

      // --------------测试数据---------------
      ArrayList<String> objects = new ArrayList<>();
      objects.add("InvoicePurchaseAnalysisStrategy");
      objects.add("InvoiceSalesAnalysisStrategy");
      AnalysisTemplate analysisTemplate =
          new AnalysisTemplate("Invoice", "发票分析", new ArrayList<>(objects));
      if (!Objects.equals(analysisTemplate.getName(), template)) {
        throw new IllegalArgumentException("未找到分析模板: " + template);
      }
      // --------------测试数据---------------

      // 执行模板中的每个策略
      for (String type : analysisTemplate.getStrategyTypes()) {
        log.info("开始分析");
        // 异步提交 + 自动重试
        analysisTaskService.executeAnalysisTask(analyzeData, type);
      }
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }
}
