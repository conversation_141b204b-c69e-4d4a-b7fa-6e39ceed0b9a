package com.qbook.insight.analyzer.entity;

import com.qbook.insight.analyzer.constant.AnalysisTaskStatus;
import com.qbook.insight.common.entity.BaseEntity;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 任务分析实体类
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-06 10:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class AnalysisTask extends BaseEntity {

  // 任务ID（对应Redis消息）
  private String taskId;

  // 当前分析策略类型
  private String strategyType;

  // 任务状态：PENDING, RUNNING, SUCCESS, FAILED, RETRYING
  private String status;

  // 错误信息
  private String errorMessage;

  // 开始时间
  private Date startTime;

  // 结束时间
  private Date endTime;

  // 重试次数
  private Integer retryCount;

  // 最大重试次数
  private Integer maxRetries = 3;

  // 是否完成
  public boolean isCompleted() {
    return AnalysisTaskStatus.SUCCESS.equals(status) || AnalysisTaskStatus.FAILED.equals(status);
  }
}
