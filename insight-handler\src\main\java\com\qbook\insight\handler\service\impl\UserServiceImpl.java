package com.qbook.insight.handler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qbook.insight.common.constant.RMsg;
import com.qbook.insight.common.entity.User;
import com.qbook.insight.common.exception.BizException;
import com.qbook.insight.common.mapper.UserMapper;
import com.qbook.insight.common.util.JwtUtil;
import com.qbook.insight.common.util.SecurityUtil;
import com.qbook.insight.common.util.StringUtils;
import com.qbook.insight.common.vo.UserInfoVO;
import com.qbook.insight.handler.service.UserService;
import com.qbook.insight.handler.vo.LoginParam;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 用户操作
 *
 * <AUTHOR>
 */
@Service
public class UserServiceImpl implements UserService {

  @Resource private UserMapper userMapper;
  @Resource private PasswordEncoder passwordEncoder;
  @Resource private JwtUtil jwtUtil;

  @Override
  public User login(LoginParam param) {
    if (param.getUsername() == null || param.getPassword() == null) {
      throw new BizException(RMsg.ERR_NO_USERNAME_PASSWORD);
    }

    LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(User::getUsername, param.getUsername());
    User user = userMapper.selectOne(wrapper);
    if (user == null) {
      throw new BizException(RMsg.ERR_NO_USER);
    }

    if (!passwordEncoder.matches(param.getPassword(), user.getPassword())) {
      throw new BizException(RMsg.ERR_PASSWORD);
    }

    user.setPassword(null);
    SecurityUtil.setCurrentUser(user);
    user.setAccessToken(jwtUtil.generateToken(user));
    return user;
  }

  @Override
  public User getUserInfo() {
    return SecurityUtil.getCurrentUser();
  }

  @Override
  public UserInfoVO getUserDetailInfo() {
    User fullUserInfo = userMapper.selectById(getUserId());
    if (fullUserInfo == null) {
      return null;
    }

    UserInfoVO vo = new UserInfoVO();
    BeanUtils.copyProperties(fullUserInfo, vo);
    return vo;
  }

  @Override
  public Long getUserId() {
    return getUserInfo().getId();
  }

  @Override
  public String[] getRoles() {
    String roles = getUserInfo().getRoles();
    if (roles == null || roles.isEmpty()) {
      return new String[] {};
    } else {
      return roles.split(",");
    }
  }

  @Override
  public boolean hasAnyRoles(String... roles) {
    for (String role : getRoles()) {
      if (!StringUtils.isEmpty(role) && StringUtils.containsAny(role, roles)) {
        return true;
      }
    }
    return false;
  }

  @Override
  public void logout(HttpServletRequest request) {
    String token = jwtUtil.getToken(request);
    if (token != null) {
      User user = jwtUtil.getUser(token);
      if (user != null) {
        SecurityUtil.setCurrentUser(user);
      }
    }
  }

  @Override
  public int add(User user) {
    user.setPassword(passwordEncoder.encode(user.getPassword()));
    if (user.getRealName() == null) {
      user.setRealName(user.getUsername());
    }
    return userMapper.insert(user);
  }
}
