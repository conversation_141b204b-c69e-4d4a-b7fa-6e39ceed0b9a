package com.qbook.insight.analyzer.stratetgy;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * 分析策略环境类
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-05-29 17:50
 */
@Slf4j
public class AnalysisStrategyManager {

  // 存放策略
  private final Map<String, AnalysisStrategy> strategyMap = new HashMap<>();

  // 1.初始化bean方法
  public AnalysisStrategyManager(Set<AnalysisStrategy> analyzeStrategies) {
    // 初始化-自动注册所有策略实现
    for (AnalysisStrategy strategy : analyzeStrategies) {
      strategyMap.put(strategy.getStrategyType(), strategy);
    }
  }

  // 2.获取策略
  public AnalysisStrategy getStrategy(String strategyType) {
    AnalysisStrategy analyzeStrategy = strategyMap.get(strategyType);
    if (Objects.isNull(analyzeStrategy)) {
      throw new IllegalArgumentException("未知的分析策略类型: " + strategyType);
    }
    return analyzeStrategy;
  }

  // 3.执行策略
  public void executeStrategy(String data, String strategyType) {
    AnalysisStrategy analyzeStrategy = getStrategy(strategyType);
    analyzeStrategy.analyze(data);
  }

  // // 获取strategyMap的长度
  // public int getStrategyMapSize() {
  //   return strategyMap.size();
  // }
  //
  // // 获取strategyMap中的键值
  // public Set<String> getStrategyMapKeys() {
  //   return strategyMap.keySet();
  // }
}
