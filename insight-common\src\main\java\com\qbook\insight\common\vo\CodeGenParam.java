package com.qbook.insight.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 代码生成参数
 *
 * <AUTHOR>
 */
@Data
public class CodeGenParam {

  @ApiModelProperty("表名")
  private String tableName;

  @ApiModelProperty("实体类名称")
  private String className;

  @ApiModelProperty("实体类中文名称")
  private String classChineseName;

  @ApiModelProperty("包路径")
  private String packagePath;

  @ApiModelProperty("作者")
  private String author;
}
