package com.qbook.insight.collector.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.qbook.insight.collector.constant.MqTaskStatus;
import com.qbook.insight.collector.domian.TaskReturnData;
import com.qbook.insight.common.entity.CollectorTask;
import com.qbook.insight.common.mapper.CollectorTaskMapper;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 处理收集数据服务
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-21 下午3:34
 */
@Service
@Slf4j
public class HandleCollectDataService {
  @Resource private CollectorTaskMapper collectorTaskMapper;
  @Resource private HandleInvoiceService handleInvoiceService;

  /** 处理收集数据 */
  public void handleCollectData(String result) {
    try {
      TaskReturnData taskReturnData = JSONUtil.toBean(result, TaskReturnData.class);
      if (taskReturnData == null) {
        log.warn("任务数据为空");
        return;
      }
      // 查询任务
      CollectorTask collectorTask =
          collectorTaskMapper.selectOne(
              new LambdaQueryWrapper<CollectorTask>()
                  .eq(CollectorTask::getTaskId, taskReturnData.getTaskId()));

      if (collectorTask == null) {
        log.warn("任务不存在，taskId: {}", taskReturnData.getTaskId());
        return;
      }
      // 幂等性判断
      if (collectorTask.getStatus() != null && collectorTask.getStatus() == 2) {
        log.warn("任务已处理完成，taskId: {}", taskReturnData.getTaskId());
        return;
      }
      // 解析任务内容
      handleInvoiceService.processTaskResult(taskReturnData);

      // 更新任务状态
      collectorTask.setStatus(MqTaskStatus.SUCCESS);
      collectorTask.setTaskFinish(DateUtil.parse(DateUtil.now()));
      LambdaUpdateWrapper<CollectorTask> updateWrapper = new LambdaUpdateWrapper<>();
      updateWrapper.eq(CollectorTask::getTaskId, taskReturnData.getTaskId());
      collectorTaskMapper.update(collectorTask, updateWrapper);
    } catch (Exception e) {
      throw new RuntimeException("任务处理失败", e);
    }
  }

  /** 处理报错消息 */
  public void handleErrorCollectData(String result, Exception errorMsg) {
    TaskReturnData data = JSONUtil.toBean(result, TaskReturnData.class);

    CollectorTask collectorTask = buildCollectorTask(data);
    // 更新任务
    collectorTask.setStatus(MqTaskStatus.FAIL);
    collectorTask.setMessage(errorMsg.toString());
    LambdaUpdateWrapper<CollectorTask> updateWrapper = new LambdaUpdateWrapper<>();
    updateWrapper.eq(CollectorTask::getTaskId, data.getTaskId());
    collectorTaskMapper.update(collectorTask, updateWrapper);
  }

  /** 构建数据采集任务数据 */
  private CollectorTask buildCollectorTask(TaskReturnData data) {
    CollectorTask collectorTask = new CollectorTask();
    collectorTask.setTaskId(data.getTaskId());
    collectorTask.setTaxId(data.getTaxId());
    collectorTask.setCollectorType(data.getTaskType());
    collectorTask.setTaskFinish(DateUtil.parse(DateUtil.now()));
    return collectorTask;
  }
}
