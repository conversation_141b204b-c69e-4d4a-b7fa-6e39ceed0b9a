<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.qbook.insight</groupId>
    <artifactId>insight-parent</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>insight-generator</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
    </dependency>

    <dependency>
      <groupId>com.qbook.insight</groupId>
      <artifactId>insight-common</artifactId>
    </dependency>

    <dependency>
      <groupId>fr.opensagres.xdocreport</groupId>
      <artifactId>org.apache.poi.xwpf.converter.core</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.xmlgraphics</groupId>
      <artifactId>fop</artifactId>
    </dependency>

    <dependency>
      <groupId>org.docx4j</groupId>
      <artifactId>docx4j-core</artifactId>
    </dependency>

    <dependency>
      <groupId>org.docx4j</groupId>
      <artifactId>docx4j-export-fo</artifactId>
    </dependency>

    <dependency>
      <groupId>org.docx4j</groupId>
      <artifactId>docx4j-JAXB-Internal</artifactId>
    </dependency>

    <dependency>
      <groupId>org.docx4j</groupId>
      <artifactId>docx4j-JAXB-ReferenceImpl</artifactId>
    </dependency>

    <dependency>
      <groupId>org.jfree</groupId>
      <artifactId>jfreechart</artifactId>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-log4j2</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
