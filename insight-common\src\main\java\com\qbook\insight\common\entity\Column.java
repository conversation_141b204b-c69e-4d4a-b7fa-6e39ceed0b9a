package com.qbook.insight.common.entity;

import lombok.Data;

/**
 * 列名实体类
 *
 * <AUTHOR>
 */
@Data
public class Column {

  // 列名称
  private String columnName;

  // 是否必填
  private boolean isRequired;

  // 是否为主键
  private boolean isPrimaryKey;

  // 序号
  private int sort;

  // 列描述
  private String columnComment;

  // 是否自增
  private boolean isAutoIncrement;

  // 列类型
  private String columnType;

  // Java字段类型
  private String javaType;

  // Java字段名
  private String javaField;
}
