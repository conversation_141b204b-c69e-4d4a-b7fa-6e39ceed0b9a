package com.qbook.insight.common.util;

import com.qbook.insight.common.entity.User;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * 安全服务工具类
 *
 * <AUTHOR>
 */
public class SecurityUtil {

  // 设置当前用户
  public static void setCurrentUser(User user) {
    Authentication auth =
        new UsernamePasswordAuthenticationToken(
            user, null, AuthorityUtils.commaSeparatedStringToAuthorityList(user.getRoles()));
    SecurityContextHolder.getContext().setAuthentication(auth);
  }

  // 获取当前用户
  public static User getCurrentUser() {
    try {
      return (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    } catch (Exception e) {
      return null;
    }
  }
}
