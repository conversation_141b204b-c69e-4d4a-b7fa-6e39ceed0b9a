package com.qbook.insight.collector.util;

import com.google.common.annotations.Beta;
import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import java.nio.charset.Charset;

/**
 * 布隆过滤器工具类 此类使用了Guava的Beta API
 *
 * <AUTHOR>
 */
@Beta
public class BloomFilterUtil {

  /**
   * 创建字符串类型的布隆过滤器
   *
   * @param expectedInsertions 预期插入元素数量
   * @param fpp 误判率
   * @return BloomFilter实例
   */
  @SuppressWarnings("UnstableApiUsage")
  public static BloomFilter<String> createStringBloomFilter(long expectedInsertions, double fpp) {
    return BloomFilter.create(
        Funnels.stringFunnel(Charset.defaultCharset()), expectedInsertions, fpp);
  }
}
