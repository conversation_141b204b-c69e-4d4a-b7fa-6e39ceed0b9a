package com.qbook.insight.common.vo;

import com.qbook.insight.common.entity.Report;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户VO（包含报告信息）
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientReportVO extends ClientVO {

  @ApiModelProperty("报告列表")
  private List<Report> reports = new ArrayList<>();
}
