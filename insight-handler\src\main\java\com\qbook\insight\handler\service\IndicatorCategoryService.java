package com.qbook.insight.handler.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qbook.insight.common.entity.IndicatorCategory;
import com.qbook.insight.common.entity.IndicatorConfig;
import com.qbook.insight.common.vo.IndicatorVo;
import com.qbook.insight.common.vo.PageParam;
import java.util.List;

/**
 * 指标分类配置接口
 *
 * <AUTHOR>
 */
public interface IndicatorCategoryService {

  /** 添加数据 */
  int add(IndicatorCategory category);

  /** 删除数据 */
  int delete(long id);

  /** 修改数据 */
  int update(long id, IndicatorCategory config);

  /** 获取列表 */
  List<IndicatorCategory> list(Integer level, Integer status, String name);

  /** 获取分类下的指标配置 */
  List<IndicatorConfig> getIndicatorsByCategory(long categoryId);

  /** 获取分类及其下的指标配置（分页） */
  IPage<IndicatorVo> getCategoriesWithIndicators(
      PageParam pageParam, Integer level, Integer status, String name);
}
