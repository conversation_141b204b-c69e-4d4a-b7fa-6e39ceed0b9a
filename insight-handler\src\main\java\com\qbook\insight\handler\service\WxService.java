package com.qbook.insight.handler.service;

import com.qbook.insight.common.entity.User;
import java.util.Map;
import org.springframework.stereotype.Service;

/** <AUTHOR> */
@Service
public interface WxService {

  /** 微信公众号Get回调 */
  String wxGetNotify(String signature, String timestamp, String nonce, String echo);

  /** 微信公众号Post回调 */
  String wxPostCallback(Map<Object, Object> map);

  /** 获取二维码地址和生成的uuid（用户点击“扫码登录”后调用） */
  Map<String, String> getQrCode();

  /** 展示二维码后，页面需要轮询调用此请求，判定用户是否扫描并关注 */
  User login(String qrId);
}
