package com.qbook.insight.handler.entity;

import com.qbook.insight.common.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会员套餐实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MemberPackage extends BaseEntity {

  @ApiModelProperty("套餐名称")
  private String name;

  @ApiModelProperty("价格（元）")
  private BigDecimal price;

  @ApiModelProperty("原价（元）")
  private BigDecimal originalPrice;

  @ApiModelProperty("有效期（月）")
  private Integer duration;

  @ApiModelProperty("报告创建次数")
  private Integer reportCount;

  @ApiModelProperty("功能列表：create_report,download_report,data_export")
  private String features;

  @ApiModelProperty("套餐描述")
  private String description;

  @ApiModelProperty("是否为推荐套餐：0-否，1-是")
  private Integer isPopular;

  @ApiModelProperty("是否启用：0-禁用，1-启用")
  private Integer isActive;
}
