package com.qbook.insight.generator.utill;

import com.qbook.insight.generator.utill.WordTemplateToPdfUtil.*;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/** <AUTHOR> */
@Slf4j
class WordTemplateToPdfUtilTest {

  @Test
  void generatePdfFromTemplate() {
    try {
      log.debug("开始执行PDF生成示例...");

      // 1. 创建文本占位符数据
      Map<String, Object> textPlaceholders = new HashMap<>();
      textPlaceholders.put("company", "示例科技有限公司Baidu");
      textPlaceholders.put(
          "code", TextStyle.create("91110105XXXXXXXXXX").withHyperlink("https://www.baidu.com/"));
      textPlaceholders.put("address", "杭州市余杭区XX路XX号");
      textPlaceholders.put("phone", "010-12345678");
      textPlaceholders.put(
          "legal_representative",
          TextStyle.create("张三").withColor(Colors.RED).withHyperlink("https://www.baidu.com/"));
      textPlaceholders.put("report_date", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
      textPlaceholders.put("total_tax", TextStyle.create("2,450,000.00").withColor(Colors.BLUE));
      textPlaceholders.put("risk_level", TextStyle.create("中风险").withColor(Colors.MEDIUM_RISK));

      // 2. 创建表格数据
      Map<String, TableData> tablePlaceholders = new HashMap<>();

      // 创建税收明细表
      List<List<String>> taxTable = new ArrayList<>();
      taxTable.add(Arrays.asList("税种", "金额", "各类基本社会保障性缴款占工资薪金支出的比例(%)各类基本社会保障性缴款占工资薪金支出的比例(%)"));
      taxTable.add(Arrays.asList("增值税", "1,200,000.00", "48.98%"));
      taxTable.add(Arrays.asList("企业所得税", "750,000.00", "30.61%"));
      taxTable.add(Arrays.asList("个人所得税", "320,000.00", "13.06%"));
      taxTable.add(Arrays.asList("其他税费", "180,000.00", "7.35%"));
      taxTable.add(Arrays.asList("合计", "2,450,000.00", "100.00%"));
      tablePlaceholders.put("tax_details", new TableData(taxTable, new double[] {0.3, 0.3, 0.6}));

      // 创建收入支出表
      List<List<String>> revenueTable = new ArrayList<>();
      revenueTable.add(Arrays.asList("季度", "收入(万元)", "支出(万元)", "利润(万元)"));
      revenueTable.add(Arrays.asList("Q1", "1000", "800", "200"));
      revenueTable.add(Arrays.asList("Q2", "1200", "900", "300"));
      revenueTable.add(Arrays.asList("Q3", "1500", "1100", "400"));
      revenueTable.add(Arrays.asList("Q4", "2000", "1500", "500"));
      revenueTable.add(Arrays.asList("Q1", "1000", "800", "200"));
      revenueTable.add(Arrays.asList("Q2", "1200", "900", "300"));
      revenueTable.add(Arrays.asList("Q3", "1500", "1100", "400"));
      revenueTable.add(Arrays.asList("Q4", "2000", "1500", "500"));
      tablePlaceholders.put("revenue_table", new TableData(revenueTable, null));

      // 3. 创建图表数据
      Map<String, ChartData> chartPlaceholders = new HashMap<>();

      // 创建税务分布饼图
      ChartData taxPieChart = new ChartData(ChartType.PIE_CHART, "税收分布情况", "", "", 400, 200);
      taxPieChart.addSeries("税种");
      taxPieChart.addDataPoint("税种", "企业所得税", 750000);
      taxPieChart.addDataPoint("税种", "个人所得税", 320000);
      taxPieChart.addDataPoint("税种", "增值税", 1200000);
      taxPieChart.addDataPoint("税种", "其他税费", 180000);
      chartPlaceholders.put("tax_pie", taxPieChart);

      // 创建月度税收柱状图
      ChartData monthlyTaxChart =
          new ChartData(ChartType.BAR_CHART, "月度税收趋势", "月份", "金额(万元)", 600, 300);
      monthlyTaxChart.addSeries("税收额");
      monthlyTaxChart.addDataPoint("税收额", "1月", 18.5);
      monthlyTaxChart.addDataPoint("税收额", "2月", 15.2);
      monthlyTaxChart.addDataPoint("税收额", "3月", 21.3);
      monthlyTaxChart.addDataPoint("税收额", "4月", 24.6);
      monthlyTaxChart.addDataPoint("税收额", "5月", 22.8);
      monthlyTaxChart.addDataPoint("税收额", "6月", 27.5);
      chartPlaceholders.put("monthly_tax", monthlyTaxChart);

      // 创建季度对比折线图
      ChartData quarterlyComparisonChart =
          new ChartData(ChartType.LINE_CHART, "季度税收对比", "季度", "金额(万元)", 700, 400);
      quarterlyComparisonChart.addSeries("2022年");
      quarterlyComparisonChart.addDataPoint("2022年", "Q1", 480.5);
      quarterlyComparisonChart.addDataPoint("2022年", "Q2", 520.3);
      quarterlyComparisonChart.addDataPoint("2022年", "Q3", 570.8);
      quarterlyComparisonChart.addDataPoint("2022年", "Q4", 610.2);

      quarterlyComparisonChart.addSeries("2023年");
      quarterlyComparisonChart.addDataPoint("2023年", "Q1", 550.0);
      quarterlyComparisonChart.addDataPoint("2023年", "Q2", 740.9);
      quarterlyComparisonChart.addDataPoint("2023年", "Q3", 680.7);
      quarterlyComparisonChart.addDataPoint("2023年", "Q4", 830.5);
      chartPlaceholders.put("quarterly_comparison", quarterlyComparisonChart);

      // 4. 生成PDF
      String templateFilePath = "/templates/report_template.docx";
      byte[] pdfBytes =
          WordTemplateToPdfUtil.generatePdfFromTemplate(
              textPlaceholders, tablePlaceholders, chartPlaceholders, templateFilePath);

      // todo 保存到文件用于测试
      String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
      String outputFileName = "tax_report_" + timestamp;
      String testOutputPath = "/tmp/" + outputFileName + ".pdf";

      try {
        File outputFile = new File(testOutputPath);
        // 确保父目录存在
        File parentDir = outputFile.getParentFile();
        if (!parentDir.exists()) {
          if (parentDir.mkdirs()) {
            log.debug("成功创建父目录: {}", parentDir.getAbsolutePath());
          } else {
            log.warn("无法创建父目录: {}", parentDir.getAbsolutePath());
          }
        }

        log.debug("开始写入PDF文件: {}", testOutputPath);
        Files.write(outputFile.toPath(), pdfBytes);
        log.info("PDF生成成功，文件保存至：{}", outputFile.getAbsolutePath());
      } catch (IOException e) {
        log.error("保存PDF文件时出错: {}", testOutputPath, e);
        throw e;
      }

    } catch (Exception e) {
      log.error("生成PDF时出错", e);
    }
  }
}
