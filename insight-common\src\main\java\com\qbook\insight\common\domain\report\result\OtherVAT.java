package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 税率及其他
 *
 * <AUTHOR>
 */
@Data
public class OtherVAT {

  @ApiModelProperty("高税率项目应用低税率项目少申报纳税(元)")
  private BigDecimal understatedHighRateItems;

  @ApiModelProperty("误用简易计税办法少申报税额(元)")
  private BigDecimal incorrectSimplifiedMethod;

  @ApiModelProperty("实际税负低于增加值税负率(元)")
  private BigDecimal lowTaxBurden;

  @ApiModelProperty("按行业平均能耗预估少计收入(元)")
  private BigDecimal energyBasedEstimate;

  @ApiModelProperty("开具零税率发票列表")
  private List<Object> zeroRateInvoices;

  @ApiModelProperty("开具免税发票列表")
  private List<Object> taxExemptInvoices;
}
