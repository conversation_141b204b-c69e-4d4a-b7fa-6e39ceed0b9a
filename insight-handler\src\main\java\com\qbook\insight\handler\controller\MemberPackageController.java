package com.qbook.insight.handler.controller;

import com.qbook.insight.common.domain.R;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.entity.MemberPackage;
import com.qbook.insight.handler.service.MemberPackageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 会员套餐相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "Member Package Operate", description = "会员套餐操作")
@RestController
@RequestMapping("/member/package")
public class MemberPackageController {

  @Resource private MemberPackageService memberPackageService;

  @ApiOperation(value = "添加会员套餐")
  @PostMapping("/add")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R add(@RequestBody MemberPackage pkg) {
    return R.ok(memberPackageService.add(pkg));
  }

  @ApiOperation(value = "删除会员套餐")
  @DeleteMapping("/{id}")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R delete(@PathVariable long id) {
    return R.ok(memberPackageService.delete(id));
  }

  @ApiOperation(value = "修改会员套餐")
  @PutMapping("/{id}")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R update(@PathVariable long id, @RequestBody MemberPackage pkg) {
    return R.ok(memberPackageService.update(id, pkg));
  }

  @ApiOperation(value = "获取会员套餐详情")
  @GetMapping("/{id}")
  public R getById(@PathVariable long id) {
    return R.ok(memberPackageService.getById(id));
  }

  @ApiOperation(value = "获取会员套餐列表")
  @GetMapping("/info")
  public R list(
      @ApiParam("套餐名称") @RequestParam(required = false) String name,
      @ApiParam("是否启用(0-禁用，1-启用)") @RequestParam(required = false) Integer isActive) {
    return R.ok(memberPackageService.list(name, isActive));
  }

  @ApiOperation(value = "获取会员套餐列表(分页)")
  @GetMapping("/page")
  public R page(
      PageParam pageParam,
      @ApiParam("套餐名称") @RequestParam(required = false) String name,
      @ApiParam("是否启用(0-禁用，1-启用)") @RequestParam(required = false) Integer isActive) {
    return R.ok(memberPackageService.page(pageParam, name, isActive));
  }

  @ApiOperation(value = "获取激活的会员套餐列表")
  @GetMapping("/plans")
  public R getActivePlans() {
    return R.ok(memberPackageService.getActivePlans());
  }
}
