package com.qbook.insight.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qbook.insight.common.constant.DsName;
import com.qbook.insight.common.entity.InvoiceSalesDetail;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 销项详情Mapper
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-24 上午12:45
 */
@Mapper
@DS(DsName.DATA)
public interface InvoiceSalesDetailMapper extends BaseMapper<InvoiceSalesDetail> {

  /** 批量查询已存在的zzfpdm+zzfphm */
  List<String> selectExistingInvoiceKeys(@Param("invoiceKeys") List<String> invoiceKeys);

  /** 批量插入进项数据 */
  int insertBatch(List<InvoiceSalesDetail> list);
}
