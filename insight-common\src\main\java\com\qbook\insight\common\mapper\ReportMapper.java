package com.qbook.insight.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qbook.insight.common.entity.Report;
import com.qbook.insight.common.vo.ReportVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 报告Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ReportMapper extends BaseMapper<Report> {

  List<Report> selectByClientIds(@Param("ids") List<Long> idList);

  Long selectCorpIdByReportId(Long reportId);

  List<ReportVO> selectReportVO(IPage<ReportVO> page, @Param("cond") ReportVO cond);
}
