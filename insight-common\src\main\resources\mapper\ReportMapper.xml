<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qbook.insight.common.mapper.ReportMapper">

  <sql id="selectReports">
    select id, client_id, corp_id, name, template_id, stage,
    progress,summary_audit_tag, detail_audit_tag,
    created_at,
    updated_at
    from report
  </sql>

  <select id="selectByClientIds"
    resultType="com.qbook.insight.common.entity.Report">
    <include refid="selectReports" />
    where client_id in
    <foreach item="id" collection="ids" open="(" separator=","
      close=")">#{id}</foreach>
  </select>

  <select id="selectCorpIdByReportId">
    select corp_id from report where id=#{reportId}
  </select>

  <select id="selectReportVO"
    resultType="com.qbook.insight.common.vo.ReportVO">
    select r.*, u.username, u.real_name, c.name as corpName, c.tax_id from
    report r, user u,
    corp c where r.user_id = u.id and r.corp_id = c.id
    <if test="cond.userId != null">
      and r.user_id = #{cond.userId}
    </if>
    <if test="cond.realName != null and cond.realName != ''">
      and u.real_name like concat('%', #{cond.realName}, '%')
    </if>
    <if test="cond.corpName != null and cond.corpName != ''">
      and c.name like concat('%', #{cond.corpName}, '%')
    </if>
    <if test="cond.taxId != null and cond.taxId != ''">
      and c.tax_id = #{cond.taxId}
    </if>
    order by r.created_at desc
  </select>
</mapper>
