package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.ResultLevel1;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 企业所得税
 *
 * <AUTHOR>
 */
@Data
public class CorporateIncomeTax {

  @ApiModelProperty("序号")
  private Integer index;

  @ApiModelProperty("年份")
  @ResultLevel1
  private Integer year;

  @ApiModelProperty("企业所得税负率")
  private BigDecimal incomeTaxBurden;

  @ApiModelProperty("所得税负率行业均值")
  private BigDecimal industryIncomeTaxBurden;

  @ApiModelProperty("收入调增事项")
  private IncomeAdjustments incomeAdjustments;

  @ApiModelProperty("收入调减事项")
  private IncomeDeductions incomeDeductions;

  @ApiModelProperty("扣除类调整事项(调增项)")
  private ExpenseAdjustments expenseAdjustments;

  @ApiModelProperty("扣除类调整事项(调减项)")
  private ExpenseDeductions expenseDeductions;

  @ApiModelProperty("资产类调整事项")
  private AssetAdjustments assetAdjustments;

  @ApiModelProperty("特殊事项调整项目")
  private SpecialAdjustments specialAdjustments;
}
