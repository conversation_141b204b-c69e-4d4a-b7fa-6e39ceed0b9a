# Nginx 配置

```
# Insight site

server {
  listen 80;
  server_name _;

  charset utf-8;
  error_page 404 /404.html;
  error_page 500 502 503 504 /50x.html;

  proxy_set_header Host $host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;

  # 前端页面
  location / {
    root /home/<USER>/insight/insight-frontend;
    index index.html index.htm;
  }

  # Handler服务接口
  location /api/ {
    proxy_pass http://localhost:8080;
  }
}
```
