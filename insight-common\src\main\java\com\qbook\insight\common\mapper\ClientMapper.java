package com.qbook.insight.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qbook.insight.common.entity.Client;
import com.qbook.insight.common.vo.ClientReportVO;
import com.qbook.insight.common.vo.ClientVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 客户Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ClientMapper extends BaseMapper<Client> {

  List<ClientReportVO> selectClientVO(IPage<ClientReportVO> page, @Param("cond") ClientVO cond);
}
