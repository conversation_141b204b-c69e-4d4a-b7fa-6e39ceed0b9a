package com.qbook.insight.handler.domain;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

/**
 * 微信公众号响应XML-回调响应内容
 *
 * <AUTHOR>
 */
@Data
@JacksonXmlRootElement(localName = "xml")
public class WxResponseXML {

  /** 开发者微信号 */
  @JacksonXmlProperty(localName = "FromUserName")
  private String fromUserName;

  /** 接收方帐号（收到的OpenID） */
  @JacksonXmlProperty(localName = "ToUserName")
  private String toUserName;

  /** 消息创建时间 */
  @JacksonXmlProperty(localName = "CreateTime")
  private Long createTime;

  /** 消息类型 */
  @JacksonXmlProperty(localName = "MsgType")
  private String msgType;

  /** 事件类型 */
  @JacksonXmlProperty(localName = "Event")
  private String event;

  /** 事件KEY值，获取二维码时的scene_id */
  @JacksonXmlProperty(localName = "EventKey")
  private String eventKey;

  /** 二维码的Ticket */
  @JacksonXmlProperty(localName = "Ticket")
  private String ticket;
}
