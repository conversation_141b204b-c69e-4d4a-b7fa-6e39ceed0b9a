package com.qbook.insight.handler.controller;

import com.qbook.insight.common.domain.R;
import com.qbook.insight.common.entity.IndicatorConfig;
import com.qbook.insight.handler.service.IndicatorConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 指标计算配置接口
 *
 * <AUTHOR>
 */
@Api(tags = "IndicatorConfig", description = "指标计算配置")
@RestController
@RequestMapping("/indicator_config")
public class IndicatorConfigController {

  @Resource private IndicatorConfigService indicatorConfigService;

  @PostMapping("/add")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R add(@RequestBody IndicatorConfig indicatorConfig) {
    return R.affect(indicatorConfigService.add(indicatorConfig));
  }

  @DeleteMapping("/{id}")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R delete(@PathVariable long id) {
    return R.affect(indicatorConfigService.delete(id));
  }

  @PutMapping("/{id}")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R update(@PathVariable long id, @RequestBody IndicatorConfig indicatorConfig) {
    return R.affect(indicatorConfigService.update(id, indicatorConfig));
  }

  @GetMapping("/list")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R list(
      @ApiParam("是否启用(0-否，1-是)") @RequestParam(required = false) Integer status,
      @ApiParam("指标编码") @RequestParam(required = false) String indicatorCode,
      @ApiParam("指标名称") @RequestParam(required = false) String indicatorName) {
    return R.ok(indicatorConfigService.list(status, indicatorName, indicatorCode));
  }

  @ApiOperation(value = "指标测试")
  @PostMapping("/test")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R test(
      @ApiParam("指标配置") @RequestBody IndicatorConfig indicatorConfig,
      @ApiParam("税号") @RequestParam String taxId,
      @ApiParam("查询条件(JSON结构)") @RequestBody(required = false) Map<String, Object> queryParams) {
    return R.ok(indicatorConfigService.test(indicatorConfig, taxId, queryParams));
  }
}
