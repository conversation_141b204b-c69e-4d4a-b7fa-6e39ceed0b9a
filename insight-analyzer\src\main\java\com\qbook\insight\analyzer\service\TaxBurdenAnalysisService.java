package com.qbook.insight.analyzer.service;

import com.qbook.insight.analyzer.vo.taxBurden.TaxStatusVo;

/**
 * 税负分析服务
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-03 16:12
 */
public interface TaxBurdenAnalysisService {

  /**
   * 获取税负分析
   *
   * @param companyId 公司id
   * @param period 时间周期（如 "2025Q1" 表示 2025 年第一季度）
   */
  TaxStatusVo getTaxBurdenAnalysis(String companyId, String period);
}
