<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qbook.insight.common.mapper.TableMapper">

  <sql id="selectTableSqlId">
    select table_name, table_comment, create_time
    from information_schema.tables
    where table_schema = (select database()) and table_name not like 'qrtz_%'
  </sql>

  <select id="selectList">
    <include refid="selectTableSqlId" />
  </select>
</mapper>
