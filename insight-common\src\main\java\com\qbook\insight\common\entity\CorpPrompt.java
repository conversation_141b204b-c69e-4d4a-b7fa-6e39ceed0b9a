package com.qbook.insight.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 公司基本信息-提示模板实体类
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-01 上午10:57
 */
@Data
public class CorpPrompt {
  @TableId(type = IdType.AUTO)
  @ApiModelProperty("公司基本信息ID")
  private Long id;

  @ApiModelProperty("税号")
  private String taxId;

  @ApiModelProperty("公司名称")
  private String name;
}
