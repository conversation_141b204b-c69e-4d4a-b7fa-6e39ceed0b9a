package ${packagePath}.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import ${packagePath}.entity.${className};
import ${packagePath}.mapper.${className}Mapper;
import ${pageParamClassName};
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

#if(!${classChineseName})
  #set($classChineseName=${className})
#end
/**
 * ${classChineseName}操作
 *
 * <AUTHOR>
 */
@Service
public class ${className}Service {

#set($mapper="${classInstanceName}Mapper")
  @Resource private ${className}Mapper ${mapper};

  public int add(${className} ${classInstanceName}) {
    return ${mapper}.insert(${classInstanceName});
  }

  public int delete(long id) {
    return ${mapper}.deleteById(id);
  }

  public int update(long id, ${className} ${classInstanceName}) {
    ${classInstanceName}.setId(id);
    return ${mapper}.updateById(${classInstanceName});
  }

  public List<${className}> list() {
    return ${mapper}.selectList(null);
  }

  public IPage<${className}> page(PageParam pageParam) {
    IPage<${className}> page = new Page<>(pageParam.getPage(), pageParam.getPageSize());
    return ${mapper}.selectPage(page, null);
  }
}
