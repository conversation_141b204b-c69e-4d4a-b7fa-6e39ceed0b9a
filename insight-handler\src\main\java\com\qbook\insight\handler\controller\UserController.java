package com.qbook.insight.handler.controller;

import com.qbook.insight.common.annotation.EventLog;
import com.qbook.insight.common.domain.R;
import com.qbook.insight.common.entity.User;
import com.qbook.insight.handler.service.UserService;
import com.qbook.insight.handler.vo.LoginParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "User Operate", description = "用户操作")
@RestController
public class UserController {

  @Resource private UserService userService;

  @ApiOperation(value = "登录")
  @PostMapping("/auth/login")
  @EventLog(name = "登录")
  public R login(@RequestBody LoginParam param) {
    return R.ok(userService.login(param));
  }

  @ApiOperation(value = "获取当前用户信息")
  @GetMapping("/user/info")
  public R getUserInfo() {
    return R.ok(userService.getUserInfo());
  }

  @ApiOperation(value = "获取当前用户详细信息")
  @GetMapping("/user/detail")
  public R getUserDetailInfo() {
    return R.ok(userService.getUserDetailInfo());
  }

  @ApiOperation(value = "获取权限码")
  @GetMapping("/auth/codes")
  public R getAccessCodes() {
    return R.ok(userService.getRoles());
  }

  @ApiOperation(value = "退出登录")
  @PostMapping("/auth/logout")
  @EventLog(name = "退出登录")
  public R logout(HttpServletRequest request) {
    userService.logout(request);
    return R.ok();
  }

  @ApiOperation(value = "获取菜单")
  @GetMapping("/menu/all")
  public R getAllMenu() {
    return R.ok();
  }

  @ApiOperation(value = "添加用户", hidden = true)
  @PostMapping("/add")
  public R add(@RequestBody User user) {
    return R.ok(userService.add(user));
  }
}
