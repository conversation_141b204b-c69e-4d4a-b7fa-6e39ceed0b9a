package com.qbook.insight.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class User extends BaseEntity {

  @ApiModelProperty("用户名")
  private String username;

  @ApiModelProperty("密码")
  private String password;

  @ApiModelProperty("用户昵称")
  private String realName;

  @ApiModelProperty("用户角色")
  private String roles;

  @ApiModelProperty("头像")
  private String avatar;

  @ApiModelProperty("性别(1:男 2:女)")
  private Integer sex;

  @ApiModelProperty("微信openid")
  private String wxOpenid;

  @ApiModelProperty("城市")
  private String city;

  @ApiModelProperty("省份")
  private String province;

  // Access Token
  @TableField(exist = false)
  private String accessToken;
}
