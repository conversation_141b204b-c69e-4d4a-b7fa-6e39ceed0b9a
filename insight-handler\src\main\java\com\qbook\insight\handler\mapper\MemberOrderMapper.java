package com.qbook.insight.handler.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qbook.insight.handler.entity.MemberOrder;
import com.qbook.insight.handler.vo.MemberOrderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberOrderMapper extends BaseMapper<MemberOrder> {

  IPage<MemberOrderVO> pageVO(
      IPage<MemberOrderVO> page,
      @Param("userId") Long userId,
      @Param("status") String status,
      @Param("paymentMethod") String paymentMethod);
}
