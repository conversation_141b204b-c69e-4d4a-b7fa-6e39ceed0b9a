package com.qbook.insight.handler.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.qbook.insight.common.constant.RMsg;
import com.qbook.insight.common.exception.BizException;
import com.qbook.insight.common.util.HttpClient;
import com.qbook.insight.common.util.HttpClient.Response;
import com.qbook.insight.common.util.XmlUtils;
import com.qbook.insight.handler.config.WxConfig;
import com.qbook.insight.handler.domain.WxResponseXML;
import com.qbook.insight.handler.domain.WxTicket;
import com.qbook.insight.handler.domain.WxToken;
import com.qbook.insight.handler.domain.WxUserInfo;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 微信工具
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class WxUtils {

  @Resource private WxConfig wxConfig;

  /** UTF_8 字符集 */
  private static final Charset UTF_8 = StandardCharsets.UTF_8;

  private byte[] aesKey;

  @PostConstruct
  public void init() {
    aesKey = Base64.decode(wxConfig.getAesKey());
  }

  /** 1、获取access_token */
  public String getAccessTokenUrl(String appId, String appSecret) {
    return String.format(
        wxConfig.getGetAccessTokenUrl().concat("?grant_type=client_credential&appid=%s&secret=%s"),
        appId,
        appSecret);
  }

  /** 2、获取创建二维码ticket */
  public String getTicketUrl(String accessToken) {
    return String.format(wxConfig.getGetTicketUrl().concat("?access_token=%s"), accessToken);
  }

  /** 3、创建二维码 */
  public String getQrCodeUrl(String ticket) {
    return String.format(wxConfig.getGetQrCodeUrl().concat("?ticket=%s"), ticket);
  }

  /** 根据openid获取用户信息 */
  public String getUserInfoUrl(String accessToken, String openId) {
    return String.format(
        wxConfig.getGetUserinfoUrl().concat("?access_token=%s&openid=%s"), accessToken, openId);
  }

  /** 获取Oauth2认证 */
  public String getOauth2Url(String appId, String redirectUrl, String state) {
    return String.format(
        wxConfig
            .getGetOauth2Url()
            .concat(
                "?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_base&state=%s#wechat_redirect"),
        appId,
        redirectUrl,
        state);
  }

  /** 获取openid */
  public String getOpenIdUrl(String code) {
    return String.format(
        wxConfig
            .getGetOpenidUrl()
            .concat("?appid=%s&secret=%s&code=%s&grant_type=authorization_code"),
        // 正式的appid和secret
        // wxConfig.getAppId(),
        // wxConfig.getAppSecret(),

        // 测试号的appid和secret
        "wxf190178e0974411a",
        "b9232bf35ccb573fd6e706990116c287",
        code);
  }

  /**
   * 获取不同密钥模式的返回内容
   *
   * @return 密钥的返回内容
   */
  @SuppressWarnings("unused")
  public Map<String, String> getSecret(Map<Object, Object> response) {
    // 判断密钥模式
    if ("plain".equals(wxConfig.getEncryptionMode())) {
      // 获取明文模式下的密钥
      return getPlainSecret(response);
    } else if ("aes".equals(wxConfig.getEncryptionMode())) {
      // 获取aes密钥的返回内容
      return getAesSecret(response);
    }
    return new HashMap<>();
  }

  /** 获取aes密钥的返回内容 */
  private Map<String, String> getAesSecret(Map<Object, Object> resp) {
    Map<String, String> result = new HashMap<>();
    String encrypt = resp.get("Encrypt").toString();
    // 消息解密
    String xmlString = this.decrypt(encrypt);
    // 解析xml
    WxResponseXML wxResponse = XmlUtils.xmlToBean(xmlString, WxResponseXML.class);
    log.info("解析微信响应的xml：{}", wxResponse.toString());
    result.put("event", wxResponse.getEvent());
    result.put("openid", wxResponse.getFromUserName());
    result.put("qrId", wxResponse.getEventKey().substring(8));
    log.info("获取aes模式的微信回调结果为：{}", result);
    return result;
  }

  /** 获取明文模式下的密钥 */
  private Map<String, String> getPlainSecret(Map<Object, Object> resp) {
    Map<String, String> result = new HashMap<>();
    result.put("event", resp.get("Event").toString());
    result.put("openid", resp.get("FromUserName").toString());
    result.put("qrId", resp.get("EventKey").toString().substring(8));
    log.info("获取明文模式的微信回调结果为：{}", result);
    return result;
  }

  /**
   * 验证微信回调结果
   *
   * @param json 微信回调结果
   */
  private void validateResponse(String json, String errPrompt) {
    JSONObject jsonObject = JSONUtil.parseObj(json);
    String errCode = jsonObject.getStr("errcode");
    if (errCode != null && Integer.parseInt(errCode) > 0) {
      throw new BizException(errPrompt + "(" + errCode + ")");
    }
  }

  /**
   * 1、获取后台调用接口的access_token
   *
   * @return access_token
   */
  public WxToken getAccessToken() {
    try {
      String url = getAccessTokenUrl(wxConfig.getAppId(), wxConfig.getAppSecret());
      Response response = HttpClient.get(url, null, 5000);
      log.info("Get access token response: {}", response.getData());
      validateResponse(response.getData(), "Get Access Token");
      return JSONUtil.toBean(response.getData(), WxToken.class);
    } catch (Exception e) {
      throw new RuntimeException(RMsg.ERR_GET_ACCESS_TOKEN);
    }
  }

  /**
   * 2、获取票据，以便能下一步获取二维码
   *
   * @param token access_token
   * @param scene qrId
   * @return 微信二维码票据getToken
   */
  public WxTicket getTicket(String token, String scene) {
    try {
      String url = getTicketUrl(token);
      Response response = HttpClient.post(url, getTicketJsonParam(scene), null, 5000);
      log.info("Get ticket response: {}", response.getData());
      validateResponse(response.getData(), "Get Ticket");
      return JSONUtil.toBean(response.getData(), WxTicket.class);
    } catch (Exception e) {
      throw new RuntimeException(RMsg.ERR_GET_TICKET);
    }
  }

  /**
   * 生成获取票据的参数
   *
   * @param scene qrId
   * @return data:场景值
   */
  private String getTicketJsonParam(String scene) {
    LinkedHashMap<String, Object> param = new LinkedHashMap<>();
    param.put("expire_seconds", 183);
    param.put("action_name", "QR_STR_SCENE");
    LinkedHashMap<String, String> sceneMap = new LinkedHashMap<>();
    sceneMap.put("scene_str", scene);
    LinkedHashMap<String, Object> actionInfoMap = new LinkedHashMap<>();
    actionInfoMap.put("scene", sceneMap);
    param.put("action_info", actionInfoMap);
    return JSONUtil.toJsonStr(param);
  }

  /**
   * 根据openid获取用户信息
   *
   * @param openid 微信用户openid
   * @return 微信用户信息
   */
  public WxUserInfo getUserInfo(String token, String openid) {
    try {
      String url = getUserInfoUrl(token, openid);
      Response response = HttpClient.get(url, null, 5000);
      log.info("Get user info response: {}", response.getData());
      validateResponse(response.getData(), "Get User Info");
      return JSONUtil.toBean(response.getData(), WxUserInfo.class);
    } catch (Exception e) {
      throw new RuntimeException(RMsg.ERR_GET_USER_INFO);
    }
  }

  /**
   * 校验微信服务器Token签名
   *
   * @param signature 微信加密签名
   * @param timestamp 时间戳
   * @param nonce 随机数
   * @return boolean
   */
  public boolean checkSignature(String signature, String timestamp, String nonce) {
    String[] arr = {wxConfig.getToken(), timestamp, nonce};
    Arrays.sort(arr);
    StringBuilder stringBuilder = new StringBuilder();
    for (String param : arr) {
      stringBuilder.append(param);
    }

    String hexString = sha1(stringBuilder.toString());
    return signature.equals(hexString);
  }

  private String sha1(String str) {
    MessageDigest md;
    try {
      md = MessageDigest.getInstance("SHA-1");
      byte[] digest = md.digest(str.getBytes());
      return toHexString(digest);
    } catch (NoSuchAlgorithmException e) {
      log.warn("校验令牌Token出现错误：{}", e.getMessage());
    }
    return "";
  }

  /**
   * 字节数组转化为十六进制
   *
   * @param digest 字节数组
   * @return String
   */
  private String toHexString(byte[] digest) {
    StringBuilder hexString = new StringBuilder();
    for (byte b : digest) {
      String shaHex = Integer.toHexString(b & 0xff);
      if (shaHex.length() < 2) {
        hexString.append(0);
      }
      hexString.append(shaHex);
    }
    return hexString.toString();
  }

  /**
   * 对密文进行解密，使用AES-CBC模式，密钥使用AES密钥，数据采用PKCS#7填充
   *
   * @param cipherText 需要解密的密文
   * @return 解密得到的明文
   */
  public String decrypt(String cipherText) {
    byte[] original;
    try {
      // 设置解密模式为AES的CBC模式
      Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
      SecretKeySpec keySpec = new SecretKeySpec(this.aesKey, "AES");
      IvParameterSpec iv = new IvParameterSpec(Arrays.copyOfRange(this.aesKey, 0, 16));
      cipher.init(Cipher.DECRYPT_MODE, keySpec, iv);

      // 使用BASE64对密文进行解码
      byte[] encrypted = Base64.decode(cipherText);

      // 解密
      original = cipher.doFinal(encrypted);
    } catch (Exception e) {
      throw new RuntimeException(e.getMessage());
    }

    String xmlContent;
    String fromAppId;
    try {
      // 去除补位字符
      byte[] bytes = this.pkcs7Encoder(original);

      // 分离16位随机字符串,网络字节序和AppId
      byte[] networkOrder = Arrays.copyOfRange(bytes, 16, 20);

      int xmlLength = bytesNetworkOrder2Number(networkOrder);

      xmlContent = new String(Arrays.copyOfRange(bytes, 20, 20 + xmlLength), UTF_8);
      fromAppId = new String(Arrays.copyOfRange(bytes, 20 + xmlLength, bytes.length), UTF_8);
    } catch (Exception e) {
      throw new RuntimeException(e.getMessage());
    }

    // appid 不相同的情况
    if (!Objects.equals(fromAppId, wxConfig.getAppId())) {
      throw new RuntimeException("AppID不正确，请核实！");
    }
    return xmlContent;
  }

  /** 4个字节的网络字节序 bytes 数组还原成一个数字. */
  public static int bytesNetworkOrder2Number(byte[] bytesInNetworkOrder) {
    int sourceNumber = 0;
    for (int i = 0; i < 4; i++) {
      sourceNumber <<= 8;
      sourceNumber |= bytesInNetworkOrder[i] & 0xff;
    }
    return sourceNumber;
  }

  /**
   * 删除解密后明文的补位字符；PKCS7算法解密填充的
   *
   * @param decrypted 解密后的明文
   * @return 删除补位字符后的明文
   */
  public byte[] pkcs7Encoder(byte[] decrypted) {
    int pad = decrypted[decrypted.length - 1];
    if (pad < 1 || pad > 32) {
      pad = 0;
    }
    return Arrays.copyOfRange(decrypted, 0, decrypted.length - pad);
  }
}
