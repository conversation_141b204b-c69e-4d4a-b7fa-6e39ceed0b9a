package com.qbook.insight.common.entity;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公司详细信息实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CorpDetail extends BaseEntity {

  @ApiModelProperty("公司ID")
  private Long corpId;

  @ApiModelProperty("企业简称字号")
  private String shortName;

  @ApiModelProperty("法人名称")
  private String frname;

  @ApiModelProperty("企业地址")
  private String dom;

  @ApiModelProperty("企业类型")
  private String entTypeName;

  @ApiModelProperty("标准化的企业类型")
  private String capTypeName;

  @ApiModelProperty("经营范围")
  private String opScope;

  @ApiModelProperty("企业简介")
  private String description;

  @ApiModelProperty("工商注册号")
  private String regno;

  @ApiModelProperty("注册资本币种")
  private String regCapCurName;

  @ApiModelProperty("登记机关")
  private String regOrgName;

  @ApiModelProperty("省份")
  private String regOrgProvince;

  @ApiModelProperty("城市")
  private String regOrgCity;

  @ApiModelProperty("区县")
  private String regOrgCounty;

  @ApiModelProperty("组织机构代码")
  private String orgCode;

  @ApiModelProperty("核准日期")
  private Date apprdate;

  @ApiModelProperty("吊销日期")
  private Date revdate;

  @ApiModelProperty("注销日期")
  private Date candate;

  @ApiModelProperty("注销原因")
  private String revcanRea;

  @ApiModelProperty("经营起始日期")
  private Date opFrom;

  @ApiModelProperty("经营结束日期")
  private Date opTo;

  @ApiModelProperty("股东数量")
  private Long invCnt;

  @ApiModelProperty("高管数量")
  private Long priCnt;

  @ApiModelProperty("社保参与人数")
  private Long ssNum;

  @ApiModelProperty("员工规模")
  private Long staffNum;

  @ApiModelProperty("法人证件号码")
  private String frIdCard;

  @ApiModelProperty("纳税信用等级")
  private String creditRating;

  @ApiModelProperty("是否出口企业")
  private Integer isExportCompany;

  @ApiModelProperty("是否高新技术企业")
  private Integer isHighTech;

  @ApiModelProperty("是否小微企业")
  private Integer isSmallMicro;

  @ApiModelProperty("法人证件类型")
  private String frIdType;

  @ApiModelProperty("法人籍贯")
  private String frOrigin;

  @ApiModelProperty("纳税人类型")
  private String taxpayerType;
}
