package com.qbook.insight.collector.service;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 * MQ 队列发送工具
 *
 * <AUTHOR>
 */
@Component
public class MqSender {

  private static RabbitTemplate rabbitTemplate;

  MqSender(RabbitTemplate rabbitTemplate) {
    MqSender.rabbitTemplate = rabbitTemplate;
  }

  public static void send(String topic, Object data) {
    rabbitTemplate.convertAndSend(topic, data);
  }
}
