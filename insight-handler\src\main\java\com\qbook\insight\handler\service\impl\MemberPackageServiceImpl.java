package com.qbook.insight.handler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qbook.insight.common.util.StringUtils;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.entity.MemberPackage;
import com.qbook.insight.handler.mapper.MemberPackageMapper;
import com.qbook.insight.handler.service.MemberPackageService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 会员套餐接口实现
 *
 * <AUTHOR>
 */
@Service
public class MemberPackageServiceImpl implements MemberPackageService {

  @Resource private MemberPackageMapper memberPackageMapper;

  @Override
  public int add(MemberPackage pkg) {
    return memberPackageMapper.insert(pkg);
  }

  @Override
  public int delete(long id) {
    return memberPackageMapper.deleteById(id);
  }

  @Override
  public int update(long id, MemberPackage pkg) {
    pkg.setId(id);
    return memberPackageMapper.updateById(pkg);
  }

  @Override
  public MemberPackage getById(long id) {
    return memberPackageMapper.selectById(id);
  }

  @Override
  public List<MemberPackage> list(String name, Integer isActive) {
    LambdaQueryWrapper<MemberPackage> wrapper = new LambdaQueryWrapper<>();
    wrapper.like(StringUtils.isNotBlank(name), MemberPackage::getName, name);
    wrapper.eq(isActive != null, MemberPackage::getIsActive, isActive);
    wrapper.orderByAsc(MemberPackage::getPrice);
    return memberPackageMapper.selectList(wrapper);
  }

  @Override
  public IPage<MemberPackage> page(PageParam pageParam, String name, Integer isActive) {
    IPage<MemberPackage> page = new Page<>(pageParam.getPage(), pageParam.getPageSize());
    LambdaQueryWrapper<MemberPackage> wrapper = new LambdaQueryWrapper<>();
    wrapper.like(StringUtils.isNotBlank(name), MemberPackage::getName, name);
    wrapper.eq(isActive != null, MemberPackage::getIsActive, isActive);
    wrapper.orderByAsc(MemberPackage::getPrice);
    return memberPackageMapper.selectPage(page, wrapper);
  }

  @Override
  public List<MemberPackage> getActivePlans() {
    LambdaQueryWrapper<MemberPackage> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MemberPackage::getIsActive, 1);
    wrapper.orderByAsc(MemberPackage::getPrice);
    return memberPackageMapper.selectList(wrapper);
  }
}
