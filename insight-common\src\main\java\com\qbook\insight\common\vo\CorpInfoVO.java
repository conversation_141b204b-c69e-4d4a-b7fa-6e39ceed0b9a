package com.qbook.insight.common.vo;

import com.qbook.insight.common.entity.Corp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公司基本信息VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CorpInfoVO extends Corp {

  @ApiModelProperty("经营范围")
  private String opScope;

  @ApiModelProperty("登记机关")
  private String regOrgName;

  @ApiModelProperty("法人名称")
  private String frname;

  @ApiModelProperty("标准化的企业类型")
  private String capTypeName;

  @ApiModelProperty("纳税信用等级")
  private String creditRating;

  @ApiModelProperty("是否出口企业")
  private Integer isExportCompany;

  @ApiModelProperty("是否高新技术企业")
  private Integer isHighTech;

  @ApiModelProperty("是否小微企业")
  private Integer isSmallMicro;

  @ApiModelProperty("法人年龄")
  private String frAge;

  @ApiModelProperty("法人证件号码")
  private String frIdCard;

  @ApiModelProperty("法人证件类型")
  private String frIdType;

  @ApiModelProperty("法人籍贯")
  private String frOrigin;

  @ApiModelProperty("纳税人类型")
  private String taxpayerType;
}
