package com.qbook.insight.analyzer.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qbook.insight.analyzer.constant.AnalysisTaskStatus;
import com.qbook.insight.analyzer.entity.AnalysisTask;
import com.qbook.insight.analyzer.mapper.AnalysisTaskMapper;
import java.util.Date;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 任务标记服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TaskMarkService {

  private final AnalysisTaskMapper taskMapper;

  /** 创建并保存新任务 */
  public void createTask(String taskId, String strategyType) {
    AnalysisTask task = new AnalysisTask();
    task.setTaskId(taskId);
    task.setStrategyType(strategyType);
    task.setStatus(AnalysisTaskStatus.PENDING);
    task.setStartTime(new Date());
    task.setRetryCount(0);

    taskMapper.insert(task);
  }

  /** 更新任务状态为运行中 */
  public void startTask(String taskId) {
    AnalysisTask task = getTaskById(taskId);
    if (task != null) {
      task.setStatus(AnalysisTaskStatus.RUNNING);
      taskMapper.updateById(task);
    }
  }

  /** 标记任务成功 */
  public void completeTask(String taskId) {
    AnalysisTask task = getTaskById(taskId);
    if (task != null) {
      task.setStatus(AnalysisTaskStatus.SUCCESS);
      task.setEndTime(new Date());
      taskMapper.updateById(task);
    }
  }

  /** 标记任务失败并记录错误信息 */
  public void failTask(String taskId, String errorMessage) {
    AnalysisTask task = getTaskById(taskId);
    if (task != null) {
      task.setStatus(AnalysisTaskStatus.FAILED);
      task.setErrorMessage(errorMessage);
      task.setEndTime(new Date());
      taskMapper.updateById(task);
    }
  }

  /** 增加重试计数 */
  public void retryTask(String taskId, String errorMessage) {
    AnalysisTask task = getTaskById(taskId);
    if (task != null && task.getRetryCount() < task.getMaxRetries()) {
      task.setRetryCount(task.getRetryCount() + 1);
      task.setStatus(AnalysisTaskStatus.RETRYING);
      task.setErrorMessage(errorMessage);
      taskMapper.updateById(task);
    } else {
      failTask(taskId, errorMessage);
    }
  }

  /** 获取任务对象 */
  private AnalysisTask getTaskById(String taskId) {
    return taskMapper.selectOne(
        new LambdaQueryWrapper<AnalysisTask>().eq(AnalysisTask::getTaskId, taskId));
  }
}
