package ${packagePath}.controller;

import ${rClassName};
import ${packagePath}.entity.${className};
import ${pageParamClassName};
import ${packagePath}.service.${className}Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.*;

#if(!${classChineseName})
  #set($classChineseName=${className})
#end
/**
 * ${classChineseName}相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "${className} Operate", description = "${classChineseName}操作")
@RestController
@RequestMapping("/${tableName}")
public class ${className}Controller {

#set($service="${classInstanceName}Service")
  @Resource private ${className}Service ${service};

  @ApiOperation(value = "添加${classChineseName}")
  @PostMapping("/add")
  public R add(@RequestBody ${className} ${classInstanceName}) {
    return R.affect(${service}.add(${classInstanceName}));
  }

  @ApiOperation(value = "删除${classChineseName}")
  @DeleteMapping("/{id}")
  public R delete(@PathVariable long id) {
    return R.affect(${service}.delete(id));
  }

  @ApiOperation(value = "修改${classChineseName}")
  @PutMapping("/{id}")
  public R update(@PathVariable long id, @RequestBody ${className} ${classInstanceName}) {
    return R.affect(${service}.update(id, ${classInstanceName}));
  }

  @ApiOperation(value = "获取${classChineseName}列表")
  @GetMapping("/list")
  public R list(@ApiParam("查询字段") @RequestParam(required = false) String searchField) {
    return R.ok(${service}.list());
  }

  @ApiOperation(value = "获取${classChineseName}列表(分页)")
  @GetMapping("/page")
  public R page(
      PageParam pageParam, @ApiParam("查询字段") @RequestParam(required = false) String searchField) {
    return R.ok(${service}.page(pageParam));
  }
}
