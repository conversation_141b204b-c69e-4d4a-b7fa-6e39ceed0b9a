package com.qbook.insight.handler.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 小蓝本配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "xlb")
public class XlbProperties {
  // 小蓝本accessId
  private String accessId;
  // 小蓝本accessToken
  private String accessToken;
  // 小蓝本接口-社会信用代码查询公司4070
  private String corpCreditCode;
  // 小蓝本接口-企业基本信息-eid入参1001
  private String corpBaseInfo;
}
