/*
 Navicat Premium Dump SQL

 Source Server         : quickbook-测试环境数据库
 Source Server Type    : MySQL
 Source Server Version : 80200 (8.2.0)
 Source Host           : ************:3306
 Source Schema         : insight

 Target Server Type    : MySQL
 Target Server Version : 80200 (8.2.0)
 File Encoding         : 65001

 Date: 01/08/2025 16:13:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for indicator_config
-- ----------------------------
DROP TABLE IF EXISTS `indicator_config`;
CREATE TABLE `indicator_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `class1` int DEFAULT NULL COMMENT '指标一级分类编号',
  `class2` int DEFAULT NULL COMMENT '指标二级分类编号',
  `indicator_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '指标编码',
  `indicator_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '指标名称',
  `execute_sql` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '执行sql',
  `return_type` tinyint NOT NULL COMMENT '指标数据类型',
  `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '指标描述',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1-启用,0-禁用)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_indicator_code` (`indicator_code`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标计算配置表';

-- ----------------------------
-- Records of indicator_config
-- ----------------------------
BEGIN;
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (4, 8, NULL, 'invoice_purchase_year', '进项数据年度', 'SELECT ipd.kprqn as year,SUM( ipd.hjje ) as invoice_purchase_amount_year FROM invoice_purchase_detail ipd WHERE ipd.tax_id = {tax_id} AND ipd.kprqn = {year}', 1, '', 0, '2025-07-18 13:52:49', '2025-07-30 13:33:37');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (6, 2, NULL, 'vat_list', '增值税数据列表', 'SELECT ipd.kprqn AS YEAR,SUM(isd.hjse - ipd.hjse) AS vat,SUM(isd.hjse - ipd.hjse) / SUM(isd.hjje) AS vat_rate FROM invoice_purchase_detail ipd LEFT JOIN invoice_sales_detail isd ON ipd.tax_id = isd.tax_id WHERE ipd.tax_id = {tax_id} GROUP BY year', 2, '年度增值税;增值税负率', 0, '2025-07-18 15:15:54', '2025-07-30 13:33:30');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (9, 1, NULL, 'taxAnalysis.corporateIncomeTax', '所得税负率对比行业均值', 'WITH company_industry AS (SELECT JSON_UNQUOTE(JSON_EXTRACT(result, \'$.companyInfo.industryCategory\')) AS industry_name FROM declare_result WHERE type = 1 AND tax_id = {tax_id} LIMIT 1), tax_data AS (SELECT YEAR(declare_begin) AS year, TRUNCATE(CASE WHEN type = 1 THEN CAST(JSON_EXTRACT(result, \'$.corporateIncomeTaxMain.actualTaxPayable\') AS DECIMAL(20, 6))/NULLIF(CAST(JSON_EXTRACT(result, \'$.corporateIncomeTaxMain.businessIncome\') AS DECIMAL(20, 6)), 0) WHEN type = 2 THEN CAST(JSON_EXTRACT(result, \'$.taxPayable\') AS DECIMAL(20, 6))/NULLIF(CAST(JSON_EXTRACT(result, \'$.businessIncome\') AS DECIMAL(20, 6)), 0) END, 4) AS income_tax_burden FROM declare_result WHERE tax_id = {tax_id} AND ((type = 1) OR (type = 2 AND declare_begin = (SELECT MAX(declare_begin) FROM declare_result WHERE type = 2 AND tax_id = {tax_id})))) SELECT t.year, t.income_tax_burden AS incomeTaxBurden, d.income_tax_burden AS industryIncomeTaxBurden FROM tax_data t LEFT JOIN data_industry_tax_burden_mean d ON t.year = d.year AND d.industry_name = (SELECT industry_name FROM company_industry) ORDER BY t.year;', 2, '查询参数:tax_id\n响应参数:year;incomeTaxBurden;industryIncomeTaxBurden', 1, '2025-07-23 09:47:52', '2025-07-30 17:34:03');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (10, 1, NULL, 'taxAnalysis.corporateIncomeTax.expenseAdjustments.uninvoicedCosts', '暂估入账的成本费用', 'WITH yfzk_data AS (SELECT owner_year AS year, SUM(sumcr) AS yfzk FROM zjks_auxiliary_origbill WHERE tax_id = {tax_id} AND subject_key = 2202 GROUP BY owner_year), bhsje_data AS (SELECT kprqn AS year, SUM(hjje) AS bhsje FROM invoice_purchase_detail WHERE tax_id = {tax_id} GROUP BY kprqn), zangu_part1_data AS (SELECT COALESCE(a.year, b.year) AS year, CASE WHEN a.yfzk > b.bhsje THEN a.yfzk - b.bhsje ELSE 0 END AS zangu_part1 FROM yfzk_data a LEFT JOIN bhsje_data b ON a.year = b.year), zangu_year_end_data AS (SELECT owner_year AS year, SUM(shedm) AS zangu_year_end FROM zjks_subject WHERE tax_id = {tax_id} AND full_name LIKE \'%应付账款-暂估%\' GROUP BY owner_year), declare_data AS (SELECT YEAR(declare_begin) AS year, JSON_UNQUOTE(JSON_EXTRACT(result, \'$.taxAdjustmentDetail.assetAdjAddAmt\')) AS nonTaxableFiscalFundAmount FROM declare_result WHERE type = 1 AND tax_id = {tax_id}) SELECT COALESCE(p1.year, p2.year) AS year, COALESCE(p1.zangu_part1, 0) + COALESCE(p2.zangu_year_end, 0) AS estimatedCostExpense, COALESCE(p3.nonTaxableFiscalFundAmount, 0) AS nonTaxableFiscalFundAmount, COALESCE(p1.zangu_part1 + p2.zangu_year_end - p3.nonTaxableFiscalFundAmount, 0) AS uninvoicedCosts FROM zangu_part1_data p1 LEFT JOIN zangu_year_end_data p2 ON p1.year = p2.year LEFT JOIN declare_data p3 ON p1.year = p3.year ORDER BY year', 2, '查询参数:tax_id\n响应参数:year;estimatedCostExpense;nonTaxableFiscalFundAmount;uninvoicedCosts', 1, '2025-07-24 08:53:31', '2025-07-31 09:07:16');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (11, 1, NULL, 'taxAnalysis.corporateIncomeTax.incomeAdjustments.unrecordedAdvanceReceipts', '发生的预收款收入', 'WITH advance_received_income AS (SELECT z.owner_year AS year, (SUM(CASE WHEN z.subject_key = \'1122\' THEN z.sumcr ELSE 0 END) + SUM(CASE WHEN z.subject_key = \'2203\' THEN z.sumcr ELSE 0 END)) / (1 + COALESCE((SELECT tax_rate FROM declare_tax_policy WHERE tax_id = {tax_id} AND tax_type = \'增值税\' LIMIT 1), 0)) AS advance_received_income FROM zjks_auxiliary_origbill z WHERE z.tax_id = {tax_id} AND z.subject_key IN (\'1122\', \'2203\') GROUP BY z.owner_year ORDER BY z.owner_year), non_accrual_income AS (SELECT YEAR(declare_begin) AS year, JSON_UNQUOTE(JSON_EXTRACT(result, \'$.taxAdjustmentDetail.nonAccrualIncome\')) AS nonAccrualIncome FROM declare_result WHERE type = 1 AND tax_id = {tax_id}) SELECT a.year AS year, a.advance_received_income AS advanceReceivedIncome, COALESCE(n.nonAccrualIncome, 0) AS nonAccrualIncome, a.advance_received_income - COALESCE(n.nonAccrualIncome, 0) AS unrecordedAdvanceReceipts FROM advance_received_income a LEFT JOIN non_accrual_income n ON a.year = n.year ORDER BY a.year;', 2, '查询参数:tax_id\n响应参数:year;advanceReceivedIncome;nonAccrualIncome;unrecordedAdvanceReceipts', 1, '2025-07-25 10:24:16', '2025-07-31 15:16:44');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (12, 1, NULL, 'taxAnalysis.corporateIncomeTax.incomeAdjustments.unrecordedScrapIncome', '下脚料、废料、包装物未确认收入', 'WITH company_industry AS (SELECT JSON_UNQUOTE(JSON_EXTRACT(result, \'$.companyInfo.industryCategory\')) AS industryCategory FROM declare_result WHERE type = 1 AND tax_id = {tax_id} LIMIT 1), business_income AS (SELECT owner_year AS year, SUM(shedm) AS business_income FROM zjks_subject WHERE tax_id = {tax_id} and subject_key in (\'5001\',\'5051\') GROUP BY owner_year), lease_income AS (SELECT YEAR(declare_begin) AS year, JSON_UNQUOTE(JSON_EXTRACT(result, \'$.generalIncomeDetail.leaseIncome\')) AS leaseIncome FROM declare_result WHERE type = 1 AND tax_id = {tax_id}) SELECT b.year,COALESCE(b.business_income,0) / COALESCE(d.scrap_income_ratio,0) - COALESCE(l.leaseIncome,0) AS unrecordedScrapIncome FROM business_income b LEFT JOIN lease_income l ON b.year = l.year LEFT JOIN data_industry_tax_burden_mean d ON b.year = d.year AND d.industry_name = (SELECT industryCategory FROM company_industry) ORDER BY b.year;', 2, '查询参数:tax_id\n响应参数:year;unrecordedScrapIncome', 1, '2025-07-25 11:07:05', '2025-07-31 20:45:54');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (13, 1, NULL, 'taxAnalysis.corporateIncomeTax.incomeDeductions.nonTaxableGovIncome', '取得的补贴收入（财政补贴）', 'WITH zjks_subject AS (SELECT owner_year AS year, SUM(shedm) AS governmentSubsidyIncome FROM zjks_subject WHERE tax_id = {tax_id} and full_name LIKE \'%营业外收入-政府补%\' GROUP BY owner_year), declare_data AS (SELECT YEAR(declare_begin) AS year, JSON_UNQUOTE(JSON_EXTRACT(result, \'$.specialFiscalFundAdjustDetail.nonTaxableFiscalFundAmount\')) AS nonTaxableFiscalFundAmount FROM declare_result WHERE type = 1 AND tax_id = {tax_id}) SELECT z.year, COALESCE (z.governmentSubsidyIncome - d.nonTaxableFiscalFundAmount,0) AS nonTaxableGovIncome FROM zjks_subject z LEFT JOIN declare_data d ON z.year = d.year ORDER BY z.year;', 2, '查询参数:tax_id\n响应参数:year;nonTaxableGovIncome', 1, '2025-07-25 11:30:59', '2025-07-31 17:42:37');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (14, 1, NULL, 'taxAnalysis.corporateIncomeTax.incomeAdjustments.deemedSalesIncome', '自产产品、财产用于交换、捐赠、偿债、赞助、集资、广告、样品、职工福利视同销售收入', 'SELECT YEAR(declare_begin) + 0 AS year, JSON_UNQUOTE(JSON_EXTRACT(result, \'$.taxAdjustmentDetail.deemedSalesIncome\')) AS deemedSalesIncome FROM declare_result WHERE type = 1 AND tax_id = {tax_id}', 2, '查询参数:tax_id\n响应参数:year;nonTaxableFiscalFundAmount', 1, '2025-07-25 13:22:55', '2025-07-30 17:46:38');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (15, 1, NULL, 'taxAnalysis.corporateIncomeTax.incomeAdjustments.unrecordedRentInterestIncome', '未按权责发生制确认收到的租金、利息等收入', 'SELECT YEAR(declare_begin) + 0 AS year, JSON_EXTRACT( result, \'$.taxAdjustmentDetail.nonAccrualIncome\' ) AS unrecordedRentInterestIncome FROM declare_result WHERE type = 1 AND tax_id = {tax_id};', 2, '查询参数:tax_id\n响应参数:year;unrecordedRentInterestIncome', 1, '2025-07-25 13:30:15', '2025-07-31 20:53:54');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (16, 1, NULL, 'taxAnalysis.socialInsurance.insufficientSocialInsurance', '未足额计提缴纳社会保险金', 'WITH zjks_subject AS (SELECT owner_year AS year, SUM(COALESCE(shedm, 0)) AS total_salary FROM zjks_subject WHERE tax_id = {tax_id} AND full_name LIKE \'%工资%\' GROUP BY owner_year), declare_info AS (SELECT YEAR(declare_begin) AS year, ROUND(AVG(t.rate), 6) AS total_rate, SUM(t.payableAmount) AS total_corporate_payment FROM declare_result JOIN JSON_TABLE(result, \'$[*]\' COLUMNS (rate DECIMAL(8,6) PATH \'$.rate\', payableAmount DECIMAL(12,2) PATH \'$.payableAmount\')) AS t WHERE tax_id = {tax_id} AND type = 6 GROUP BY YEAR(declare_begin)) SELECT z.year, ROUND(z.total_salary * d.total_rate - d.total_corporate_payment, 2) AS insufficientSocialInsurance FROM zjks_subject z LEFT JOIN declare_info d ON z.year = d.year;', 2, '查询参数:tax_id\n响应参数:year;insufficientSocialInsurance', 1, '2025-07-25 16:20:10', '2025-07-31 17:47:09');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (17, 1, NULL, 'taxAnalysis.corporateIncomeTax.expenseAdjustments.personalSocialInsurance', '管理费用中列支应由职工个人缴纳的养老金', 'WITH zjks_subject AS (SELECT owner_year AS year, SUM(COALESCE(shedm, 0)) AS total_amount FROM zjks_subject WHERE tax_id = {tax_id} AND (subject_key = \'5602.01.02\' OR full_name LIKE \'其他应收款-个人社保%\') GROUP BY owner_year), declare_data_social AS (SELECT YEAR(declare_begin) AS year, SUM(t.payableAmount) AS total_corporate_payment FROM declare_result JOIN JSON_TABLE(result, \'$[*]\' COLUMNS(levySubItem VARCHAR(50) PATH \'$.levySubItem\', payableAmount DECIMAL(12,2) PATH \'$.payableAmount\')) AS t WHERE type = 6 AND tax_id = {tax_id} AND t.levySubItem = \'企业缴纳\' GROUP BY YEAR(declare_begin)), declare_data_income AS (SELECT YEAR(declare_begin) AS year, JSON_UNQUOTE(JSON_EXTRACT(result, \'$.staffCostAdjustment.socialSecAdj\')) AS socialSecAdj FROM declare_result WHERE type = 1 AND tax_id = {tax_id}) SELECT z.year, COALESCE(z.total_amount - ddc.total_corporate_payment - ddi.socialSecAdj, 0) AS personalSocialInsurance FROM zjks_subject z LEFT JOIN declare_data_social ddc ON z.year = ddc.year LEFT JOIN declare_data_income ddi ON z.year = ddi.year ORDER BY z.year;', 2, '查询参数:tax_id\n响应参数:year;personalSocialInsurance', 1, '2025-07-25 16:56:14', '2025-07-31 15:57:53');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (18, 15, NULL, 'invoiceAnalysis.purchaseInvoice.invoiceRiskStats', '发票风险统计(进项)', 'SELECT gmfmc AS corpName, gmfnsrsbh AS taxId, SUM(jshj) AS totalAmount\nFROM invoice_purchase\nWHERE tax_id = { tax_id } AND fpztdm = \'正常\' AND kprq >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)\nGROUP BY corpName, taxId\nORDER BY totalAmount DESC\nLIMIT 15;', 2, '', 1, '2025-08-01 13:27:31', '2025-08-01 13:37:42');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (19, 15, NULL, 'invoiceAnalysis.salesInvoice.invoiceRiskStats', '发票风险统计(销项)', 'SELECT gmfmc AS corpName, gmfnsrsbh AS taxId, SUM(jshj) AS totalAmount\nFROM invoice_sales\nWHERE tax_id = { tax_id } AND fpztdm = \'正常\' AND kprq >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)\nGROUP BY corpName, taxId\nORDER BY totalAmount DESC\nLIMIT 15', 2, '', 1, '2025-08-01 13:37:30', '2025-08-01 13:37:30');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (20, 15, NULL, 'invoiceAnalysis', '发票分析', 'SELECT\npurchaseTotalAmount,\nsalesTotalAmount,\n(purchaseTotalAmount > salesTotalAmount) AS isPurchaseGTSalesTotal,\npurchaseTax,\nsalesTax,\n(purchaseTax / salesTax < 0.2) AS isSalesMLTPurchaseTax,\n(purchaseTotalAmount / salesTotalAmount < 0.1) as isOnlySales\nFROM (\nSELECT\n(SELECT SUM(jshj) FROM invoice_purchase WHERE tax_id={ tax_id } AND kprq >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)) AS purchaseTotalAmount,\n(SELECT SUM(jshj) FROM invoice_sales WHERE tax_id={ tax_id } AND kprq >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)) AS salesTotalAmount,\n(SELECT SUM(hjse) FROM invoice_purchase WHERE tax_id={ tax_id } AND kprq >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)) AS purchaseTax,\n(SELECT SUM(hjse) FROM invoice_sales WHERE tax_id={ tax_id } AND kprq >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)) AS salesTax\n) AS t', 1, '', 1, '2025-08-01 14:04:11', '2025-08-01 14:23:17');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (21, 16, NULL, 'productAnalysis.mainPurchaseProducts', '主要进项商品列表', 'WITH d AS (SELECT hwmc AS `name`, dw AS `unit`, SUM(xssl) AS quantity, SUM(jshj) AS amount, CASE WHEN SUM(xssl) = 0 THEN 0 ELSE ROUND(SUM(jshj) / SUM(xssl), 2) END AS avgPrice FROM invoice_purchase_detail WHERE tax_id = {tax_id} GROUP BY hwmc, dw), total_amount AS (SELECT SUM(amount) AS total_amount FROM d) SELECT d.name, d.unit, d.quantity, d.amount, d.avgPrice, ROUND(d.amount / total.total_amount * 100, 2) AS percentage FROM d CROSS JOIN total_amount total ORDER BY d.amount DESC LIMIT 15;', 2, '', 1, '2025-08-01 15:26:43', '2025-08-01 15:26:43');
INSERT INTO `indicator_config` (`id`, `class1`, `class2`, `indicator_code`, `indicator_name`, `execute_sql`, `return_type`, `description`, `status`, `created_at`, `updated_at`) VALUES (22, 16, NULL, 'productAnalysis.mainSalesProducts', '主要销项商品列表', 'WITH d AS (SELECT hwmc AS `name`, dw AS `unit`, SUM(xssl) AS quantity, SUM(jshj) AS amount, CASE WHEN SUM(xssl) = 0 THEN 0 ELSE ROUND(SUM(jshj) / SUM(xssl), 2) END AS avgPrice FROM invoice_sales_detail WHERE tax_id = {tax_id} GROUP BY hwmc, dw), total_amount AS (SELECT SUM(amount) AS total_amount FROM d) SELECT d.name, d.unit, d.quantity, d.amount, d.avgPrice, ROUND(d.amount / total.total_amount * 100, 2) AS percentage FROM d CROSS JOIN total_amount total ORDER BY d.amount DESC LIMIT 15;', 2, '', 1, '2025-08-01 15:36:40', '2025-08-01 15:36:40');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
