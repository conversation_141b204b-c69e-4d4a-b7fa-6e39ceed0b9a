package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 增值税
 *
 * <AUTHOR>
 */
@Data
public class VAT {

  @ApiModelProperty("序号")
  private Integer index;

  @ApiModelProperty("年份")
  private Integer year;

  @ApiModelProperty("企业增值税负率")
  private BigDecimal vatBurden;

  @ApiModelProperty("增值税负率行业均值")
  private BigDecimal industryVatTaxBurden;

  @ApiModelProperty("增加值税负率")
  private BigDecimal addTaxBurden;

  @ApiModelProperty("销项税额方面")
  private OutputTax outputTax;

  @ApiModelProperty("进项税额方面")
  private InputTax inputTax;

  @ApiModelProperty("税率及其他")
  private OtherVAT otherVat;
}
