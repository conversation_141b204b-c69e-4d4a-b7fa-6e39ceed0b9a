package com.qbook.insight.common.util;

import com.qbook.insight.common.entity.Column;
import java.util.*;

/**
 * 代码生成工具类
 *
 * <AUTHOR>
 */
public class GenUtil {

  // 需要忽略的字段
  private static final Set<String> ignoreColumns =
      new HashSet<>(Arrays.asList("id", "created_at", "updated_at"));

  // 数据库数字类型
  private static final Set<String> COLUMN_TYPE_NUMBER =
      new HashSet<>(
          Arrays.asList(
              "tinyint",
              "smallint",
              "mediumint",
              "int",
              "number",
              "integer",
              "bit",
              "bigint",
              "float",
              "double",
              "decimal"));

  // 数据库时间字段
  private static final Set<String> COLUMN_TYPE_TIME =
      new HashSet<>(Arrays.asList("datetime", "time", "date", "timestamp"));

  /**
   * 整理字段
   *
   * <p>1. 删除某些字段 2. 设置Java字段类型、Java字段名
   */
  public static void organizeColumns(List<Column> columns) {
    Iterator<Column> iterator = columns.iterator();
    while (iterator.hasNext()) {
      Column column = iterator.next();
      String columnName = column.getColumnName();
      if (ignoreColumns.contains(columnName)) {
        iterator.remove();
        continue;
      }
      organizeColumn(column);
    }
  }

  public static void organizeColumn(Column column) {
    String dataType = column.getColumnType();
    if (dataType.indexOf("(") > 0) {
      dataType = dataType.substring(0, dataType.indexOf("("));
    }

    String columnName = column.getColumnName();
    column.setJavaField(StringUtils.toCamelCase(columnName));

    if (COLUMN_TYPE_NUMBER.contains(dataType)) {
      String[] str =
          StringUtils.split(StringUtils.substringBetween(column.getColumnType(), "(", ")"), ",");
      if (str != null && str.length == 2 && Integer.parseInt(str[1]) > 0) {
        column.setJavaType("BigDecimal");
      } else if (str != null && str.length == 1 && Integer.parseInt(str[0]) <= 10) {
        column.setJavaType("Integer");
      } else {
        column.setJavaType("Long");
      }
    } else if (COLUMN_TYPE_TIME.contains(dataType)) {
      column.setJavaField("Date");
    } else {
      column.setJavaType("String");
    }
  }
}
