package com.qbook.insight.handler.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.qbook.insight.handler.config.WxPayConfig;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 微信支付工具类
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-11 下午1:59
 */
@Component
@Slf4j
public class WxPayUtils {

  @Resource private WxPayConfig wxPayConfig;

  /**
   * 构建微信支付参数
   *
   * @param appId appId
   * @param prepayId 预支付id
   * @return 支付参数
   */
  public Map<String, String> buildPayMap(String appId, String prepayId) {
    try {
      String timeStamp = String.valueOf(System.currentTimeMillis() / 1000L);
      String nonceStr = String.valueOf(System.currentTimeMillis());
      String packageStr = "prepay_id=" + prepayId;
      Map<String, String> packageParams = new HashMap<>(6);
      packageParams.put("appId", appId);
      packageParams.put("timeStamp", timeStamp);
      packageParams.put("nonceStr", nonceStr);
      packageParams.put("package", packageStr);
      packageParams.put("signType", "RSA");
      ArrayList<String> list = new ArrayList<>();
      list.add(appId);
      list.add(timeStamp);
      list.add(nonceStr);
      list.add(packageStr);
      PrivateKey privateKey = convertToPrivateKey(wxPayConfig.getMchPriKeyPath());
      // 生成签名
      String packageSign = encryptByPrivateKey(buildSignMessage(list), privateKey);
      packageParams.put("paySign", packageSign);
      return packageParams;
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * 获取商户API私钥对象
   *
   * @param privateKeyString 私钥字符串
   * @return 私钥对象
   */
  public PrivateKey convertToPrivateKey(String privateKeyString) throws Exception {
    // 移除PEM格式的私钥中的首尾标识，例如 -----BEGIN PRIVATE KEY-----
    privateKeyString = privateKeyString.replaceAll("-----\\w+ PRIVATE KEY-----", "");
    // 去除换行符、空格等字符
    privateKeyString = privateKeyString.replaceAll("\\s", "");
    // 将Base64编码的私钥字符串解码为字节数组
    byte[] privateKeyBytes = Base64.decode(privateKeyString);
    // 构造PKCS8EncodedKeySpec对象
    PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
    // 获取RSA密钥工厂
    KeyFactory keyFactory = KeyFactory.getInstance("RSA");
    // 生成私钥对象
    return keyFactory.generatePrivate(keySpec);
  }

  /**
   * 商户API私钥加密
   *
   * @param data 待加密数据
   * @param privateKey 私钥对象
   * @return 密文
   */
  public String encryptByPrivateKey(String data, PrivateKey privateKey) throws Exception {
    Signature signature = Signature.getInstance("SHA256WithRSA");
    signature.initSign(privateKey);
    signature.update(data.getBytes(StandardCharsets.UTF_8));
    byte[] signed = signature.sign();
    return StrUtil.str(Base64.encode(signed));
  }

  /**
   * 构建验字符串参数
   *
   * @param signMessage 签名信息
   * @return 签名参数
   */
  public String buildSignMessage(List<String> signMessage) {
    if (signMessage != null && !signMessage.isEmpty()) {
      StringBuilder sbf = new StringBuilder();
      for (String str : signMessage) {
        sbf.append(str).append("\n");
      }
      return sbf.toString();
    } else {
      return null;
    }
  }
}
