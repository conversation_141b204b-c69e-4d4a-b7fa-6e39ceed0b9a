package com.qbook.insight.common.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 报告实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Report extends BaseEntity {

  @ApiModelProperty("用户ID")
  private Long userId;

  @ApiModelProperty("客户ID")
  private Long clientId;

  @ApiModelProperty("公司ID")
  private Long corpId;

  @ApiModelProperty("报告名称")
  private String name;

  @ApiModelProperty("报告模板")
  private Integer templateId;

  @ApiModelProperty("报告进度(百分比)")
  private Integer progress;

  @ApiModelProperty("报告所处阶段")
  private Integer stage;

  @ApiModelProperty("报告概述审核状态")
  private Integer summaryAuditTag;

  @ApiModelProperty("报告详情审核状态")
  private Integer detailAuditTag;
}
