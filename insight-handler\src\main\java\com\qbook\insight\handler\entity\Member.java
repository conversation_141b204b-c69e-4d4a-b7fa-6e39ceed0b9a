package com.qbook.insight.handler.entity;

import com.qbook.insight.common.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会员信息实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Member extends BaseEntity {

  @ApiModelProperty("所属用户ID")
  private Long userId;

  @ApiModelProperty("开始时间")
  private Date startDate;

  @ApiModelProperty("结束时间")
  private Date endDate;

  @ApiModelProperty("是否激活：0-否，1-是")
  private Integer isActive;

  @ApiModelProperty("功能列表：create_report,download_report,data_export")
  private String features;

  @ApiModelProperty("总报告创建次数")
  private Integer totalReports;

  @ApiModelProperty("剩余报告创建次数")
  private Integer remainingReports;
}
