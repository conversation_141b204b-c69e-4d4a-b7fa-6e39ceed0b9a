package com.qbook.insight.handler.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qbook.insight.common.vo.ClientReportVO;
import com.qbook.insight.common.vo.ClientVO;
import com.qbook.insight.common.vo.PageParam;
import org.springframework.stereotype.Service;

/**
 * 客户操作接口
 *
 * <AUTHOR>
 */
@Service
public interface ClientService {

  /** 添加客户 */
  int add(ClientVO clientVO);

  /** 删除客户 */
  int delete(long id);

  /** 修改客户 */
  int update(long id, ClientVO clientVO);

  /** 获取客户列表(分页，包含报告列表) */
  IPage<ClientReportVO> page(PageParam pageParam, String corpName, String taxId, String tags);
}
