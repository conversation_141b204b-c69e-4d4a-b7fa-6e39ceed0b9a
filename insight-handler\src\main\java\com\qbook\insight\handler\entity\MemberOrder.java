package com.qbook.insight.handler.entity;

import com.qbook.insight.common.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会员订单实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MemberOrder extends BaseEntity {

  @ApiModelProperty("所属用户ID")
  private Long userId;

  @ApiModelProperty("套餐ID")
  private Integer planId;

  @ApiModelProperty("订单号")
  private String orderId;

  @ApiModelProperty("微信openid")
  private String wxPayOpenid;

  @ApiModelProperty("订单金额（元）")
  private BigDecimal amount;

  @ApiModelProperty("订单状态：pending, paid, failed, cancelled, refunded")
  private String status;

  @ApiModelProperty("支付方式：wechat_pay, alipay")
  private String paymentMethod;

  @ApiModelProperty("支付链接")
  private String paymentUrl;

  @ApiModelProperty("第三方支付订单ID")
  private String thirdPartyOrderId;

  @ApiModelProperty("支付时间")
  private Date paidAt;

  @ApiModelProperty("退款时间")
  private Date refundedAt;

  @ApiModelProperty("退款原因")
  private String refundReason;

  @ApiModelProperty("备注")
  private String remark;
}
