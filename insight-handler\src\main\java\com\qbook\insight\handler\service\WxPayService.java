package com.qbook.insight.handler.service;

import javax.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

/**
 * 微信支付操作
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-08 下午10:59
 */
@Service
public interface WxPayService {

  /**
   * 重定向至微信内置浏览器进行支付
   *
   * @param orderId 订单id
   */
  void redirectWxPay(String orderId);

  /**
   * 微信回调支付页面
   *
   * @param request 请求
   * @param model 模型
   * @return 模板名称
   */
  String showPayPage(HttpServletRequest request, Model model);

  /**
   * 微信支付回调
   *
   * @param request 请求
   * @return 响应结果
   */
  String wxGetNotify(HttpServletRequest request);
}
