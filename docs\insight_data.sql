/*
 Navicat Premium Dump SQL

 Source Server         : quickbook-测试环境数据库
 Source Server Type    : MySQL
 Source Server Version : 80200 (8.2.0)
 Source Host           : ************:3306
 Source Schema         : insight_data

 Target Server Type    : MySQL
 Target Server Version : 80200 (8.2.0)
 File Encoding         : 65001

 Date: 24/07/2025 13:57:49
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for declare_asset_accelerated_depreciation
-- ----------------------------
DROP TABLE IF EXISTS `declare_asset_accelerated_depreciation`;
CREATE TABLE `declare_asset_accelerated_depreciation` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tax_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人识别号（统一社会信用代码）',
  `taxpayer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人名称',
  `tax_period_start` date NOT NULL COMMENT '税款所属期间起',
  `tax_period_end` date NOT NULL COMMENT '税款所属期间止',
  `accel_depreciation_assets` decimal(15,2) DEFAULT '0.00' COMMENT '加速折旧、摊销（不含一次性扣除）-本年享受优惠的资产原值',
  `accel_book_depreciation_amount` decimal(15,2) DEFAULT '0.00' COMMENT '加速折旧、摊销（不含一次性扣除）-账载折旧摊销金额',
  `accel_depreciation_normal_amount` decimal(15,2) DEFAULT '0.00' COMMENT '加速折旧、摊销（不含一次性扣除）-按税收一般规定计算折旧/摊销金额',
  `accel_depreciation_actual_amount` decimal(15,2) DEFAULT '0.00' COMMENT '加速折旧、摊销（不含一次性扣除）-本年加速折旧/摊销金额',
  `accel_depreciation_tax_adjustment` decimal(15,2) DEFAULT '0.00' COMMENT '加速折旧、摊销（不含一次性扣除）-纳税调减金额',
  `accel_depreciation_benefit_amount` decimal(15,2) DEFAULT '0.00' COMMENT '加速折旧、摊销（不含一次性扣除）-享受加速折旧/摊销优惠金额',
  `one_time_deduction_assets` decimal(15,2) DEFAULT '0.00' COMMENT '一次性扣除-本年享受优惠的资产原值',
  `one_time_book_depreciation_amount` decimal(15,2) DEFAULT '0.00' COMMENT '一次性扣除-账载折旧摊销金额',
  `one_time_deduction_normal_amount` decimal(15,2) DEFAULT '0.00' COMMENT '一次性扣除-按税收一般规定计算折旧/摊销金额',
  `one_time_deduction_actual_amount` decimal(15,2) DEFAULT '0.00' COMMENT '一次性扣除-本年一次性扣除金额',
  `one_time_deduction_tax_adjustment` decimal(15,2) DEFAULT '0.00' COMMENT '一次性扣除-纳税调减金额',
  `one_time_deduction_benefit_amount` decimal(15,2) DEFAULT '0.00' COMMENT '一次性扣除-享受一次性扣除优惠金额',
  `total_assets` decimal(15,2) DEFAULT '0.00' COMMENT '合计-本年享受优惠的资产原值',
  `total_normal_amount` decimal(15,2) DEFAULT '0.00' COMMENT '合计-按税收一般规定计算折旧/摊销金额',
  `total_actual_amount` decimal(15,2) DEFAULT '0.00' COMMENT '合计-本年加速折旧/摊销金额',
  `total_tax_adjustment` decimal(15,2) DEFAULT '0.00' COMMENT '合计-纳税调减金额',
  `total_benefit_amount` decimal(15,2) DEFAULT '0.00' COMMENT '合计-享受加速折旧/摊销优惠金额',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_taxpayer_id` (`tax_id`),
  KEY `idx_taxpayer_name` (`taxpayer_name`),
  KEY `idx_tax_period` (`tax_period_start`,`tax_period_end`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资产加速折旧、摊销（扣除）优惠明细表 (A201020)';

-- ----------------------------
-- Table structure for declare_enterprise_income_tax_prepayment
-- ----------------------------
DROP TABLE IF EXISTS `declare_enterprise_income_tax_prepayment`;
CREATE TABLE `declare_enterprise_income_tax_prepayment` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tax_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人识别号（统一社会信用代码）',
  `taxpayer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人名称',
  `tax_period_start` date NOT NULL COMMENT '税款所属期间起',
  `tax_period_end` date NOT NULL COMMENT '税款所属期间止',
  `prepayment_method` tinyint NOT NULL COMMENT '预缴方式(1、按照实际利润额预缴2、按照上一纳税年度应纳税所得额平均额\r\n预缴3、按照税务机关确定的其他方法预缴)',
  `enterprise_type` tinyint NOT NULL COMMENT '企业类型(1、一般企业2、跨地区经营汇总纳税企业总机构3、跨地区经营汇总纳税企业分支机构)',
  `q1_employee_start` int DEFAULT NULL COMMENT '一季度从业人数季初',
  `q1_employee_end` int DEFAULT NULL COMMENT '一季度从业人数季末',
  `q2_employee_start` int DEFAULT NULL COMMENT '二季度从业人数季初',
  `q2_employee_end` int DEFAULT NULL COMMENT '二季度从业人数季末',
  `q3_employee_start` int DEFAULT NULL COMMENT '三季度从业人数季初',
  `q3_employee_end` int DEFAULT NULL COMMENT '三季度从业人数季末',
  `q4_employee_start` int DEFAULT NULL COMMENT '四季度从业人数季初',
  `q4_employee_end` int DEFAULT NULL COMMENT '四季度从业人数季末',
  `employee_quarterly_avg` decimal(10,2) DEFAULT NULL COMMENT '从业人数季度平均值',
  `q1_assets_start` decimal(15,2) DEFAULT NULL COMMENT '一季度资产总额季初(万元)',
  `q1_assets_end` decimal(15,2) DEFAULT NULL COMMENT '一季度资产总额季末(万元)',
  `q2_assets_start` decimal(15,2) DEFAULT NULL COMMENT '二季度资产总额季初(万元)',
  `q2_assets_end` decimal(15,2) DEFAULT NULL COMMENT '二季度资产总额季末(万元)',
  `q3_assets_start` decimal(15,2) DEFAULT NULL COMMENT '三季度资产总额季初(万元)',
  `q3_assets_end` decimal(15,2) DEFAULT NULL COMMENT '三季度资产总额季末(万元)',
  `q4_assets_start` decimal(15,2) DEFAULT NULL COMMENT '四季度资产总额季初(万元)',
  `q4_assets_end` decimal(15,2) DEFAULT NULL COMMENT '四季度资产总额季末(万元)',
  `assets_quarterly_avg` decimal(15,2) DEFAULT NULL COMMENT '资产总额季度平均值(万元)',
  `restricted_industry` tinyint(1) DEFAULT NULL COMMENT '国家限制或禁止行业(是/否)',
  `small_micro_enterprise` tinyint(1) DEFAULT NULL COMMENT '小型微利企业(是/否)',
  `support_poverty_donation` decimal(15,2) DEFAULT NULL COMMENT '扶贫捐赠支出全额扣除(本年累计，元)',
  `software_ic_policy_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '软件集成电路企业优惠政策适用类型',
  `operating_income` decimal(15,2) NOT NULL COMMENT '营业收入',
  `operating_cost` decimal(15,2) NOT NULL COMMENT '营业成本',
  `total_profit` decimal(15,2) NOT NULL COMMENT '利润总额',
  `specific_business_income` decimal(15,2) DEFAULT NULL COMMENT '特定业务计算的应纳税所得额',
  `non_taxable_income` decimal(15,2) DEFAULT NULL COMMENT '不征税收入',
  `accelerated_depreciation` decimal(15,2) DEFAULT NULL COMMENT '资产加速折旧、摊销(扣除)调减额',
  `tax_exempt_income` decimal(15,2) DEFAULT NULL COMMENT '免税收入、减计收入、加计扣除',
  `income_reduction` decimal(15,2) DEFAULT NULL COMMENT '所得减免',
  `loss_offset` decimal(15,2) DEFAULT NULL COMMENT '弥补以前年度亏损',
  `actual_profit` decimal(15,2) DEFAULT NULL COMMENT '实际利润额',
  `tax_rate` decimal(5,2) DEFAULT NULL COMMENT '税率(25%)',
  `tax_payable` decimal(15,2) DEFAULT NULL COMMENT '应纳所得税额',
  `tax_reduction` decimal(15,2) DEFAULT NULL COMMENT '减免所得税额',
  `sme_tax_reduction` decimal(15,2) DEFAULT NULL COMMENT '符合条件的小型微利企业减免企业所得税',
  `prepaid_tax` decimal(15,2) DEFAULT NULL COMMENT '本年实际已缴纳所得税额',
  `specific_business_prepaid` decimal(15,2) DEFAULT NULL COMMENT '特定业务预缴(征)所得税额',
  `tax_refund` decimal(15,2) DEFAULT NULL COMMENT '本期应补(退)所得税额',
  `hq_tax_refund` decimal(15,2) DEFAULT NULL COMMENT '总机构本期分摊应补(退)所得税额',
  `hq_share_tax_refund` decimal(15,2) DEFAULT NULL COMMENT '总机构分摊应补(退)所得税额',
  `hq_share_ratio` decimal(10,8) DEFAULT NULL COMMENT '总机构分摊比例(%)',
  `fiscal_centralized_tax_refund` decimal(15,2) DEFAULT NULL COMMENT '财政集中分配应补(退)所得税额',
  `fiscal_centralized_ratio` decimal(10,8) DEFAULT NULL COMMENT '财政集中分配比例(%)',
  `hq_production_tax_refund` decimal(15,2) DEFAULT NULL COMMENT '总机构具有主体生产经营职能的部门分摊所得税额',
  `branch_share_ratio` decimal(10,8) DEFAULT NULL COMMENT '全部分支机构分摊比例(%)',
  `hq_production_ratio` decimal(10,8) DEFAULT NULL COMMENT '总机构具有主体生产经营职能部门分摊比例(%)',
  `branch_share_tax_refund` decimal(15,2) DEFAULT NULL COMMENT '分支机构本期分摊应补(退)所得税额',
  `branch_ratio` decimal(10,8) DEFAULT NULL COMMENT '分支机构本期分摊比例(%)',
  `central_tax_payable` decimal(15,2) DEFAULT NULL COMMENT '中央级收入实际应纳税额',
  `local_tax_payable` decimal(15,2) DEFAULT NULL COMMENT '地方级收入应纳税额',
  `autonomous_region_reduction` tinyint DEFAULT NULL COMMENT '民族自治地区企业所得税地方分享部分(1、无2、免征3、减征)',
  `reduction_ratio` decimal(10,8) DEFAULT NULL COMMENT '减征幅度(%)',
  `current_reduction_amount` decimal(15,2) DEFAULT NULL COMMENT '本期实际减免金额',
  `ytd_reduction_amount` decimal(15,2) DEFAULT NULL COMMENT '本机构本年累计的减免金额',
  `total_ytd_reduction_amount` decimal(15,2) DEFAULT NULL COMMENT '本年累计应减免金额(总机构及分支机构)',
  `actual_local_tax_payable` decimal(15,2) DEFAULT NULL COMMENT '地方级收入实际应纳税额',
  `actual_tax_refund` decimal(15,2) DEFAULT NULL COMMENT '实际应补(退)所得税额',
  `handler_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经办人姓名',
  `handler_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经办人身份证号',
  `agent_org` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '代理机构',
  `agent_org_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '代理机构统一社会信用代码',
  `acceptance_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '受理人',
  `acceptance_org` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '受理税务机关',
  `acceptance_date` date DEFAULT NULL COMMENT '受理日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_taxpayer_id` (`tax_id`),
  KEY `idx_taxpayer_name` (`taxpayer_name`),
  KEY `idx_tax_period` (`tax_period_start`,`tax_period_end`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='企业所得税月（季）度预缴纳税申报表（A类）';

-- ----------------------------
-- Table structure for declare_general_tax_declaration
-- ----------------------------
DROP TABLE IF EXISTS `declare_general_tax_declaration`;
CREATE TABLE `declare_general_tax_declaration` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增唯一标识',
  `taxpayer_id` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '纳税人识别号',
  `taxpayer_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '纳税人名称',
  `collection_project` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '征收项目',
  `collection_item` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '征收品目',
  `tax_period_start` date DEFAULT NULL COMMENT '税（费）款所属期起',
  `tax_period_end` date DEFAULT NULL COMMENT '税（费）款所属期止',
  `taxable_item` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应税项（总数量或收入总额、应缴费人数、原值、面积、缴费基数等）',
  `deduction_item` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '减除项（允许减除数量、金额、面积、已安排残疾人数等）',
  `tax_calculation_basis` decimal(15,2) DEFAULT NULL COMMENT '计税（费）依据',
  `tax_rate` decimal(10,6) DEFAULT NULL COMMENT '税（费）率或单位税额',
  `taxable_income_rate` decimal(10,6) DEFAULT NULL COMMENT '应税所得率',
  `quick_deduction` decimal(15,2) DEFAULT NULL COMMENT '速算扣除数',
  `current_taxable_amount` decimal(15,2) DEFAULT NULL COMMENT '本期应纳税（费）额',
  `tax_reduction_amount` decimal(15,2) DEFAULT NULL COMMENT '减免税（费）额',
  `reduction_nature` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '减免性质',
  `vat_small_scale_nature` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '增值税小规模纳税人减免性质',
  `vat_small_scale_reduction_ratio` decimal(10,6) DEFAULT NULL COMMENT '增值税小规模纳税人受减税比例（%）',
  `vat_small_scale_reduction_amount` decimal(15,2) DEFAULT NULL COMMENT '增值税小规模纳税人减征额',
  `current_paid_amount` decimal(15,2) DEFAULT NULL COMMENT '本期已缴税（费）额',
  `current_refund_amount` decimal(15,2) DEFAULT NULL COMMENT '本期应补（退）税（费）额',
  `tax_source_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '税源名称',
  `tax_source_code` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '税源编号',
  `is_rent_split` tinyint DEFAULT NULL COMMENT '其他个人出租不动产租金是否分摊',
  `rent_period_start` date DEFAULT NULL COMMENT '其他个人出租不动产租赁期起',
  `rent_period_end` date DEFAULT NULL COMMENT '其他个人出租不动产租赁期止',
  `monthly_rent_after_split` decimal(15,2) DEFAULT NULL COMMENT '其他个人出租不动产分摊后月租金收入',
  `tax_handler` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '办税人',
  `declaration_date` date DEFAULT NULL COMMENT '申报日期',
  `acceptor` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '受理人',
  `accept_date` date DEFAULT NULL COMMENT '受理日期',
  `accept_tax_authority` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '受理税务机关',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_taxpayer_period` (`taxpayer_id`,`tax_period_start`,`tax_period_end`) COMMENT '纳税人+所属期 唯一索引，避免重复申报'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通用申报表（税及附征税费）';

-- ----------------------------
-- Table structure for declare_property_and_behavior_tax_declaration
-- ----------------------------
DROP TABLE IF EXISTS `declare_property_and_behavior_tax_declaration`;
CREATE TABLE `declare_property_and_behavior_tax_declaration` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tax_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人识别号(统一社会信用代码)',
  `taxpayer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人名称',
  `serial_number` int DEFAULT NULL COMMENT '序号',
  `tax_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '税种',
  `tax_item` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '税目',
  `tax_period_start` date DEFAULT NULL COMMENT '税款所属期起',
  `tax_period_end` date DEFAULT NULL COMMENT '税款所属期止',
  `tax_base` decimal(12,2) DEFAULT NULL COMMENT '计税依据',
  `tax_rate` decimal(6,4) DEFAULT NULL COMMENT '税率',
  `tax_payable` decimal(10,2) DEFAULT NULL COMMENT '应纳税额',
  `tax_reduction` decimal(10,2) DEFAULT NULL COMMENT '减免税额',
  `tax_paid` decimal(10,2) DEFAULT NULL COMMENT '已缴税额',
  `tax_supplement_refund` decimal(10,2) DEFAULT NULL COMMENT '应补(退)税额',
  `declarant_signature` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '纳税人',
  `declaration_date` date DEFAULT NULL COMMENT '申报日期',
  `handler` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经办人',
  `handler_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经办人身份证号',
  `agency_signature` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '代理机构签章',
  `agency_credit_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '代理机构统一社会信用代码',
  `receiver` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '受理人',
  `tax_authority` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '受理税务机关(章)',
  `receive_date` date DEFAULT NULL COMMENT '受理日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='财产和行为税纳税申报表';

-- ----------------------------
-- Table structure for declare_property_behavior_tax_reduction_detail
-- ----------------------------
DROP TABLE IF EXISTS `declare_property_behavior_tax_reduction_detail`;
CREATE TABLE `declare_property_behavior_tax_reduction_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tax_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人识别号（统一社会信用代码）',
  `taxpayer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人名称',
  `is_sme_six_tax_two_fee` tinyint(1) DEFAULT NULL COMMENT '本期是否适用小微企业"六税两费"减免政策',
  `reduction_policy_subject` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '减免政策适用主体',
  `is_small_scale_vat_taxpayer` tinyint(1) DEFAULT NULL COMMENT '是否增值税小规模纳税人',
  `is_general_vat_taxpayer` tinyint(1) DEFAULT NULL COMMENT '增值税一般纳税人',
  `taxpayer_type` tinyint(1) DEFAULT NULL COMMENT '增值税一般纳税人（1、个体工商户，2、小型微利企业）',
  `reduction_period_start` date DEFAULT NULL COMMENT '适用减免政策开始时间',
  `reduction_period_end` date DEFAULT NULL COMMENT '适用减免政策结束时间',
  `total_reduction_amount` decimal(10,2) DEFAULT NULL COMMENT '合计减免税额',
  `land_tax_serial` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '城镇土地使用税-序号',
  `land_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '城镇土地使用税-土地编号',
  `land_tax_start` date DEFAULT NULL COMMENT '城镇土地使用税-税款所属期起',
  `land_tax_end` date DEFAULT NULL COMMENT '城镇土地使用税-税款所属期止',
  `land_reduction_policy` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '城镇土地使用税-减免性质代码和项目名称',
  `land_reduction_amount` decimal(10,2) DEFAULT NULL COMMENT '城镇土地使用税-减免税额',
  `property_tax_serial` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '房产税-序号',
  `property_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '房产税-房产编号',
  `property_tax_start` date DEFAULT NULL COMMENT '房产税-税款所属期起',
  `property_tax_end` date DEFAULT NULL COMMENT '房产税-税款所属期止',
  `property_reduction_policy` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '房产税-减免性质代码和项目名称',
  `property_reduction_amount` decimal(10,2) DEFAULT NULL COMMENT '房产税-减免税额',
  `vehicle_tax_serial` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '车船税-序号',
  `vehicle_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '车船税-车辆识别代码/船舶识别码',
  `vehicle_tax_start` date DEFAULT NULL COMMENT '车船税-税款所属期起',
  `vehicle_tax_end` date DEFAULT NULL COMMENT '车船税-税款所属期止',
  `vehicle_reduction_policy` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '车船税-减免性质代码和项目名称',
  `vehicle_reduction_amount` decimal(10,2) DEFAULT NULL COMMENT '车船税-减免税额',
  `stamp_tax_serial` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '印花税-序号',
  `stamp_tax_item` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '印花税-税目',
  `stamp_tax_start` date DEFAULT NULL COMMENT '印花税-税款所属期起',
  `stamp_tax_end` date DEFAULT NULL COMMENT '印花税-税款所属期止',
  `stamp_reduction_policy` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '印花税-减免性质代码和项目名称',
  `stamp_reduction_amount` decimal(10,2) DEFAULT NULL COMMENT '印花税-减免税额',
  `resource_tax_serial` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '资源税-序号',
  `resource_tax_item` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '资源税-税目',
  `resource_tax_subitem` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '资源税-子目',
  `resource_tax_start` date DEFAULT NULL COMMENT '资源税-税款所属期起',
  `resource_tax_end` date DEFAULT NULL COMMENT '资源税-税款所属期止',
  `resource_reduction_policy` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '资源税-减免性质代码和项目名称',
  `resource_reduction_amount` decimal(10,2) DEFAULT NULL COMMENT '资源税-减免税额',
  `farmland_tax_serial` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '耕地占用税-序号',
  `farmland_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '耕地占用税-税源编号',
  `farmland_tax_start` date DEFAULT NULL COMMENT '耕地占用税-税款所属期起',
  `farmland_tax_end` date DEFAULT NULL COMMENT '耕地占用税-税款所属期止',
  `farmland_reduction_policy` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '耕地占用税-减免性质代码和项目名称',
  `farmland_reduction_amount` decimal(10,2) DEFAULT NULL COMMENT '耕地占用税-减免税额',
  `deed_tax_serial` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '契税-序号',
  `deed_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '契税-税源编号',
  `deed_tax_start` date DEFAULT NULL COMMENT '契税-税款所属期起',
  `deed_tax_end` date DEFAULT NULL COMMENT '契税-税款所属期止',
  `deed_reduction_policy` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '契税-减免性质代码和项目名称',
  `deed_reduction_amount` decimal(10,2) DEFAULT NULL COMMENT '契税-减免税额',
  `land_value_tax_serial` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '土地增值税-序号',
  `land_value_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '土地增值税-项目编号',
  `land_value_tax_start` date DEFAULT NULL COMMENT '土地增值税-税款所属期起',
  `land_value_tax_end` date DEFAULT NULL COMMENT '土地增值税-税款所属期止',
  `land_value_reduction_policy` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '土地增值税-减免性质代码和项目名称',
  `land_value_reduction_amount` decimal(10,2) DEFAULT NULL COMMENT '土地增值税-减免税额',
  `environment_tax_serial` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '环境保护税-序号',
  `environment_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '环境保护税-税源编号',
  `pollutant_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '环境保护税-污染物类别',
  `pollutant_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '环境保护税-污染物名称',
  `environment_tax_start` date DEFAULT NULL COMMENT '环境保护税-税款所属期起',
  `environment_tax_end` date DEFAULT NULL COMMENT '环境保护税-税款所属期止',
  `environment_reduction_policy` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '环境保护税-减免性质代码和项目名称',
  `environment_reduction_amount` decimal(10,2) DEFAULT NULL COMMENT '环境保护税-减免税额',
  `declaration_date` date DEFAULT NULL COMMENT '申报日期',
  `handler_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经办人姓名',
  `handler_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经办人身份证号',
  `agent_org_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '代理机构统一社会信用代码',
  `acceptance_org` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '受理税务机关',
  `acceptance_date` date DEFAULT NULL COMMENT '受理日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_taxpayer_id` (`tax_id`),
  KEY `idx_taxpayer_name` (`taxpayer_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='财产和行为税减免税明细申报附表';

-- ----------------------------
-- Table structure for declare_result
-- ----------------------------
DROP TABLE IF EXISTS `declare_result`;
CREATE TABLE `declare_result` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tax_id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '税号',
  `type` int NOT NULL COMMENT '申报表类型',
  `declare_begin` date NOT NULL COMMENT '税款所属期起',
  `declare_end` date NOT NULL COMMENT '税款所属期至',
  `result` json DEFAULT NULL COMMENT '申报结果',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='申报结果';

-- ----------------------------
-- Table structure for declare_tax_basic_info
-- ----------------------------
DROP TABLE IF EXISTS `declare_tax_basic_info`;
CREATE TABLE `declare_tax_basic_info` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tax_period_start` date NOT NULL COMMENT '税款所属期间开始时间',
  `tax_period_end` date NOT NULL COMMENT '税款所属期间结束时间',
  `tax_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人识别号(统一社会信用代码)',
  `taxpayer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人名称',
  `taxpayer_seal` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '纳税人（签章）',
  `declaration_date` date DEFAULT NULL COMMENT '申报日期',
  `tax_authority` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '受理税务机关',
  `tax_authority_seal` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '受理税务机关(章)',
  `handler_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经办人姓名',
  `handler_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经办人身份证号',
  `agent_org_seal` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '代理机构签章',
  `acceptance_date` date DEFAULT NULL COMMENT '受理日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_taxpayer_period` (`tax_id`,`tax_period_start`,`tax_period_end`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='企业所得税年度纳税申报表';

-- ----------------------------
-- Table structure for declare_tax_declaration_forms
-- ----------------------------
DROP TABLE IF EXISTS `declare_tax_declaration_forms`;
CREATE TABLE `declare_tax_declaration_forms` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tax_period_start` date NOT NULL COMMENT '税款所属期间开始时间',
  `tax_period_end` date NOT NULL COMMENT '税款所属期间结束时间',
  `tax_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人识别号(统一社会信用代码)',
  `taxpayer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人名称',
  `form_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '表单编号',
  `form_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '表单名称',
  `is_fill_in` tinyint NOT NULL COMMENT '是否填报',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='企业所得税年度纳税申报表填报表单';

-- ----------------------------
-- Table structure for declare_tax_enterprise_basic_info
-- ----------------------------
DROP TABLE IF EXISTS `declare_tax_enterprise_basic_info`;
CREATE TABLE `declare_tax_enterprise_basic_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tax_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人识别号(统一社会信用代码)',
  `taxpayer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人名称',
  `tax_year` int NOT NULL COMMENT '纳税年度',
  `tax_period_start` date NOT NULL COMMENT '税款所属期间起',
  `tax_period_end` date NOT NULL COMMENT '税款所属期间止',
  `enterprise_type_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '101纳税申报企业类型(填写代码)',
  `branch_tax_ratio` decimal(5,2) DEFAULT NULL COMMENT '102分支机构就地纳税比例(%)',
  `avg_assets` decimal(12,2) DEFAULT NULL COMMENT '103资产总额(万元)',
  `avg_employees` int DEFAULT NULL COMMENT '104从业人员数(人)',
  `industry_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '105所属国民经济行业（填写代码）',
  `is_restricted_industry` tinyint(1) DEFAULT '0' COMMENT '106是否从事国家限制或禁止行业',
  `accounting_std_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '107适用会计准则名称或代码',
  `use_general_financial_format` tinyint(1) DEFAULT '0' COMMENT '108是否采用一般企业财务报表格式',
  `is_small_low_profit` tinyint(1) DEFAULT '0' COMMENT '109是否小型微利企业',
  `is_listed_company` tinyint(1) DEFAULT '0' COMMENT '110是否上市公司',
  `is_overseas_listed` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '110境内或境外上市公司',
  `has_equity_investment` tinyint(1) DEFAULT '0' COMMENT '201是否从事股权投资业务',
  `has_overseas_related_trans` tinyint(1) DEFAULT '0' COMMENT '202是否存在境外关联交易',
  `overseas_income_method` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '203-1境外所得抵免方式',
  `has_hainan_ftz_investment` tinyint(1) DEFAULT '0' COMMENT '203-2是否有海南自贸港境外投资',
  `hainan_ftz_industry` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '海南自贸港产业类别',
  `is_venture_capital_partner` tinyint(1) DEFAULT '0' COMMENT '204是否有合伙制创投法人合伙人',
  `is_venture_capital_enterprise` tinyint(1) DEFAULT '0' COMMENT '205是否是创业投资企业',
  `advanced_tech_service_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '206技术先进服务企业类型',
  `is_non_profit_org` tinyint(1) DEFAULT '0' COMMENT '207是否非营利组织',
  `software_ic_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '208软件集成电路企业类型',
  `ic_project_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '209集成电路生产项目类型(1、130纳米，2、65纳米，3、28纳米 )',
  `is_sci_tech_sme` tinyint(1) DEFAULT '0' COMMENT '210是否科技型中小企业',
  `sci_tech_sme_reg_no1` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '210-1年(申报所属期年度)入库编号1',
  `sci_tech_sme_reg_date1` date DEFAULT NULL COMMENT '210-2入库时间1',
  `sci_tech_sme_reg_no2` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '210-3年(所属期下一年度)入库编号2',
  `sci_tech_sme_reg_date2` date DEFAULT NULL COMMENT '210-4入库时间2',
  `is_high_tech_enterprise` tinyint(1) DEFAULT '0' COMMENT '211是否高新技术企业',
  `high_tech_cert_no1` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '211-1证书编号1',
  `high_tech_cert_date1` date DEFAULT NULL COMMENT '211-2发证时间1',
  `high_tech_cert_no2` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '211-3证书编号2',
  `high_tech_cert_date2` date DEFAULT NULL COMMENT '211-4发证时间2',
  `reorganization_tax_method` tinyint DEFAULT NULL COMMENT '212重组事项税务处理方式(1、一般性，2、特殊性)',
  `reorganization_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '213重组交易类型',
  `reorganization_party_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '214重组当事方类型',
  `policy_relocation_start_date` date DEFAULT NULL COMMENT '215政策性搬迁开始时间',
  `has_policy_relocation_no_income` tinyint(1) DEFAULT '0' COMMENT '216是否政策性搬迁无所得年度',
  `is_policy_relocation_loss_deduction` tinyint(1) DEFAULT '0' COMMENT '217是否政策性搬迁损失分期扣除',
  `has_non_monetary_investment` tinyint(1) DEFAULT '0' COMMENT '218是否有非货币性资产投资递延',
  `non_monetary_investment_year` tinyint(1) DEFAULT '0' COMMENT '219非货币性资产投资递延年度',
  `has_tech_achievement_investment` tinyint(1) DEFAULT '0' COMMENT '220是否有技术成果投资入股递延',
  `tech_achievement_investment_year` tinyint(1) DEFAULT '0' COMMENT '221技术成果投资入股递延年度',
  `has_asset_equity_transfer` tinyint(1) DEFAULT '0' COMMENT '222是否有资产(股权)划转特殊性税务',
  `debt_reorganization_deferred_year` tinyint(1) DEFAULT '0' COMMENT '223债务重组所得递延年度',
  `shareholder_info` json DEFAULT NULL COMMENT '主要股东及分红情况',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='纳税调整项目明细表(A000000)';

-- ----------------------------
-- Table structure for declare_tax_policy
-- ----------------------------
DROP TABLE IF EXISTS `declare_tax_policy`;
CREATE TABLE `declare_tax_policy` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tax_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '税号',
  `tax_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '税种类型（如：印花税、增值税等）',
  `item_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '征收品目',
  `sub_item_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '征收子目',
  `tax_period` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '纳税期限',
  `report_period` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '申报期限',
  `tax_rate` decimal(6,6) DEFAULT '0.000000' COMMENT '税率或单位税额',
  `main_or_surcharge` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '主附税标志（主税/增值税附税）',
  `collection_method` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '征收代理方式（自行申报/代扣代缴）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='税收政策表';

-- ----------------------------
-- Table structure for declare_vat_returns_small_scale_merged
-- ----------------------------
DROP TABLE IF EXISTS `declare_vat_returns_small_scale_merged`;
CREATE TABLE `declare_vat_returns_small_scale_merged` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '原主表自增主键',
  `tax_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '税号（统一社会信用代码）',
  `taxpayer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人名称',
  `tax_period_start` date NOT NULL COMMENT '税款所属期起始日',
  `tax_period_end` date NOT NULL COMMENT '税款所属期截止日',
  `filling_date` date DEFAULT NULL COMMENT '填表日期',
  `declare_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '经办人',
  `agent_company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '代理机构签章',
  `tax_authority` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '受理税务机关',
  `accept_date` date DEFAULT NULL COMMENT '受理日期',
  `basis_taxable_sales_3pct` decimal(12,2) DEFAULT NULL COMMENT '应征增值税不含税销售额(3%征收率)，服务、不动产和无形资产本期数',
  `basis_special_invoice_sales_3pct` decimal(12,2) DEFAULT NULL COMMENT '增值税专用发票不含税销售额(3%征收率)，服务、不动产和无形资产本期数',
  `basis_other_invoice_sales_3pct` decimal(12,2) DEFAULT NULL COMMENT '其他增值税发票不含税销售额(3%征收率)，服务、不动产和无形资产本期数',
  `basis_taxable_sales_5pct` decimal(12,2) DEFAULT NULL COMMENT '应征增值税不含税销售额(5%征收率)，服务、不动产和无形资产本期数',
  `basis_special_invoice_sales_5pct` decimal(12,2) DEFAULT NULL COMMENT '增值税专用发票不含税销售额(5%征收率)，服务、不动产和无形资产本期数',
  `basis_other_invoice_sales_5pct` decimal(12,2) DEFAULT NULL COMMENT '其他增值税发票不含税销售额(5%征收率)，服务、不动产和无形资产本期数',
  `basis_fixed_asset_sales` decimal(12,2) DEFAULT NULL COMMENT '销售使用过的固定资产不含税销售额，货物及劳务本期数',
  `basis_fixed_asset_other_invoice` decimal(12,2) DEFAULT NULL COMMENT '销售使用过的固定资产其他发票销售额，货物及劳务本期数',
  `basis_tax_exempt_sales` decimal(12,2) DEFAULT NULL COMMENT '免税销售额，服务、不动产和无形资产本期数',
  `basis_micro_enterprise_exempt` decimal(12,2) DEFAULT NULL COMMENT '小微企业免税销售额，服务、不动产和无形资产本期数',
  `basis_under_threshold_exempt` decimal(12,2) DEFAULT NULL COMMENT '未达起征点销售额，服务、不动产和无形资产本期数',
  `basis_other_exempt_sales` decimal(12,2) DEFAULT NULL COMMENT '其他免税销售额，服务、不动产和无形资产本期数',
  `basis_export_exempt_sales` decimal(12,2) DEFAULT NULL COMMENT '出口免税销售额，服务、不动产和无形资产本期数',
  `calc_tax_payable` decimal(12,2) DEFAULT NULL COMMENT '本期应纳税额，服务、不动产和无形资产本期数',
  `calc_tax_reduction` decimal(12,2) DEFAULT NULL COMMENT '本期应纳税额减征额，服务、不动产和无形资产本期数',
  `calc_tax_exemption` decimal(12,2) DEFAULT NULL COMMENT '本期免税额，服务、不动产和无形资产本期数',
  `calc_micro_enterprise_exemption` decimal(12,2) DEFAULT NULL COMMENT '小微企业免税额，服务、不动产和无形资产本期数',
  `calc_total_tax_payable` decimal(12,2) DEFAULT NULL COMMENT '应纳税额合计，服务、不动产和无形资产本期数',
  `calc_prepaid_tax` decimal(12,2) DEFAULT NULL COMMENT '本期预缴税额，服务、不动产和无形资产本期数',
  `calc_tax_to_supplement` decimal(12,2) DEFAULT NULL COMMENT '本期应补(退)税额，服务、不动产和无形资产本期数',
  `surtax_city_maintenance_tax` decimal(12,2) DEFAULT NULL COMMENT '城市维护建设税本期应补(退)税额',
  `surtax_education_surcharge` decimal(12,2) DEFAULT NULL COMMENT '教育费附加本期应补(退)费额',
  `surtax_local_education_surcharge` decimal(12,2) DEFAULT NULL COMMENT '地方教育附加本期应补(退)费额',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小规模纳税人增值税申报合并表（主表+计税依据+税款计算+附加税费）';

-- ----------------------------
-- Table structure for declare_vat_small_scale_appendix_one
-- ----------------------------
DROP TABLE IF EXISTS `declare_vat_small_scale_appendix_one`;
CREATE TABLE `declare_vat_small_scale_appendix_one` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `tax_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '税号（统一社会信用代码）',
  `taxpayer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人名称',
  `tax_period_start` date NOT NULL COMMENT '税款所属期起始日',
  `tax_period_end` date NOT NULL COMMENT '税款所属期截止日',
  `filling_date` date NOT NULL COMMENT '填表日期',
  `deduct_3pct_begin` decimal(12,2) DEFAULT NULL COMMENT '3%征收率-期初余额（栏1）',
  `deduct_3pct_current` decimal(12,2) DEFAULT NULL COMMENT '3%征收率-本期发生额（栏2）',
  `deduct_3pct_deducted` decimal(12,2) DEFAULT NULL COMMENT '3%征收率-本期扣除额（栏3）',
  `deduct_3pct_end` decimal(12,2) DEFAULT NULL COMMENT '3%征收率-期末余额（栏4）',
  `sales_3pct_total` decimal(12,2) DEFAULT NULL COMMENT '3%征收率-全部含税收入（栏5）',
  `sales_3pct_deducted` decimal(12,2) DEFAULT NULL COMMENT '3%征收率-本期扣除额（栏6）',
  `sales_3pct_taxable` decimal(12,2) DEFAULT NULL COMMENT '3%征收率-含税销售额（栏7）',
  `sales_3pct_tax_free` decimal(12,2) DEFAULT NULL COMMENT '3%征收率-不含税销售额（栏8）',
  `deduct_5pct_begin` decimal(12,2) DEFAULT NULL COMMENT '5%征收率-期初余额（栏9）',
  `deduct_5pct_current` decimal(12,2) DEFAULT NULL COMMENT '5%征收率-本期发生额（栏10）',
  `deduct_5pct_deducted` decimal(12,2) DEFAULT NULL COMMENT '5%征收率-本期扣除额（栏11）',
  `deduct_5pct_end` decimal(12,2) DEFAULT NULL COMMENT '5%征收率-期末余额（栏12）',
  `sales_5pct_total` decimal(12,2) DEFAULT NULL COMMENT '5%征收率-全部含税收入（栏13）',
  `sales_5pct_deducted` decimal(12,2) DEFAULT NULL COMMENT '5%征收率-本期扣除额（栏14）',
  `sales_5pct_taxable` decimal(12,2) DEFAULT NULL COMMENT '5%征收率-含税销售额（栏15）',
  `sales_5pct_tax_free` decimal(12,2) DEFAULT NULL COMMENT '5%征收率-不含税销售额（栏16）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小规模纳税人增值税附列资料（一）（服务、不动产和无形资产扣除项目明细）';

-- ----------------------------
-- Table structure for declare_vat_small_scale_appendix_two
-- ----------------------------
DROP TABLE IF EXISTS `declare_vat_small_scale_appendix_two`;
CREATE TABLE `declare_vat_small_scale_appendix_two` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `tax_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '税号',
  `taxpayer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '纳税人名称',
  `tax_period_start` date NOT NULL COMMENT '税款所属期起始',
  `tax_period_end` date NOT NULL COMMENT '税款所属期截止',
  `tax_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '税（费）种',
  `vat_tax_amount` decimal(10,2) DEFAULT NULL COMMENT '计税（费）依据-增值税税额（列1）',
  `tax_rate` decimal(5,4) DEFAULT NULL COMMENT '税（费）率（%）（列2）',
  `current_tax_payable` decimal(10,2) DEFAULT NULL COMMENT '本期应纳税（费）额（列3=列1×列2）',
  `exemption_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '减免性质代码（列4）',
  `exemption_amount` decimal(10,2) DEFAULT NULL COMMENT '减免税（费）额（列5）',
  `reduction_ratio` decimal(5,2) DEFAULT NULL COMMENT '减征比例（%）（列6）',
  `reduction_amount` decimal(10,2) DEFAULT NULL COMMENT '减征额（列7=(列3-列5)×列6 ）',
  `prepaid_tax` decimal(10,2) DEFAULT NULL COMMENT '本期已缴税（费）额（列8）',
  `current_tax_adjust` decimal(10,2) DEFAULT NULL COMMENT '本期应补（退）税（费）额（列9=列3-列5-列7-列8 ）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小规模纳税人增值税附列资料（二）（附加税费情况表）';

-- ----------------------------
-- Table structure for declare_vat_tax_relief_declaration
-- ----------------------------
DROP TABLE IF EXISTS `declare_vat_tax_relief_declaration`;
CREATE TABLE `declare_vat_tax_relief_declaration` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `tax_id` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '税号',
  `taxpayer_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '纳税人名称',
  `tax_period_start` date NOT NULL COMMENT '税款所属期起始日',
  `tax_period_end` date NOT NULL COMMENT '税款所属期截止日',
  `relief_code_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '减免性质代码及名称',
  `opening_balance` decimal(10,2) DEFAULT NULL COMMENT '期初余额（列1）',
  `current_occurrence` decimal(10,2) DEFAULT NULL COMMENT '本期发生额（列2）',
  `current_entitlement` decimal(10,2) DEFAULT NULL COMMENT '本期应抵减税额（列3=1+2）',
  `current_actual` decimal(10,2) DEFAULT NULL COMMENT '本期实际抵减税额（列4≤3）',
  `closing_balance` decimal(10,2) DEFAULT NULL COMMENT '期末余额（列5=3-4）',
  `exempt_code_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '免税性质代码及名称',
  `exempt_sales` decimal(10,2) DEFAULT NULL COMMENT '免征增值税项目销售额（列1）',
  `exempt_deduction` decimal(10,2) DEFAULT NULL COMMENT '免税销售额扣除项目本期实际扣除金额（列2）',
  `exempt_sales_after` decimal(10,2) DEFAULT NULL COMMENT '扣除后免税销售额（列3=1-2）',
  `exempt_input_tax` decimal(10,2) DEFAULT NULL COMMENT '免税销售额对应的进项税额（列4）',
  `exempt_amount` decimal(10,2) DEFAULT NULL COMMENT '免税额（列5）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='增值税减免税申报明细表（含减税项目和免税项目）';

-- ----------------------------
-- Table structure for invoice_purchase
-- ----------------------------
DROP TABLE IF EXISTS `invoice_purchase`;
CREATE TABLE `invoice_purchase` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属任务编号',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '销方税号',
  `zzfpdm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '发票代码',
  `zzfphm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票号码',
  `fpkjfxlxdm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数电票号码',
  `gmfmc` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '购方名称',
  `gmfnsrsbh` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '购方识别号',
  `kprq` datetime NOT NULL COMMENT '开票日期',
  `hjje` decimal(10,2) DEFAULT '0.00' COMMENT '金额',
  `hjse` decimal(10,2) DEFAULT '0.00' COMMENT '税额',
  `jshj` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价税合计',
  `fplydm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票来源',
  `fppzdm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票票种',
  `fpztdm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票状态',
  `sflzfp` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票风险等级',
  `kpr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '开票人',
  `bz` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `kprqn` int DEFAULT NULL COMMENT '开票日期年',
  `kprqy` int DEFAULT NULL COMMENT '开票日期月',
  `gather_datetime` datetime NOT NULL COMMENT '采集时间',
  `deduction` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '是否可以抵扣',
  `checkout` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '是否被勾选确认',
  `checkout_datetime` datetime DEFAULT NULL COMMENT '勾选日期',
  `confirm_task_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '勾选任务ID',
  `ofd_file` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '原始发票文件',
  `expand_content` json DEFAULT NULL COMMENT '发票扩充内容：火车票、二手车销售票、机票行程单等',
  `create_time` datetime DEFAULT (now()) COMMENT '创建时间',
  PRIMARY KEY (`id`,`kprq`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销项发票-发票基础信息'
/*!50500 PARTITION BY RANGE  COLUMNS(kprq)
(PARTITION p202401 VALUES LESS THAN ('2024-02-01') ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN ('2024-03-01') ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN ('2024-04-01') ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN ('2024-05-01') ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN ('2024-06-01') ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN ('2024-07-01') ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN ('2024-08-01') ENGINE = InnoDB,
 PARTITION p202408 VALUES LESS THAN ('2024-09-01') ENGINE = InnoDB,
 PARTITION p202409 VALUES LESS THAN ('2024-10-01') ENGINE = InnoDB,
 PARTITION p202410 VALUES LESS THAN ('2024-11-01') ENGINE = InnoDB,
 PARTITION p202411 VALUES LESS THAN ('2024-12-01') ENGINE = InnoDB,
 PARTITION p202412 VALUES LESS THAN ('2025-01-01') ENGINE = InnoDB,
 PARTITION p202501 VALUES LESS THAN ('2025-02-01') ENGINE = InnoDB,
 PARTITION p202502 VALUES LESS THAN ('2025-03-01') ENGINE = InnoDB,
 PARTITION p202503 VALUES LESS THAN ('2025-04-01') ENGINE = InnoDB,
 PARTITION p202504 VALUES LESS THAN ('2025-05-01') ENGINE = InnoDB,
 PARTITION p202505 VALUES LESS THAN ('2025-06-01') ENGINE = InnoDB,
 PARTITION p202506 VALUES LESS THAN ('2025-07-01') ENGINE = InnoDB,
 PARTITION p202507 VALUES LESS THAN ('2025-08-01') ENGINE = InnoDB,
 PARTITION p202508 VALUES LESS THAN ('2025-09-01') ENGINE = InnoDB,
 PARTITION p202509 VALUES LESS THAN ('2025-10-01') ENGINE = InnoDB,
 PARTITION p202510 VALUES LESS THAN ('2025-11-01') ENGINE = InnoDB,
 PARTITION p202511 VALUES LESS THAN ('2025-12-01') ENGINE = InnoDB,
 PARTITION p202512 VALUES LESS THAN ('2026-01-01') ENGINE = InnoDB,
 PARTITION p202601 VALUES LESS THAN ('2026-02-01') ENGINE = InnoDB,
 PARTITION p202602 VALUES LESS THAN ('2026-03-01') ENGINE = InnoDB,
 PARTITION p202603 VALUES LESS THAN ('2026-04-01') ENGINE = InnoDB,
 PARTITION p202604 VALUES LESS THAN ('2026-05-01') ENGINE = InnoDB,
 PARTITION p202605 VALUES LESS THAN ('2026-06-01') ENGINE = InnoDB,
 PARTITION p202606 VALUES LESS THAN ('2026-07-01') ENGINE = InnoDB,
 PARTITION p202607 VALUES LESS THAN ('2026-08-01') ENGINE = InnoDB,
 PARTITION p202608 VALUES LESS THAN ('2026-09-01') ENGINE = InnoDB,
 PARTITION p202609 VALUES LESS THAN ('2026-10-01') ENGINE = InnoDB,
 PARTITION p202610 VALUES LESS THAN ('2026-11-01') ENGINE = InnoDB,
 PARTITION p202611 VALUES LESS THAN ('2026-12-01') ENGINE = InnoDB,
 PARTITION p202612 VALUES LESS THAN ('2027-01-01') ENGINE = InnoDB,
 PARTITION pother VALUES LESS THAN (MAXVALUE) ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for invoice_purchase_detail
-- ----------------------------
DROP TABLE IF EXISTS `invoice_purchase_detail`;
CREATE TABLE `invoice_purchase_detail` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属任务编号',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '销方税号',
  `zzfpdm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '发票代码',
  `zzfphm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票号码',
  `fpkjfxlxdm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数电票号码',
  `gmfmc` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '购方名称',
  `gmfnsrsbh` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '购方识别号',
  `kprq` datetime NOT NULL COMMENT '开票日期',
  `ssflbm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '税收分类编码',
  `tdywlx` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '特定业务类型',
  `hwmc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '货物或应税劳务名称',
  `easy_hwmc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '货物名称',
  `ggxh` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规格型号',
  `dw` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计量单位名称',
  `dw_symbol` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计量单位符号',
  `xssl` decimal(24,6) DEFAULT '0.000000' COMMENT '数量',
  `dj` decimal(24,8) DEFAULT '0.00000000' COMMENT '单价',
  `hjje` decimal(10,2) DEFAULT '0.00' COMMENT '金额',
  `sl` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '税率',
  `hjse` decimal(10,2) DEFAULT '0.00' COMMENT '税额',
  `jshj` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价税合计',
  `fplydm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票来源',
  `fppzdm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票票种',
  `fpztdm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票状态',
  `sflzfp` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票风险等级',
  `kpr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '开票人',
  `bz` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `kprqn` int DEFAULT NULL COMMENT '开票日期年',
  `kprqy` int DEFAULT NULL COMMENT '开票日期月',
  `gather_datetime` datetime DEFAULT NULL COMMENT '采集时间',
  `create_time` datetime DEFAULT (now()) COMMENT '创建时间',
  PRIMARY KEY (`id`,`kprq`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='进项发票-信息汇总表'
/*!50500 PARTITION BY RANGE  COLUMNS(kprq)
(PARTITION p202401 VALUES LESS THAN ('2024-02-01') ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN ('2024-03-01') ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN ('2024-04-01') ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN ('2024-05-01') ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN ('2024-06-01') ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN ('2024-07-01') ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN ('2024-08-01') ENGINE = InnoDB,
 PARTITION p202408 VALUES LESS THAN ('2024-09-01') ENGINE = InnoDB,
 PARTITION p202409 VALUES LESS THAN ('2024-10-01') ENGINE = InnoDB,
 PARTITION p202410 VALUES LESS THAN ('2024-11-01') ENGINE = InnoDB,
 PARTITION p202411 VALUES LESS THAN ('2024-12-01') ENGINE = InnoDB,
 PARTITION p202412 VALUES LESS THAN ('2025-01-01') ENGINE = InnoDB,
 PARTITION p202501 VALUES LESS THAN ('2025-02-01') ENGINE = InnoDB,
 PARTITION p202502 VALUES LESS THAN ('2025-03-01') ENGINE = InnoDB,
 PARTITION p202503 VALUES LESS THAN ('2025-04-01') ENGINE = InnoDB,
 PARTITION p202504 VALUES LESS THAN ('2025-05-01') ENGINE = InnoDB,
 PARTITION p202505 VALUES LESS THAN ('2025-06-01') ENGINE = InnoDB,
 PARTITION p202506 VALUES LESS THAN ('2025-07-01') ENGINE = InnoDB,
 PARTITION p202507 VALUES LESS THAN ('2025-08-01') ENGINE = InnoDB,
 PARTITION p202508 VALUES LESS THAN ('2025-09-01') ENGINE = InnoDB,
 PARTITION p202509 VALUES LESS THAN ('2025-10-01') ENGINE = InnoDB,
 PARTITION p202510 VALUES LESS THAN ('2025-11-01') ENGINE = InnoDB,
 PARTITION p202511 VALUES LESS THAN ('2025-12-01') ENGINE = InnoDB,
 PARTITION p202512 VALUES LESS THAN ('2026-01-01') ENGINE = InnoDB,
 PARTITION p202601 VALUES LESS THAN ('2026-02-01') ENGINE = InnoDB,
 PARTITION p202602 VALUES LESS THAN ('2026-03-01') ENGINE = InnoDB,
 PARTITION p202603 VALUES LESS THAN ('2026-04-01') ENGINE = InnoDB,
 PARTITION p202604 VALUES LESS THAN ('2026-05-01') ENGINE = InnoDB,
 PARTITION p202605 VALUES LESS THAN ('2026-06-01') ENGINE = InnoDB,
 PARTITION p202606 VALUES LESS THAN ('2026-07-01') ENGINE = InnoDB,
 PARTITION p202607 VALUES LESS THAN ('2026-08-01') ENGINE = InnoDB,
 PARTITION p202608 VALUES LESS THAN ('2026-09-01') ENGINE = InnoDB,
 PARTITION p202609 VALUES LESS THAN ('2026-10-01') ENGINE = InnoDB,
 PARTITION p202610 VALUES LESS THAN ('2026-11-01') ENGINE = InnoDB,
 PARTITION p202611 VALUES LESS THAN ('2026-12-01') ENGINE = InnoDB,
 PARTITION p202612 VALUES LESS THAN ('2027-01-01') ENGINE = InnoDB,
 PARTITION pother VALUES LESS THAN (MAXVALUE) ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for invoice_sales
-- ----------------------------
DROP TABLE IF EXISTS `invoice_sales`;
CREATE TABLE `invoice_sales` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属任务编号',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '销方税号',
  `zzfpdm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '发票代码',
  `zzfphm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票号码',
  `fpkjfxlxdm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数电票号码',
  `gmfmc` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '购方名称',
  `gmfnsrsbh` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '购方识别号',
  `kprq` datetime NOT NULL COMMENT '开票日期',
  `hjje` decimal(10,2) DEFAULT '0.00' COMMENT '金额',
  `hjse` decimal(10,2) DEFAULT '0.00' COMMENT '税额',
  `jshj` decimal(10,2) NOT NULL COMMENT '价税合计',
  `fplydm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票来源',
  `fppzdm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票票种',
  `fpztdm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票状态',
  `sflzfp` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票风险等级',
  `kpr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '开票人',
  `bz` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `kprqn` int DEFAULT NULL COMMENT '开票日期年',
  `kprqy` int DEFAULT NULL COMMENT '开票日期月',
  `gather_datetime` datetime NOT NULL COMMENT '采集时间',
  `ofd_file` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '原始发票文件',
  `expand_content` json DEFAULT NULL COMMENT '发票扩充内容：火车票、二手车销售票、机票行程单等',
  `create_time` datetime DEFAULT (now()) COMMENT '创建时间',
  PRIMARY KEY (`id`,`kprq`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销项发票-发票基础信息'
/*!50500 PARTITION BY RANGE  COLUMNS(kprq)
(PARTITION p202401 VALUES LESS THAN ('2024-02-01') ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN ('2024-03-01') ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN ('2024-04-01') ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN ('2024-05-01') ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN ('2024-06-01') ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN ('2024-07-01') ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN ('2024-08-01') ENGINE = InnoDB,
 PARTITION p202408 VALUES LESS THAN ('2024-09-01') ENGINE = InnoDB,
 PARTITION p202409 VALUES LESS THAN ('2024-10-01') ENGINE = InnoDB,
 PARTITION p202410 VALUES LESS THAN ('2024-11-01') ENGINE = InnoDB,
 PARTITION p202411 VALUES LESS THAN ('2024-12-01') ENGINE = InnoDB,
 PARTITION p202412 VALUES LESS THAN ('2025-01-01') ENGINE = InnoDB,
 PARTITION p202501 VALUES LESS THAN ('2025-02-01') ENGINE = InnoDB,
 PARTITION p202502 VALUES LESS THAN ('2025-03-01') ENGINE = InnoDB,
 PARTITION p202503 VALUES LESS THAN ('2025-04-01') ENGINE = InnoDB,
 PARTITION p202504 VALUES LESS THAN ('2025-05-01') ENGINE = InnoDB,
 PARTITION p202505 VALUES LESS THAN ('2025-06-01') ENGINE = InnoDB,
 PARTITION p202506 VALUES LESS THAN ('2025-07-01') ENGINE = InnoDB,
 PARTITION p202507 VALUES LESS THAN ('2025-08-01') ENGINE = InnoDB,
 PARTITION p202508 VALUES LESS THAN ('2025-09-01') ENGINE = InnoDB,
 PARTITION p202509 VALUES LESS THAN ('2025-10-01') ENGINE = InnoDB,
 PARTITION p202510 VALUES LESS THAN ('2025-11-01') ENGINE = InnoDB,
 PARTITION p202511 VALUES LESS THAN ('2025-12-01') ENGINE = InnoDB,
 PARTITION p202512 VALUES LESS THAN ('2026-01-01') ENGINE = InnoDB,
 PARTITION p202601 VALUES LESS THAN ('2026-02-01') ENGINE = InnoDB,
 PARTITION p202602 VALUES LESS THAN ('2026-03-01') ENGINE = InnoDB,
 PARTITION p202603 VALUES LESS THAN ('2026-04-01') ENGINE = InnoDB,
 PARTITION p202604 VALUES LESS THAN ('2026-05-01') ENGINE = InnoDB,
 PARTITION p202605 VALUES LESS THAN ('2026-06-01') ENGINE = InnoDB,
 PARTITION p202606 VALUES LESS THAN ('2026-07-01') ENGINE = InnoDB,
 PARTITION p202607 VALUES LESS THAN ('2026-08-01') ENGINE = InnoDB,
 PARTITION p202608 VALUES LESS THAN ('2026-09-01') ENGINE = InnoDB,
 PARTITION p202609 VALUES LESS THAN ('2026-10-01') ENGINE = InnoDB,
 PARTITION p202610 VALUES LESS THAN ('2026-11-01') ENGINE = InnoDB,
 PARTITION p202611 VALUES LESS THAN ('2026-12-01') ENGINE = InnoDB,
 PARTITION p202612 VALUES LESS THAN ('2027-01-01') ENGINE = InnoDB,
 PARTITION pother VALUES LESS THAN (MAXVALUE) ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for invoice_sales_detail
-- ----------------------------
DROP TABLE IF EXISTS `invoice_sales_detail`;
CREATE TABLE `invoice_sales_detail` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_Id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属任务编号',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '销方税号',
  `zzfpdm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '发票代码',
  `zzfphm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票号码',
  `fpkjfxlxdm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数电票号码',
  `gmfmc` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '购方名称',
  `gmfnsrsbh` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '购方识别号',
  `kprq` datetime NOT NULL COMMENT '开票日期',
  `ssflbm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '税收分类编码',
  `tdywlx` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '特定业务类型',
  `hwmc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '货物或应税劳务名称',
  `easy_hwmc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '货物名称',
  `ggxh` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规格型号',
  `dw` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计量单位名称',
  `dw_symbol` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计量单位符号',
  `xssl` decimal(24,6) DEFAULT NULL COMMENT '数量',
  `dj` decimal(24,8) DEFAULT '0.00000000' COMMENT '单价',
  `hjje` decimal(24,2) DEFAULT NULL COMMENT '金额',
  `sl` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '税率',
  `hjse` decimal(10,2) DEFAULT '0.00' COMMENT '税额',
  `jshj` decimal(10,2) NOT NULL COMMENT '价税合计',
  `fplydm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票来源',
  `fppzdm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票票种',
  `fpztdm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票状态',
  `sflzfp` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票风险等级',
  `kpr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '开票人',
  `bz` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `kprqn` int DEFAULT NULL COMMENT '开票日期年',
  `kprqy` int DEFAULT NULL COMMENT '开票日期月',
  `gather_datetime` datetime DEFAULT NULL COMMENT '采集时间',
  `create_time` datetime DEFAULT (now()) COMMENT '创建时间',
  PRIMARY KEY (`id`,`kprq`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销项发票-信息汇总表'
/*!50500 PARTITION BY RANGE  COLUMNS(kprq)
(PARTITION p202401 VALUES LESS THAN ('2024-02-01') ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN ('2024-03-01') ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN ('2024-04-01') ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN ('2024-05-01') ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN ('2024-06-01') ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN ('2024-07-01') ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN ('2024-08-01') ENGINE = InnoDB,
 PARTITION p202408 VALUES LESS THAN ('2024-09-01') ENGINE = InnoDB,
 PARTITION p202409 VALUES LESS THAN ('2024-10-01') ENGINE = InnoDB,
 PARTITION p202410 VALUES LESS THAN ('2024-11-01') ENGINE = InnoDB,
 PARTITION p202411 VALUES LESS THAN ('2024-12-01') ENGINE = InnoDB,
 PARTITION p202412 VALUES LESS THAN ('2025-01-01') ENGINE = InnoDB,
 PARTITION p202501 VALUES LESS THAN ('2025-02-01') ENGINE = InnoDB,
 PARTITION p202502 VALUES LESS THAN ('2025-03-01') ENGINE = InnoDB,
 PARTITION p202503 VALUES LESS THAN ('2025-04-01') ENGINE = InnoDB,
 PARTITION p202504 VALUES LESS THAN ('2025-05-01') ENGINE = InnoDB,
 PARTITION p202505 VALUES LESS THAN ('2025-06-01') ENGINE = InnoDB,
 PARTITION p202506 VALUES LESS THAN ('2025-07-01') ENGINE = InnoDB,
 PARTITION p202507 VALUES LESS THAN ('2025-08-01') ENGINE = InnoDB,
 PARTITION p202508 VALUES LESS THAN ('2025-09-01') ENGINE = InnoDB,
 PARTITION p202509 VALUES LESS THAN ('2025-10-01') ENGINE = InnoDB,
 PARTITION p202510 VALUES LESS THAN ('2025-11-01') ENGINE = InnoDB,
 PARTITION p202511 VALUES LESS THAN ('2025-12-01') ENGINE = InnoDB,
 PARTITION p202512 VALUES LESS THAN ('2026-01-01') ENGINE = InnoDB,
 PARTITION p202601 VALUES LESS THAN ('2026-02-01') ENGINE = InnoDB,
 PARTITION p202602 VALUES LESS THAN ('2026-03-01') ENGINE = InnoDB,
 PARTITION p202603 VALUES LESS THAN ('2026-04-01') ENGINE = InnoDB,
 PARTITION p202604 VALUES LESS THAN ('2026-05-01') ENGINE = InnoDB,
 PARTITION p202605 VALUES LESS THAN ('2026-06-01') ENGINE = InnoDB,
 PARTITION p202606 VALUES LESS THAN ('2026-07-01') ENGINE = InnoDB,
 PARTITION p202607 VALUES LESS THAN ('2026-08-01') ENGINE = InnoDB,
 PARTITION p202608 VALUES LESS THAN ('2026-09-01') ENGINE = InnoDB,
 PARTITION p202609 VALUES LESS THAN ('2026-10-01') ENGINE = InnoDB,
 PARTITION p202610 VALUES LESS THAN ('2026-11-01') ENGINE = InnoDB,
 PARTITION p202611 VALUES LESS THAN ('2026-12-01') ENGINE = InnoDB,
 PARTITION p202612 VALUES LESS THAN ('2027-01-01') ENGINE = InnoDB,
 PARTITION pother VALUES LESS THAN (MAXVALUE) ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for sys_database
-- ----------------------------
DROP TABLE IF EXISTS `sys_database`;
CREATE TABLE `sys_database` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `db_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据源名称',
  `driver_class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '驱动class',
  `host` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '主机',
  `port` int DEFAULT NULL COMMENT '端口',
  `library_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '库名',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统数据库';

-- ----------------------------
-- Table structure for tax_arrears
-- ----------------------------
DROP TABLE IF EXISTS `tax_arrears`;
CREATE TABLE `tax_arrears` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `tax_id` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '税号',
  `tax_type` tinyint NOT NULL COMMENT '税种',
  `outstanding_tax_amount` decimal(16,2) NOT NULL COMMENT '欠税额',
  `current_tax_amount` decimal(16,2) NOT NULL COMMENT '本期新增欠税额',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_tax_id` (`tax_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='欠税公告';

-- ----------------------------
-- Table structure for zjks_auxiliary_dept
-- ----------------------------
DROP TABLE IF EXISTS `zjks_auxiliary_dept`;
CREATE TABLE `zjks_auxiliary_dept` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录号',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属企业税号',
  `owner_year` int NOT NULL DEFAULT (0) COMMENT '所属年份',
  `dept_code` int NOT NULL DEFAULT (0) COMMENT '部门代码',
  `dept_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '部门名称',
  `descs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `tax_id_owner_year_dept_code` (`tax_id`,`owner_year`,`dept_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1412 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='辅助核算-部门';

-- ----------------------------
-- Table structure for zjks_auxiliary_enterprise
-- ----------------------------
DROP TABLE IF EXISTS `zjks_auxiliary_enterprise`;
CREATE TABLE `zjks_auxiliary_enterprise` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录号',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属企业税号',
  `owner_year` int NOT NULL DEFAULT (0) COMMENT '所属年份',
  `category` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'c' COMMENT 'c 客户 s 供应商',
  `enterprise_code` int NOT NULL DEFAULT '0' COMMENT '往来单位代码 10000 段客户 20000段供应商',
  `enterprise_tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '往来单位税号',
  `enterprise_name` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '往来单位名称',
  `descs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `subject_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '上次发生科目',
  `create_time` datetime DEFAULT (now()) COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `tax_id_owner_year_enterprise_code` (`tax_id`,`owner_year`,`enterprise_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=44939 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='辅助核算-往来单位';

-- ----------------------------
-- Table structure for zjks_auxiliary_goods
-- ----------------------------
DROP TABLE IF EXISTS `zjks_auxiliary_goods`;
CREATE TABLE `zjks_auxiliary_goods` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '税号',
  `owner_year` int NOT NULL COMMENT '所属年份',
  `goods_code` int NOT NULL DEFAULT (0) COMMENT '商品编号',
  `goods` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',
  `goods_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品类别(1403 原材料 1405库存商品)',
  `types` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规格型号',
  `unit` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计量单位',
  `symbol` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计量单位符号',
  `storage_id` int DEFAULT NULL COMMENT '存放仓库',
  `min_number` decimal(20,6) DEFAULT '0.000000' COMMENT '最低库存量',
  `max_number` decimal(20,6) DEFAULT '0.000000' COMMENT '最高库存量',
  `valuation_method` int DEFAULT NULL COMMENT '计价方法1-全月平均 2 移动加权',
  `gross_margin` decimal(24,2) DEFAULT '0.00' COMMENT '毛利率',
  `cost_calculation` int DEFAULT NULL COMMENT '成本计算方式:0 移动加权平均法 1 BOM配比法 2毛利率法',
  `classify_code` int DEFAULT NULL COMMENT '所属类别代码',
  `match_types` int DEFAULT '0' COMMENT '是否匹配规格型号 0 不匹配 1匹配',
  `match_unit` int DEFAULT '1' COMMENT '是否匹配计量单位 0 不匹配 1匹配',
  `ssflbm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '税收分类编码，用于确定商品类别',
  `quantity` decimal(20,6) DEFAULT '0.000000' COMMENT '原材料数量或占比',
  `main_raw_material` int DEFAULT '0' COMMENT '是否为主要原材料0不是 1是',
  `goods_price` decimal(20,2) DEFAULT '0.00' COMMENT '存货单价（导入存货价格默认0值）',
  `price_range` decimal(20,2) DEFAULT '0.00' COMMENT '价格区间 0 不开启 也可由会计设置为 100% 150% 200%',
  `descs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_goods` (`tax_id`,`owner_year`,`goods_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8938 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='辅助存货表';

-- ----------------------------
-- Table structure for zjks_auxiliary_origbill
-- ----------------------------
DROP TABLE IF EXISTS `zjks_auxiliary_origbill`;
CREATE TABLE `zjks_auxiliary_origbill` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '税号',
  `owner_year` int NOT NULL COMMENT '所属年份',
  `auxiliary_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '辅助核算类型',
  `auxiliary_object_id` int DEFAULT NULL COMMENT '辅助核算对象id',
  `subject_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '科目编码',
  `drcr` int DEFAULT NULL COMMENT '借贷方向',
  `origm` decimal(24,2) DEFAULT '0.00' COMMENT '期初余额',
  `sumdr` decimal(24,2) DEFAULT '0.00' COMMENT '借累计余额',
  `sumcr` decimal(24,2) DEFAULT '0.00' COMMENT '贷累计余额',
  `shedm` decimal(24,2) DEFAULT '0.00' COMMENT '当前余额',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '外币币种',
  `vht` decimal(24,8) DEFAULT NULL COMMENT '汇率',
  `origc` decimal(24,2) DEFAULT '0.00' COMMENT '外币期初',
  `sumdrc` decimal(24,2) DEFAULT '0.00' COMMENT '外币借累计',
  `sumcrc` decimal(24,2) DEFAULT '0.00' COMMENT '外币贷累计',
  `shedc` decimal(24,2) DEFAULT '0.00' COMMENT '外币当前余额',
  `origq` decimal(24,6) DEFAULT '0.000000' COMMENT '期初数量',
  `sumin` decimal(24,6) DEFAULT '0.000000' COMMENT '收累计',
  `sumout` decimal(24,6) DEFAULT '0.000000' COMMENT '发累计',
  `shedq` decimal(24,6) DEFAULT '0.000000' COMMENT '当前数量',
  `sum_owner_month` int DEFAULT '0' COMMENT '借累计，贷累计所属月0为年初开始',
  `create_time` datetime DEFAULT (now()),
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `tax_id_owner_year_auxiliary_type_auxiliary_object_id_subject_key` (`tax_id`,`owner_year`,`auxiliary_type`,`auxiliary_object_id`,`subject_key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=97639 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='辅助核算期初余额';

-- ----------------------------
-- Table structure for zjks_auxiliary_person
-- ----------------------------
DROP TABLE IF EXISTS `zjks_auxiliary_person`;
CREATE TABLE `zjks_auxiliary_person` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录号',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属企业税号',
  `owner_year` int NOT NULL DEFAULT (0) COMMENT '所属年份',
  `person_code` int NOT NULL DEFAULT (0) COMMENT '员工代码',
  `person_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '员工名称',
  `identity_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证件号码',
  `dept_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所属部门',
  `descs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `tax_id_owner_year_person_code` (`tax_id`,`owner_year`,`person_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='辅助核算-人员';

-- ----------------------------
-- Table structure for zjks_auxiliary_project
-- ----------------------------
DROP TABLE IF EXISTS `zjks_auxiliary_project`;
CREATE TABLE `zjks_auxiliary_project` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录号',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属企业税号',
  `owner_year` int NOT NULL DEFAULT (0) COMMENT '所属年份',
  `project_code` int NOT NULL DEFAULT (0) COMMENT '项目代码',
  `project_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '项目名称',
  `descs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `tax_id_owner_year_project_code` (`tax_id`,`owner_year`,`project_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=405 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='辅助核算-项目';

-- ----------------------------
-- Table structure for zjks_banks
-- ----------------------------
DROP TABLE IF EXISTS `zjks_banks`;
CREATE TABLE `zjks_banks` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录号',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '税号',
  `owner_year` int NOT NULL DEFAULT (0) COMMENT '所属年份',
  `bank_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '银行帐号',
  `bank_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '银行名称',
  `currency` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '货币类型',
  `subject_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '科目编号',
  PRIMARY KEY (`id`,`tax_id`,`owner_year`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=390 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='银行信息表';

-- ----------------------------
-- Table structure for zjks_credence
-- ----------------------------
DROP TABLE IF EXISTS `zjks_credence`;
CREATE TABLE `zjks_credence` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属税号',
  `credence_time` date NOT NULL COMMENT '凭证发生时间',
  `owner_year` int NOT NULL COMMENT '凭证所属年',
  `owner_month` int NOT NULL COMMENT '凭证所属月',
  `subject_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '科目编码',
  `invoice_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发票号码',
  `invoice_date` datetime DEFAULT NULL COMMENT '开票时间',
  `year_no` int DEFAULT NULL COMMENT '凭证年流水号',
  `month_no` int DEFAULT NULL COMMENT '凭证月流水号',
  `debtor` decimal(24,2) DEFAULT '0.00' COMMENT '借方',
  `credit` decimal(24,2) DEFAULT '0.00' COMMENT '贷方',
  `type` int NOT NULL DEFAULT '0' COMMENT '凭证类型 911销项 912 进项',
  `recorder` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '录入员',
  `assessor` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核人',
  `chalker` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '记帐员',
  `check_flag` tinyint DEFAULT '0' COMMENT '是否校对',
  `chalk_flag` tinyint DEFAULT '0' COMMENT '是否已记帐',
  `descs` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '摘要',
  `sort_no` int NOT NULL COMMENT '同一张凭证排序编号',
  `auxiliary_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '辅助核算类型-与科目匹配',
  `auxiliary_object_id` int DEFAULT '0' COMMENT '辅助核算对象id',
  `currency` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'CNY' COMMENT '外币币种',
  `vht` decimal(22,8) DEFAULT '1.00000000' COMMENT '汇率',
  `debtor_fcmoney` decimal(24,2) DEFAULT '0.00' COMMENT '外币借方金额',
  `credit_fcmoney` decimal(22,2) DEFAULT '0.00' COMMENT '外币贷方金额',
  `price` decimal(22,6) DEFAULT NULL COMMENT '单价',
  `debtor_quantity` decimal(22,6) DEFAULT '0.000000' COMMENT '借方数量',
  `credit_quantity` decimal(22,6) DEFAULT '0.000000' COMMENT '贷方数量',
  `money` decimal(22,2) DEFAULT NULL COMMENT '金额',
  `ssflbm` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '税收分类编码',
  `goods` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '货物或商品名称',
  `attachment` int DEFAULT '0' COMMENT '凭证附件张数',
  `save_flag` int NOT NULL DEFAULT (0) COMMENT '0 系统创建用户未修改凭证\r\n1 用户已修改凭证（用户已修改重新生成凭证时不允许删除）',
  `create_time` datetime NOT NULL DEFAULT (now()) COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  PRIMARY KEY (`id`,`credence_time`) USING BTREE,
  KEY `taxId__index` (`tax_id`,`owner_year`,`owner_month`,`subject_key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=775158 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='凭证';

-- ----------------------------
-- Table structure for zjks_enterprise_info
-- ----------------------------
DROP TABLE IF EXISTS `zjks_enterprise_info`;
CREATE TABLE `zjks_enterprise_info` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录号',
  `account_id` varchar(6) CHARACTER SET utf8mb3 DEFAULT NULL COMMENT '帐套号',
  `tax_id` varchar(32) CHARACTER SET utf8mb3 NOT NULL COMMENT '企业税号',
  `name` varchar(128) CHARACTER SET utf8mb3 NOT NULL COMMENT '公司名称',
  `type` varchar(1) CHARACTER SET utf8mb3 NOT NULL DEFAULT '0' COMMENT '企业性质;个体户 有限责任公司 私营有限责任公司 集体企业',
  `category` varchar(1) CHARACTER SET utf8mb3 NOT NULL DEFAULT '0' COMMENT '企业类别;0工业企业 1商业企业 2其它',
  `scale` varchar(1) CHARACTER SET utf8mb3 NOT NULL DEFAULT '0' COMMENT '企业规模;0 小规模纳税人1 一般纳税人',
  `login_tax_id` varchar(32) CHARACTER SET utf8mb3 DEFAULT '' COMMENT '登录税号：默认为自己,也可能使用税务代理机构登录',
  `operate_id` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作员号',
  `password` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作密码',
  `state` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '0' COMMENT '代理状态;\r\n0 正常代理 \r\n1 停止代理\r\n2 暂停代理',
  `accountant_system` int(10) unsigned zerofill DEFAULT '**********' COMMENT '会计制度',
  `end_account` date DEFAULT NULL COMMENT '账期截止时间',
  `create_account` date DEFAULT (now()) COMMENT '建帐时间',
  `start_account` date DEFAULT (date_format((curdate() - interval 1 month),_utf8mb4'%Y-%m-01')) COMMENT '在快书系统中的开始做账时间',
  `person_tax_password` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '个税密码',
  `person_tax_type` int DEFAULT '0' COMMENT '个税类型：0 个税不报 1 工资薪金 2生产经营 (核定征收，查帐征收)',
  `tax_agent` varchar(32) CHARACTER SET utf8mb3 DEFAULT NULL COMMENT '办税员姓名',
  `jgkey` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '所属机构',
  `legal_person` varchar(32) CHARACTER SET utf8mb3 DEFAULT NULL COMMENT '法人',
  `address` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '公司地址',
  `phone` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '联系电话',
  `bank_name` varchar(90) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '开户银行',
  `bank_account` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '银行帐号',
  `business_scope` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '经营范围',
  `user_id` varchar(32) CHARACTER SET utf8mb3 NOT NULL COMMENT '用户ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `datadb` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '企业数据存储数据库名',
  `dbserver` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '数据库服务器',
  `vcode` varchar(32) CHARACTER SET utf8mb3 DEFAULT NULL COMMENT '小程序绑定企业时验证码',
  `accounting_date` date DEFAULT NULL COMMENT '当前帐期时间',
  `province` varchar(10) CHARACTER SET utf8mb3 DEFAULT NULL COMMENT '地区，省，直辖市',
  `province_code` int DEFAULT NULL COMMENT '地址代码，接口用',
  `store_manager` varchar(32) CHARACTER SET utf8mb3 DEFAULT NULL COMMENT '仓库管理员',
  `industry_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '所属行业（主营业务税目代码）',
  `initial_confirm` int DEFAULT '0' COMMENT '导入初始参数是否已确认（0未确认，1已确认）',
  `default_recorder` varchar(50) CHARACTER SET utf8mb3 DEFAULT NULL COMMENT '默认记帐员',
  `default_auditor` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '默认审核员',
  `create_time` datetime DEFAULT (now()) COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `deleted` int DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `tax_id` (`tax_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1700 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='企业信息表';

-- ----------------------------
-- Table structure for zjks_fixed_assets_card
-- ----------------------------
DROP TABLE IF EXISTS `zjks_fixed_assets_card`;
CREATE TABLE `zjks_fixed_assets_card` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录号',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '税号',
  `owner_year` int NOT NULL COMMENT '所属年',
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '卡片编号',
  `name` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '固定资产名',
  `easy_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '简称-用于固定资产类别',
  `make_time` date DEFAULT NULL COMMENT '制造日期',
  `spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规格型号',
  `increase` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '来源-数据字典-增减方式',
  `quantity` decimal(24,6) DEFAULT '0.000000' COMMENT '数量',
  `storage` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '存放地点',
  `usekey` int DEFAULT '2' COMMENT '使用状态-数据字典',
  `useyear` int DEFAULT '0' COMMENT '使用年限',
  `deprekey` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '减少方式',
  `use_start_date` date DEFAULT NULL COMMENT '开始使用日期',
  `origin` decimal(24,2) DEFAULT '0.00' COMMENT '原值',
  `remnant` decimal(24,2) DEFAULT '0.00' COMMENT '残值',
  `depresum` decimal(24,2) DEFAULT '0.00' COMMENT '初始累计折旧',
  `subject_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计提科目-费用折旧科目',
  `manufacturer` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '生产厂家',
  `units` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计量单位',
  `depreciation_rate_month` decimal(24,6) DEFAULT '0.000000' COMMENT '折旧率（月）',
  `depreciation_monery_month` decimal(24,2) DEFAULT '0.00' COMMENT '折旧额（月）',
  `residual_value_rate` decimal(24,6) DEFAULT '5.000000' COMMENT '残值率',
  `provision_method` int DEFAULT '0' COMMENT '计提方式',
  `isscrapped` int DEFAULT '0' COMMENT '停用标志-是否报废',
  `sum_month` int DEFAULT '0' COMMENT '已提月份',
  `credence_id` int DEFAULT NULL COMMENT '凭证id-该卡片通过凭证生成',
  `invoice_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '固定资产来源发票号码',
  `create_time` datetime DEFAULT (now()) COMMENT '创建时间',
  PRIMARY KEY (`id`,`tax_id`,`owner_year`,`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=320 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='固定资产卡片';

-- ----------------------------
-- Table structure for zjks_fixed_assets_jtzj
-- ----------------------------
DROP TABLE IF EXISTS `zjks_fixed_assets_jtzj`;
CREATE TABLE `zjks_fixed_assets_jtzj` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tax_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner_year` int NOT NULL COMMENT '年份',
  `owner_month` int NOT NULL COMMENT '月份',
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '固定资产编号',
  `depreciation_rate_month` decimal(24,6) DEFAULT NULL COMMENT '月折旧率',
  `depreciation_monery_month` decimal(24,2) DEFAULT NULL COMMENT '月折旧额',
  `origin` decimal(24,2) DEFAULT NULL COMMENT '原值',
  `date` datetime DEFAULT NULL COMMENT '折旧日期',
  `descs` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '摘要',
  `dept_id` int DEFAULT NULL COMMENT '辅助核算-部门id',
  `recorder` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员',
  `checker` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核员',
  `checkflag` int DEFAULT NULL COMMENT '审核标志',
  `create_time` datetime DEFAULT (now()),
  PRIMARY KEY (`id`,`tax_id`,`owner_year`,`owner_month`,`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=419 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='固定资产计提折旧（每月1行）';

-- ----------------------------
-- Table structure for zjks_standard_subject
-- ----------------------------
DROP TABLE IF EXISTS `zjks_standard_subject`;
CREATE TABLE `zjks_standard_subject` (
  `subject_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '科目编码',
  `easy_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '科目简称',
  `subject_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '科目名称',
  `full_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '全路径节点名',
  `level` int DEFAULT '0' COMMENT '科目级次',
  `islast` int DEFAULT '0' COMMENT '末级',
  `type` int DEFAULT '0' COMMENT '科目类别',
  `subject_direct` int DEFAULT '0' COMMENT '科目方向',
  `currency` int DEFAULT '0' COMMENT '外币币种',
  `auxiliary_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '辅助类型:往来单位、存货、项目、部门、个人',
  `isdept` int DEFAULT '0' COMMENT '部门核算',
  `isenterprise` int DEFAULT '0' COMMENT '往来单位核算',
  `isperson` int DEFAULT '0' COMMENT '个人核算',
  `isquantity` int DEFAULT '0' COMMENT '数量核算',
  `units` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '计量单位',
  `isfcurrency` int DEFAULT '0' COMMENT '外币核算',
  `isproject` int DEFAULT '0' COMMENT '项目核算',
  `flag` int DEFAULT '0',
  `iscash` int DEFAULT '0' COMMENT '现金科目',
  `isbank` int DEFAULT '0' COMMENT '银行科目',
  `remark` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '默认账册',
  `spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '科目模糊匹配关键字',
  `drcr` int DEFAULT NULL COMMENT '借贷',
  `origm` decimal(24,2) DEFAULT NULL COMMENT '期初余额',
  `sumdr` decimal(24,2) DEFAULT NULL COMMENT '借累计余额',
  `sumcr` decimal(24,2) DEFAULT NULL COMMENT '贷累计余额',
  `shedm` decimal(24,2) DEFAULT NULL COMMENT '当前余额',
  `origc` decimal(24,2) DEFAULT NULL COMMENT '外币期初余额',
  `sumdrc` decimal(24,2) DEFAULT NULL COMMENT '外币借累计',
  `sumcrc` decimal(24,2) DEFAULT NULL COMMENT '外币贷累计',
  `shedc` decimal(24,2) DEFAULT NULL COMMENT '外币当前余额',
  `origq` decimal(24,6) DEFAULT NULL COMMENT '期初数量',
  `sumin` decimal(24,6) DEFAULT NULL COMMENT '收累计',
  `sumout` decimal(24,6) DEFAULT NULL COMMENT '发累计',
  `shedq` decimal(24,6) DEFAULT NULL COMMENT '当前数量',
  `sort_id` int DEFAULT NULL COMMENT '科目排序显示',
  `current_balance` decimal(24,2) DEFAULT NULL COMMENT '当前余额',
  `current_quantity` decimal(24,6) DEFAULT NULL COMMENT '当前数量',
  `current_f_balance` decimal(24,2) DEFAULT NULL COMMENT '当前外币余额',
  PRIMARY KEY (`subject_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标准科目模板（辅助模式）';

-- ----------------------------
-- Table structure for zjks_subject
-- ----------------------------
DROP TABLE IF EXISTS `zjks_subject`;
CREATE TABLE `zjks_subject` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tax_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '税号',
  `owner_year` int NOT NULL COMMENT '年属年份',
  `subject_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '科目编码',
  `easy_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '科目简称',
  `subject_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '科目名称',
  `full_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '全路径节点名',
  `level` int NOT NULL DEFAULT '0' COMMENT '科目级次',
  `islast` int NOT NULL DEFAULT '0' COMMENT '末级',
  `type` int DEFAULT '0' COMMENT '科目类别',
  `subject_direct` int NOT NULL DEFAULT '0' COMMENT '科目方向',
  `auxiliary_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '辅助核算类型',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '外币币种',
  `units` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数量核算 计量单位',
  `flag` int DEFAULT '0' COMMENT '0 为用户科目 1系统科目',
  `iscash` int DEFAULT '0' COMMENT '现金科目',
  `isbank` int DEFAULT '0' COMMENT '银行科目',
  `remark` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '默认账册',
  `drcr` int DEFAULT NULL COMMENT '借贷',
  `origm` decimal(24,2) DEFAULT NULL COMMENT '期初余额',
  `sumdr` decimal(24,2) DEFAULT NULL COMMENT '借累计余额',
  `sumcr` decimal(24,2) DEFAULT NULL COMMENT '贷累计余额',
  `shedm` decimal(24,2) DEFAULT NULL COMMENT '当前余额',
  `origc` decimal(24,2) DEFAULT NULL COMMENT '外币期初余额',
  `sumdrc` decimal(24,2) DEFAULT NULL COMMENT '外币借累计',
  `sumcrc` decimal(24,2) DEFAULT NULL COMMENT '外币贷累计',
  `shedc` decimal(24,2) DEFAULT NULL COMMENT '外币当前余额',
  `spec` varchar(90) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '科目全程(1级+N级名称)',
  `origq` decimal(24,6) DEFAULT NULL COMMENT '期初数量',
  `sumin` decimal(24,6) DEFAULT NULL COMMENT '收累计',
  `sumout` decimal(24,6) DEFAULT NULL COMMENT '发累计',
  `shedq` decimal(24,6) DEFAULT NULL COMMENT '当前数量',
  `sort_id` int DEFAULT NULL COMMENT '科目排序显示',
  `current_balance` decimal(24,2) DEFAULT NULL COMMENT '当前余额',
  `current_quantity` decimal(24,6) DEFAULT NULL COMMENT '当前数量',
  `current_f_balance` decimal(24,2) DEFAULT NULL COMMENT '当前外币余额',
  `sum_owner_month` int DEFAULT '0',
  `create_time` datetime DEFAULT (now()) COMMENT '创建时间',
  `deleted` int DEFAULT NULL COMMENT '是否删除 0-未删除 1-已删除',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `tax_id_owner_year_subject_key` (`tax_id`,`owner_year`,`subject_key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=334945 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='企业科目模板';

SET FOREIGN_KEY_CHECKS = 1;
