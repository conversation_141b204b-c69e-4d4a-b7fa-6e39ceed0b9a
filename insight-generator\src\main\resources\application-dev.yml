---
logging:
  file:
    path: "/tmp/logs"
    name: "${spring.application.name}"
  level:
    root: "INFO"
    com.qbook: "DEBUG"
    org.springframework: "INFO"
spring:
  datasource:
    url: "**************************************************************************"
    username: "zjks"
    password: "CNzjks*^%&!"
    driver-class-name: "com.mysql.cj.jdbc.Driver"
    hikari:
      maximum-pool-size: 10
      minimum-idle: 3
      connection-timeout: 10000
      idle-timeout: 10000
  redis:
    host: "localhost"
    port: 6379
    password: null
    database: 0
    connect-timeout: "10s"
    timeout: "10s"
    client-name: "${spring.application.name}"
    jedis:
      pool:
        enabled: true
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: "5000ms"
