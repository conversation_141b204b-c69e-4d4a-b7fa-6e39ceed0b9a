package com.qbook.insight.analyzer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qbook.insight.analyzer.entity.AnalysisTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.repository.query.Param;

/**
 * 模板配置接口类
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-05 15:54
 */
@Mapper
public interface AnalysisTemplateMapper extends BaseMapper<AnalysisTemplate> {

  /**
   * 根据模板名称查询模板
   *
   * @param name 模板名称
   * @return 模板
   */
  @Select("select name from analysis_template where name = #{name}")
  AnalysisTemplate selectByName(@Param("name") String name);
}
