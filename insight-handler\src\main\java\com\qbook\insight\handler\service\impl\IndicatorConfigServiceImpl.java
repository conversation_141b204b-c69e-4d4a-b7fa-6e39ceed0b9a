package com.qbook.insight.handler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qbook.insight.common.entity.IndicatorConfig;
import com.qbook.insight.common.exception.BizException;
import com.qbook.insight.common.mapper.IndicatorConfigMapper;
import com.qbook.insight.common.util.IndicatorCalculator;
import com.qbook.insight.handler.service.IndicatorConfigService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 指标计算配置接口实现
 *
 * <AUTHOR>
 */
@Service
public class IndicatorConfigServiceImpl implements IndicatorConfigService {

  @Resource private IndicatorConfigMapper indicatorConfigMapper;
  @Resource private IndicatorCalculator indicatorCalculator;

  @Override
  public int add(IndicatorConfig indicatorConfig) {
    return indicatorConfigMapper.insert(indicatorConfig);
  }

  @Override
  public int delete(long id) {
    return indicatorConfigMapper.deleteById(id);
  }

  @Override
  public int update(long id, IndicatorConfig indicatorConfig) {
    indicatorConfig.setId(id);
    return indicatorConfigMapper.updateById(indicatorConfig);
  }

  @Override
  public List<IndicatorConfig> list(Integer status, String indicatorName, String indicatorCode) {
    LambdaQueryWrapper<IndicatorConfig> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(status != null, IndicatorConfig::getStatus, status);
    wrapper.eq(indicatorCode != null, IndicatorConfig::getIndicatorCode, indicatorCode);
    wrapper.eq(indicatorName != null, IndicatorConfig::getIndicatorName, indicatorName);
    wrapper.orderByDesc(IndicatorConfig::getCreatedAt);
    return indicatorConfigMapper.selectList(wrapper);
  }

  @Override
  public Object test(
      IndicatorConfig indicatorConfig, String taxId, Map<String, Object> queryParams) {

    if (indicatorConfig == null
        || indicatorConfig.getIndicatorCode() == null
        || indicatorConfig.getExecuteSql() == null
        || indicatorConfig.getReturnType() == null) {
      throw new BizException("缺少指标配置必要参数");
    }
    if (queryParams == null) {
      queryParams = new HashMap<>();
    }
    queryParams.put("tax_id", taxId);
    return indicatorCalculator.calculate(indicatorConfig, queryParams);
  }
}
