package com.qbook.insight.common.config;

import com.qbook.insight.common.entity.User;
import com.qbook.insight.common.util.JwtUtil;
import com.qbook.insight.common.util.SecurityUtil;
import java.io.IOException;
import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.NonNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * Token 过滤器
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "component.jwt", havingValue = "true")
public class AuthTokenFilter extends OncePerRequestFilter {

  @SuppressWarnings("all")
  private static final String NewAccessToken = "New-Access-Token";

  @Resource private JwtUtil jwtUtil;

  @Override
  protected void doFilterInternal(
      @NonNull HttpServletRequest request,
      @NonNull HttpServletResponse response,
      @NonNull FilterChain chain)
      throws ServletException, IOException {
    String token = jwtUtil.getToken(request);
    if (token != null) {
      User user = jwtUtil.getUser(token);
      if (user != null) {
        if (user.getAccessToken() != null) {
          response.setHeader(NewAccessToken, user.getAccessToken());
          user.setAccessToken(null);
        }

        SecurityUtil.setCurrentUser(user);
      } else {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        return;
      }
    }
    chain.doFilter(request, response);
  }
}
