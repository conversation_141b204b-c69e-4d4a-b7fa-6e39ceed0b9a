package com.qbook.insight.handler.controller;

import com.qbook.insight.common.domain.R;
import com.qbook.insight.common.vo.CodeGenParam;
import com.qbook.insight.handler.service.GenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 代码生成相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "Code Generate", description = "代码生成")
@RestController
@RequestMapping("/gen")
@PreAuthorize("hasRole(@role.DEVELOPER)")
public class GenController {

  @Resource private GenService genService;

  @ApiOperation(value = "获取表名列表")
  @GetMapping("/table/list")
  public R getTableList() {
    return R.ok(genService.getTables());
  }

  @ApiOperation(value = "获取指定表的字段")
  @GetMapping("/table/columns")
  public R getColumnsByTableName(@ApiParam("表名") @RequestParam String tableName) {
    return R.ok(genService.getColumnsByTableName(tableName));
  }

  @ApiOperation(value = "预览代码")
  @GetMapping("/code/preview")
  public R codePreview(CodeGenParam param) {
    return R.ok(genService.codePreview(param));
  }
}
