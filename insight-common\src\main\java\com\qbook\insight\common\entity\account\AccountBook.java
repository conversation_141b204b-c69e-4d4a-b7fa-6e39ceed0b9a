package com.qbook.insight.common.entity.account;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 账簿
 *
 * <AUTHOR>
 */
@Data
public class AccountBook {

  @ApiModelProperty("电子账簿")
  private AccountLedger accountLedger;

  @ApiModelProperty("会计科目")
  private List<AccountingSubject> accountingSubjects;

  @ApiModelProperty("部门")
  private List<DepartmentInfo> departmentInfos;

  @ApiModelProperty("人员")
  private List<EmployeeInfo> employeeInfos;

  @ApiModelProperty("往来单位")
  private List<BusinessUnits> businessUnits;

  @ApiModelProperty("项目")
  private List<ProjectInfo> projectInfos;

  @ApiModelProperty("存货")
  private List<InventoryInfo> inventoryInfos;

  @ApiModelProperty("科目余额及发生额")
  private List<SubjectBalance> subjectBalances;

  @ApiModelProperty("记账凭证")
  private List<AccountingVoucher> accountingVouchers;
}
