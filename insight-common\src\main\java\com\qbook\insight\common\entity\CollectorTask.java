package com.qbook.insight.common.entity;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据采集任务实体类实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CollectorTask extends BaseEntity {

  @ApiModelProperty("任务ID")
  private String taskId;

  @ApiModelProperty("税号")
  private String taxId;

  @ApiModelProperty("采集类型")
  private Integer collectorType;

  @ApiModelProperty("数据开始日期")
  private Date dataStart;

  @ApiModelProperty("数据结束日期")
  private Date dataEnd;

  @ApiModelProperty("任务状态（0:初始 1:开始 2:成功 3:失败）")
  private Integer status;

  @ApiModelProperty("错误信息")
  private String message;

  @ApiModelProperty("任务开始时间")
  private Date taskStart;

  @ApiModelProperty("任务结束时间")
  private Date taskFinish;
}
