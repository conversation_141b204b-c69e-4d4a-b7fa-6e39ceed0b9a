package com.qbook.insight.handler.vo;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 支付订单VO
 *
 * <AUTHOR>
 */
@Data
public class MemberOrderVO {

  @ApiModelProperty("套餐ID")
  private Integer planId;

  @ApiModelProperty("订单号")
  private String orderId;

  @ApiModelProperty("订单金额（元）")
  private BigDecimal amount;

  @ApiModelProperty("订单状态：pending, paid, failed, cancelled, refunded")
  private String status;

  @ApiModelProperty("支付方式：wechat_pay, alipay")
  private String paymentMethod;

  @ApiModelProperty("支付链接")
  private String paymentUrl;

  @ApiModelProperty("创建时间")
  private Date createdAt;

  @ApiModelProperty("支付时间")
  private Date paidAt;

  @ApiModelProperty("套餐名称")
  private String planName;
}
