package com.qbook.insight.collector.service;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.qbook.insight.common.constant.MsgTopic;
import com.qbook.insight.common.constant.ReportStage;
import com.qbook.insight.common.core.Pipe;
import com.qbook.insight.common.entity.Report;
import com.qbook.insight.common.mapper.ReportMapper;
import com.qbook.insight.common.util.RedisUtil;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.stereotype.Service;

/**
 * 任务监听器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TaskListener {

  @Resource private RedisUtil redisUtil;
  @Resource private ReportMapper reportMapper;
  @Resource private CollectDataService collectDataService;

  public void start() {
    Thread thread = new Thread(new TaskListenerThread());
    thread.setName("Thread-TaskListener");
    thread.start();
  }

  private class TaskListenerThread implements Runnable {
    @Override
    public void run() {
      String data;
      while (!Pipe.exit) {
        try {
          data = (String) redisUtil.blpop(MsgTopic.TASK_COLLECTOR, 10, TimeUnit.SECONDS);
          if (data == null) {
            continue;
          }
          log.info("[REPORT_COLLECTOR] {}", data);
          Report report = JSONUtil.toBean(data, Report.class);
          // 采集数据
          collectDataService.collectTask(report);

          log.info("报告数据采集完成");
          LambdaUpdateWrapper<Report> updateWrapper = new LambdaUpdateWrapper<>();
          updateWrapper
              .set(Report::getStage, ReportStage.COLLECTED)
              .eq(Report::getId, report.getId());
          reportMapper.update(null, updateWrapper);

          redisUtil.rpush(MsgTopic.TASK_ANALYZER, JSONUtil.toJsonStr(report));
        } catch (RedisConnectionFailureException ignored) {
        } catch (Exception e) {
          log.error(e.getMessage());
        }
      }
    }
  }
}
