package com.qbook.insight.handler.config;

import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.List;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * Api文档配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableSwagger2
@ConfigurationProperties(prefix = "apidoc")
@Data
public class ApiDocConfig {

  @Value("${apidoc.enable:false}")
  private boolean enable;

  @Value("${apidoc.version:}")
  private String version;

  // 令牌自定义标识
  @Value("${jwt.header:Authorization}")
  private String header;

  // 允许匿名访问的地址列表
  private List<String> permitAllUris;

  private static final String title = "insight-handler API";

  @Bean
  public Docket docket() {
    return new Docket(DocumentationType.SWAGGER_2)
        .enable(enable)
        .apiInfo(apiInfo())
        .select()
        .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
        .paths(PathSelectors.any())
        .build()
        .securitySchemes(securitySchemes())
        .securityContexts(securityContexts());
  }

  private ApiInfo apiInfo() {
    return new ApiInfoBuilder().title(title).description(null).version(version).build();
  }

  /** 定义 Header 名称 */
  private List<SecurityScheme> securitySchemes() {
    return Collections.singletonList(new ApiKey(header, header, "header"));
  }

  /** 安全上下文 */
  private List<SecurityContext> securityContexts() {
    SecurityContext context =
        SecurityContext.builder()
            .securityReferences(defaultAuth())
            .forPaths(PathSelectors.any())
            .build();
    return Collections.singletonList(context);
  }

  private List<SecurityReference> defaultAuth() {
    AuthorizationScope scope = new AuthorizationScope("global", "Global access");
    return Collections.singletonList(
        new SecurityReference(header, new AuthorizationScope[] {scope}));
  }
}
