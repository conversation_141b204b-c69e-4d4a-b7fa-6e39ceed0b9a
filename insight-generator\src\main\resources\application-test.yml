---
logging:
  file:
    path: "/home/<USER>/insight/logs"
    name: "${spring.application.name}"
  level:
    root: "INFO"
    com.qbook: "DEBUG"
    org.springframework: "INFO"
spring:
  datasource:
    url: "**************************************************************************"
    username: "zjks"
    password: "CNzjks*^%&!"
    driver-class-name: "com.mysql.cj.jdbc.Driver"
    hikari:
      maximum-pool-size: 10
      minimum-idle: 3
      connection-timeout: 10000
      idle-timeout: 10000
  redis:
    host: "************"
    port: 6379
    password: "Zjks@123456"
    database: 10
    connect-timeout: "10s"
    timeout: "10s"
    client-name: "${spring.application.name}"
    jedis:
      pool:
        enabled: true
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: "5000ms"
