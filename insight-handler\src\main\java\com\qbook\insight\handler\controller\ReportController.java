package com.qbook.insight.handler.controller;

import com.qbook.insight.common.annotation.EventLog;
import com.qbook.insight.common.domain.R;
import com.qbook.insight.common.entity.Report;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.service.ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 报告相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "Report Operate", description = "报告操作")
@RestController
@RequestMapping("/report")
public class ReportController {

  @Resource private ReportService reportService;

  @ApiOperation(value = "添加报告")
  @PostMapping("/add")
  @EventLog(name = "添加报告")
  public R add(@RequestBody Report report) {
    return R.affect(reportService.add(report));
  }

  @ApiOperation(value = "删除报告")
  @DeleteMapping("/{id}")
  @EventLog(name = "删除报告")
  public R delete(@PathVariable long id) {
    return R.affect(reportService.delete(id));
  }

  @ApiOperation(value = "修改报告审核状态")
  @PutMapping("/{id}")
  @PreAuthorize("hasRole(@role.AUDITOR)")
  public R update(@PathVariable long id, @RequestBody Report report) {
    return R.ok(reportService.update(id, report));
  }

  @ApiOperation(value = "获取报告列表")
  @GetMapping("/list")
  public R list(@ApiParam("查询字段") @RequestParam(required = false) String searchField) {
    return R.ok(reportService.list());
  }

  @ApiOperation(value = "获取报告列表(分页)")
  @GetMapping("/page")
  public R page(
      PageParam pageParam,
      @ApiParam("用户昵称") @RequestParam(required = false) String realName,
      @ApiParam("公司名称") @RequestParam(required = false) String corpName,
      @ApiParam("税号") @RequestParam(required = false) String taxId) {
    return R.ok(reportService.page(pageParam, realName, corpName, taxId));
  }
}
