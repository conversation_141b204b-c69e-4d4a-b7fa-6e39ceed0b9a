package com.qbook.insight.handler.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.entity.Member;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 会员信息接口
 *
 * <AUTHOR>
 */
@Service
public interface MemberService {

  /** 获取当前用户的会员信息 */
  Member info();

  /** 修改用户会员信息 */
  int update(long id, Member member);

  /** 获取用户会员信息详情 */
  Member getById(long id);

  /** 获取用户会员信息列表 */
  List<Member> list(Integer isActive);

  /** 获取用户会员信息列表(分页) */
  IPage<Member> page(PageParam pageParam, Integer isActive);

  /**
   * 处理支付成功后的会员权限更新
   *
   * @param userId 用户ID
   * @param planId 套餐ID
   */
  void handlePaymentSuccess(Long userId, Integer planId);

  /** 校验会员是否有效并扣减报告次数 */
  void consumeReport();

  /**
   * 获取当前用户的有效会员信息
   *
   * @return 会员信息，如果不是会员则返回null
   */
  Member getCurrentMember();
}
