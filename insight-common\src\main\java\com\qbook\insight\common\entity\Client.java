package com.qbook.insight.common.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Client extends BaseEntity {

  @ApiModelProperty("所属用户")
  private Long userId;

  @ApiModelProperty("公司ID")
  private Long corpId;

  @ApiModelProperty("登录税号")
  private String loginTaxId;

  @ApiModelProperty("登录模式(电子税务局)")
  private Integer loginMode;

  @ApiModelProperty("登录手机号(电子税务局)")
  private String loginPhone;

  @ApiModelProperty("登录密码(电子税务局)")
  private String loginPassword;

  @ApiModelProperty("标签(多个标签之间使用','分隔)")
  private String tags;
}
