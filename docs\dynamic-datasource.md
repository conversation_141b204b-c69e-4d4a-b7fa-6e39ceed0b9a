# 动态数据源

本项目采用 MyBatis-Plus 的一个子项目 [dynamic-datasource](https://github.com/baomidou/dynamic-datasource) 实现的数据源的动态切换。

## 1. 前提条件

假设系统中有2个库，分别为 `insight` 和 `insight_data`。如果某个模块中同时用到这2个库，我们可以这样定义数据库配置：

```yaml
spring:
  datasource:
    dynamic:
      primary: "sys"  # 默认的数据源为 sys（当没有指定数据源时，）
      strict: false
      grace-destroy: false
      datasource:
        sys:  # 数据源名称为 sys，对应 insight 库
          url: "***********************************?..."
        data: # 数据源名称为 data，对应 insight_data 库
          url: "****************************************?..."
```

假设有3张表，其中 `ta` 表对应的默认数据源，`tb`、`tc` 表对应`data`数据源。对应表的Mapper定义如下：

```java
@Mapper
public interface TaMapper extends BaseMapper<Ta> {}

@Mapper
@DS("data")
public interface TbMapper extends BaseMapper<Tb> {}

@Mapper
@DS("data")
public interface TcMapper extends BaseMapper<Tc> {}
```

使用场景举例如下：

## 2. 使用场景

### 2.1 多库、无事务

```java
  public void case1() {
    TaMapper.selectById(1);
    TbMapper.selectById(2);
  }
```

### 2.2 单库、带事务

```java
  @Transactional  // 由于没有涉及到库的切换，所以可以用 @Transactional，也可以用 @DSTransactional
  public void case2() {
  Tb tb = new Tb("b");
  TbMapper.insert(tb);

  Tc tc = new Tc("c");
  TcMapper.insert(tc);
}
```

### 2.3 多库、带事务

```java
  @DSTransactional  // 由于涉及到库的切换，添加 @Transactional 注解后，会复用同一个连接，因此会报找不到第2个表的错误
  public void case2() {
  Ta ta = new Ta("a");
  TaMapper.insert(ta);

  Tb tb = new Tb("b");
  TbMapper.insert(tb);
}
```
