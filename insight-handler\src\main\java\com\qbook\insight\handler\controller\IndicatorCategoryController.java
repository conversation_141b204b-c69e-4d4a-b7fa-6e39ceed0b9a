package com.qbook.insight.handler.controller;

import com.qbook.insight.common.domain.R;
import com.qbook.insight.common.entity.IndicatorCategory;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.service.IndicatorCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 指标分类相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "IndicatorCategory Operate", description = "指标分类操作")
@RestController
@RequestMapping("/indicator_category")
public class IndicatorCategoryController {

  @Resource private IndicatorCategoryService indicatorCategoryService;

  @ApiOperation(value = "添加指标分类")
  @PostMapping("/add")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R add(@RequestBody IndicatorCategory indicatorCategory) {
    return R.affect(indicatorCategoryService.add(indicatorCategory));
  }

  @ApiOperation(value = "删除指标分类")
  @DeleteMapping("/{id}")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R delete(@PathVariable long id) {
    return R.affect(indicatorCategoryService.delete(id));
  }

  @ApiOperation(value = "修改指标分类")
  @PutMapping("/{id}")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R update(@PathVariable long id, @RequestBody IndicatorCategory indicatorCategory) {
    return R.affect(indicatorCategoryService.update(id, indicatorCategory));
  }

  @ApiOperation(value = "获取指标分类列表")
  @GetMapping("/list")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R list(
      @ApiParam("是否启用(0-否，1-是)") @RequestParam(required = false) Integer status,
      @ApiParam("指标分类级别") @RequestParam(required = false) Integer level,
      @ApiParam("指标分类名称") @RequestParam(required = false) String name) {
    return R.ok(indicatorCategoryService.list(level, status, name));
  }

  @ApiOperation(value = "获取分类下的指标配置")
  @GetMapping("/{id}/indicators")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R getIndicatorsByCategory(@PathVariable long id) {
    return R.ok(indicatorCategoryService.getIndicatorsByCategory(id));
  }

  @ApiOperation(value = "获取分类及其下的指标配置")
  @GetMapping("/page")
  @PreAuthorize("hasRole(@role.ADMIN)")
  public R getCategoriesWithIndicators(
      PageParam pageParam,
      @ApiParam("是否启用(0-否，1-是)") @RequestParam(required = false) Integer status,
      @ApiParam("指标分类级别") @RequestParam(required = false) Integer level,
      @ApiParam("指标分类名称") @RequestParam(required = false) String name) {
    return R.ok(
        indicatorCategoryService.getCategoriesWithIndicators(pageParam, level, status, name));
  }
}
