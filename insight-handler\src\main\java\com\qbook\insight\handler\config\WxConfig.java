package com.qbook.insight.handler.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信配置
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "wx")
@Data
public class WxConfig {

  // AppId
  private String appId;

  // AppSecret
  private String appSecret;

  // Token
  private String token;

  // AES key
  private String aesKey;

  // 报文模式
  private String encryptionMode;

  // 扫码登录事件Redis缓存键前缀
  private String qrRedisPrefix;

  // 扫码登录事件Redis缓存过期时间(秒)
  private Integer qrRedisExpire;

  // 获取 access_token url
  private String getAccessTokenUrl;

  // 获取创建二维码ticket url
  private String getTicketUrl;

  // 获取二维码 url
  private String getQrCodeUrl;

  // 根据openid获取用户信息 url
  private String getUserinfoUrl;

  // 获取Oauth认证
  private String getOauth2Url;

  // 获取openid
  private String getOpenidUrl;
}
