package com.qbook.insight.handler.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信支付配置
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-08 下午4:59
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wx.pay")
public class WxPayConfig {
  // 微信商户号
  private String mchId;
  // 商户API证书
  private String mchCert;
  // 商户API证书序列号
  private String mchSerialNo;
  // 商户API证书私钥路径
  private String mchPriKeyPath;

  /** 后期将平台证书更换成微信支付公钥 */
  // 微信支付平台证书序列号
  private String certSerialNo;
  // 微信支付平台证书路径
  private String certPath;

  /** 后期使用这个微信支付公钥 */
  // 微信支付公钥Id
  private String publicKeyId;
  // 微信支付公钥地址
  private String publicKeyPath;

  // 支付通知-回调地址
  private String notifyUrl;
  // ApiV3-回调解密密钥
  private String apiV3Key;

  // 微信jsAPI预支付Url
  private String jsapiPreOrderUrl;
}
