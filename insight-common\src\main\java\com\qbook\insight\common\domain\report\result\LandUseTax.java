package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.ResultLevel1;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 城镇土地使用税
 *
 * <AUTHOR>
 */
@Data
public class LandUseTax {

  @ApiModelProperty("序号")
  private Integer index;

  @ApiModelProperty("年份")
  private Integer year;

  @ApiModelProperty("误用土地等级税额标准少计算交纳税款(元)")
  @ResultLevel1
  private BigDecimal incorrectLandGradeRate;

  @ApiModelProperty("误用土地使用面积少计算交纳税款(元)")
  private BigDecimal incorrectLandArea;
}
