package com.qbook.insight.common.util;

import cn.hutool.core.util.StrUtil;
import com.thoughtworks.xstream.XStream;

/**
 * XML工具类
 *
 * <AUTHOR>
 */
public class XmlUtils {
  /**
   * 将 Java Bean 转化为 XML
   *
   * @param bean {@link Object}
   * @param cls 传入对象的字节码
   * @return XML 字符串
   */
  public static <T> String beanToXml(Object bean, Class<T> cls) {
    XStream stream = new XStream();
    stream.processAnnotations(cls);
    return stream.toXML(bean);
  }

  /**
   * 将 Java Bean 转化为 XML
   *
   * @param bean {@link Object}
   * @return XML 字符串
   */
  public static String beanToXml(Object bean) {
    return beanToXml(bean, bean.getClass());
  }

  /**
   * 将 xml 转化为 Bean
   *
   * @param xml xml
   * @param cls bean 的类型
   * @param alias 对应 bean 的别名
   * @param <T> 泛型
   * @return T
   */
  @SuppressWarnings("unchecked")
  public static <T> T xmlToBean(String xml, Class<T> cls, String alias) {
    if (StrUtil.hasBlank(xml, alias)) {
      throw new IllegalArgumentException("xml to Bean 参数错误");
    }
    XStream stream = new XStream();
    stream.alias(alias, cls);
    return (T) stream.fromXML(xml);
  }

  /**
   * 将 xml 转化为 Bean，bean 默认别名为 xml
   *
   * @param xml xml
   * @param cls bean 的类型
   * @param <T> 泛型
   * @return T
   */
  public static <T> T xmlToBean(String xml, Class<T> cls) {
    return xmlToBean(xml, cls, "xml");
  }
}
