package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *发票数据
 *
 * <AUTHOR>
 */
@Data
public class Invoice {
    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("税号")
    private String taxId;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("发票号码")
    private String invoiceNum;

    @ApiModelProperty("开票日期")
    private LocalDate invoiceDate;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("税额")
    private BigDecimal tax;

    @ApiModelProperty("含税金额")
    private BigDecimal taxIncludedAmount;
}
