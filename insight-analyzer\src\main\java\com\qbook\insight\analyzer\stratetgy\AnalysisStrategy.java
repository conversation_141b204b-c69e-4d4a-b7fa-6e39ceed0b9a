package com.qbook.insight.analyzer.stratetgy;

import com.qbook.insight.analyzer.domain.AnalysisData;

/**
 * 分析策略接口类
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-05-29 17:18
 */
public interface AnalysisStrategy {
  /** 分析策略 */
  void analyze(String data);

  /**
   * 获取分析策略类型
   *
   * @return 分析策略类型
   */
  String getStrategyType();

  /**
   * 保存分析结果到数据库
   *
   * @param analyzeData 数据
   */
  void saveResult(AnalysisData analyzeData);

  /**
   * 加载策略所需数据
   *
   * @param taskId 任务ID
   * @return 数据对象
   */
  AnalysisData loadData(String taskId);
}
