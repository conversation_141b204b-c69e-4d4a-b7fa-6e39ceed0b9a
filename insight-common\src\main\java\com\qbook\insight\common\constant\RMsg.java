package com.qbook.insight.common.constant;

/**
 * REST API 错误信息
 *
 * <AUTHOR>
 */
public class RMsg {

  public static final String SUCCESS = "OK";
  public static final String ERR_NO_USERNAME_PASSWORD = "用户名或密码不能为空";
  public static final String ERR_NO_USER = "用户不存在";
  public static final String ERR_PASSWORD = "密码错误";
  public static final String ERR_PARAM = "参数错误";

  // 扫码登录异常信息
  public static final String ERR_QR_CODE = "二维码生成失败";
  public static final String ERR_NO_OPENID = "openId不能为空";
  public static final String ERR_GET_USER_INFO = "获取微信用户信息失败";
  public static final String ERR_GET_ACCESS_TOKEN = "获取微信access_token失败";
  public static final String ERR_GET_TICKET = "获取微信ticket失败";

  // API获取数据异常信息
  public static final String ERR_GET_API_DATA = "API数据异常";
}
