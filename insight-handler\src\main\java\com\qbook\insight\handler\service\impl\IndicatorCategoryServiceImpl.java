package com.qbook.insight.handler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qbook.insight.common.entity.IndicatorCategory;
import com.qbook.insight.common.entity.IndicatorConfig;
import com.qbook.insight.common.mapper.IndicatorCategoryMapper;
import com.qbook.insight.common.mapper.IndicatorConfigMapper;
import com.qbook.insight.common.vo.IndicatorVo;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.service.IndicatorCategoryService;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 指标分类操作
 *
 * <AUTHOR>
 */
@Service
public class IndicatorCategoryServiceImpl implements IndicatorCategoryService {

  @Resource private IndicatorCategoryMapper indicatorCategoryMapper;
  @Resource private IndicatorConfigMapper indicatorConfigMapper;

  public int add(IndicatorCategory indicatorCategory) {
    return indicatorCategoryMapper.insert(indicatorCategory);
  }

  public int delete(long id) {
    return indicatorCategoryMapper.deleteById(id);
  }

  public int update(long id, IndicatorCategory indicatorCategory) {
    indicatorCategory.setId(id);
    return indicatorCategoryMapper.updateById(indicatorCategory);
  }

  public List<IndicatorCategory> list(Integer level, Integer status, String name) {
    LambdaQueryWrapper<IndicatorCategory> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(status != null, IndicatorCategory::getStatus, status);
    wrapper.eq(level != null, IndicatorCategory::getLevel, level);
    wrapper.like(name != null && !name.trim().isEmpty(), IndicatorCategory::getName, name);
    wrapper.orderByAsc(IndicatorCategory::getLevel);
    wrapper.orderByDesc(IndicatorCategory::getCreatedAt);
    return indicatorCategoryMapper.selectList(wrapper);
  }

  @Override
  public List<IndicatorConfig> getIndicatorsByCategory(long categoryId) {
    LambdaQueryWrapper<IndicatorConfig> wrapper = new LambdaQueryWrapper<>();
    wrapper.and(
        w ->
            w.eq(IndicatorConfig::getClass1, categoryId)
                .or()
                .eq(IndicatorConfig::getClass2, categoryId));
    wrapper.orderByDesc(IndicatorConfig::getCreatedAt);
    return indicatorConfigMapper.selectList(wrapper);
  }

  @Override
  public IPage<IndicatorVo> getCategoriesWithIndicators(
      PageParam pageParam, Integer level, Integer status, String name) {
    // 创建分页对象
    IPage<IndicatorCategory> page = new Page<>(pageParam.getPage(), pageParam.getPageSize());

    // 构建查询条件
    LambdaQueryWrapper<IndicatorCategory> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(status != null, IndicatorCategory::getStatus, status);
    wrapper.eq(level != null, IndicatorCategory::getLevel, level);
    wrapper.like(name != null && !name.trim().isEmpty(), IndicatorCategory::getName, name);
    wrapper.orderByAsc(IndicatorCategory::getLevel);
    wrapper.orderByDesc(IndicatorCategory::getCreatedAt);

    // 执行分页查询
    IPage<IndicatorCategory> categoryPage = indicatorCategoryMapper.selectPage(page, wrapper);

    List<IndicatorVo> voList =
        categoryPage.getRecords().stream()
            .map(
                category -> {
                  IndicatorVo vo = new IndicatorVo();
                  BeanUtils.copyProperties(category, vo);
                  vo.setIndicatorConfig(getIndicatorsByCategory(category.getId()));
                  return vo;
                })
            .collect(Collectors.toList());

    // 创建返回的分页对象
    IPage<IndicatorVo> resultPage = new Page<>(pageParam.getPage(), pageParam.getPageSize());
    resultPage.setRecords(voList);
    resultPage.setTotal(categoryPage.getTotal());
    return resultPage;
  }
}
