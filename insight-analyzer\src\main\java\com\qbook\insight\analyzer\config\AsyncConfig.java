package com.qbook.insight.analyzer.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 异步任务线程池
 *
 * <AUTHOR>
 */
@Configuration
@EnableAsync
public class AsyncConfig {

  /**
   * 线程池：用于处理任务分析
   *
   * @return executor
   */
  @Bean("analysisTaskExecutor")
  public Executor analysisTaskExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    int poolSize = Runtime.getRuntime().availableProcessors() * 2;
    executor.setCorePoolSize(poolSize);
    executor.setMaxPoolSize(poolSize * 2);
    executor.setQueueCapacity(100);
    executor.setThreadNamePrefix("AnalysisTask-");
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    executor.initialize();
    return executor;
  }
}
