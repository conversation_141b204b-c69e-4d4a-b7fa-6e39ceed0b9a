package com.qbook.insight.handler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qbook.insight.common.constant.RMsg;
import com.qbook.insight.common.entity.Client;
import com.qbook.insight.common.entity.Report;
import com.qbook.insight.common.exception.BizException;
import com.qbook.insight.common.mapper.ClientMapper;
import com.qbook.insight.common.vo.ClientReportVO;
import com.qbook.insight.common.vo.ClientVO;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.service.ClientService;
import com.qbook.insight.handler.service.ReportService;
import com.qbook.insight.handler.service.UserService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 客户操作接口实现
 *
 * <AUTHOR>
 */
@Service
public class ClientServiceImpl implements ClientService {

  @Resource private ClientMapper clientMapper;
  @Resource private ReportService reportService;
  @Resource private UserService userService;

  @Override
  public int add(ClientVO clientVO) {
    clientVO.setUserId(userService.getUserId());
    Long corpId = clientVO.getCorpId();
    if (corpId != null) {
      if (corpId <= 0) {
        throw new BizException(RMsg.ERR_PARAM);
      }

      // 判断该客户是否已存在
      LambdaQueryWrapper<Client> wrapper = new LambdaQueryWrapper<>();
      wrapper.eq(Client::getUserId, clientVO.getUserId());
      wrapper.eq(Client::getCorpId, corpId);
      if (clientMapper.exists(wrapper)) {
        throw new BizException("该公司已经存在");
      }
    } else {
      throw new BizException("请输入正确的公司或税号");
    }

    return clientMapper.insert(clientVO);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public int delete(long id) {
    LambdaUpdateWrapper<Client> wrapper = new LambdaUpdateWrapper<>();
    wrapper.eq(Client::getId, id);
    wrapper.eq(Client::getUserId, userService.getUserId());
    int rows = clientMapper.delete(wrapper);
    if (rows == 0) {
      return 0;
    }

    reportService.deleteByClientId(id);
    return rows;
  }

  @Override
  public int update(long id, ClientVO clientVO) {
    LambdaUpdateWrapper<Client> wrapper = new LambdaUpdateWrapper<>();
    wrapper.eq(Client::getId, id);
    wrapper.eq(Client::getUserId, userService.getUserId());
    clientVO.setId(null);
    clientVO.setUserId(null);
    clientVO.setCorpId(null);
    return clientMapper.update(clientVO, wrapper);
  }

  @Override
  public IPage<ClientReportVO> page(
      PageParam pageParam, String corpName, String taxId, String tags) {
    IPage<ClientReportVO> page = new Page<>(pageParam.getPage(), pageParam.getPageSize());
    ClientVO clientVO = new ClientVO();
    clientVO.setUserId(userService.getUserId());
    clientVO.setCorpName(corpName);
    clientVO.setTaxId(taxId);
    clientVO.setTags(tags);
    List<ClientReportVO> clientVos = clientMapper.selectClientVO(page, clientVO);

    if (!clientVos.isEmpty()) {
      Map<Long, ClientReportVO> map = new HashMap<>();
      List<Long> ids = new ArrayList<>();
      for (ClientReportVO client : clientVos) {
        ids.add(client.getId());
        map.put(client.getId(), client);
      }
      List<Report> reports = reportService.selectByClientIds(ids);
      for (Report report : reports) {
        Long clientId = report.getClientId();
        map.get(clientId).getReports().add(report);
      }
    }

    page.setRecords(clientVos);
    return page;
  }
}
