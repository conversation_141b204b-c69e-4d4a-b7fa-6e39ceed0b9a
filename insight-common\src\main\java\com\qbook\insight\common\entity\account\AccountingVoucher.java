package com.qbook.insight.common.entity.account;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/** 记账凭证 */
@Data
public class AccountingVoucher {

  @ApiModelProperty("凭证日期")
  private String voucherDate;

  @ApiModelProperty("凭证种类")
  private String voucherType;

  @ApiModelProperty("凭证编号")
  private String voucherNumber;

  @ApiModelProperty("行号")
  private Integer lineNumber;

  @ApiModelProperty("摘要")
  private String summary;

  @ApiModelProperty("科目编号")
  private String subjectKey;

  @ApiModelProperty("借方金额")
  private BigDecimal debitAmount;

  @ApiModelProperty("贷方金额")
  private BigDecimal creditAmount;

  @ApiModelProperty("币种")
  private String currency;

  @ApiModelProperty("借方外币金额")
  private BigDecimal debitForeignCurrencyAmount;

  @ApiModelProperty("贷方外币金额")
  private BigDecimal creditForeignCurrencyAmount;

  @ApiModelProperty("汇率")
  private BigDecimal exchangeRate;

  @ApiModelProperty("数量")
  private BigDecimal quantity;

  @ApiModelProperty("单价")
  private BigDecimal unitPrice;

  @ApiModelProperty("辅助核算组")
  private String auxiliaryAccountingGroup;

  @ApiModelProperty("结算方式")
  private String settlementMethod;

  @ApiModelProperty("票据类型")
  private String billType;

  @ApiModelProperty("票据号")
  private String billNumber;

  @ApiModelProperty("票据日期")
  private String billDate;

  @ApiModelProperty("附件数")
  private Integer attachmentNumber;

  @ApiModelProperty("制单人员")
  private String creator;

  @ApiModelProperty("审核人员")
  private String reviewer;

  @ApiModelProperty("记账人员")
  private String accountants;

  @ApiModelProperty("出纳人员")
  private String cashier;

  @ApiModelProperty("记账标志")
  private String accountingFlag;
}
