package com.qbook.insight.common.enums;

/**
 * 采集数据类型枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-07-18 下午3:23
 */
public enum CollectDataType {
  // 数据采集类型列表（1:发票采集 2:账套采集 3:申报表采集）
  INVOICE("1", "发票采集"),
  ACCOUNT("2", "账套采集"),
  DECLARATION("3", "申报表采集"),
  UNKNOWN("-1", "未知");

  private final String code;
  private final String name;

  CollectDataType(String code, String name) {
    this.code = code;
    this.name = name;
  }

  public String getCode() {
    return code;
  }

  public String getName() {
    return name;
  }

  public static CollectDataType getByCode(String code) {
    for (CollectDataType value : CollectDataType.values()) {
      if (value.getCode().equals(code)) {
        return value;
      }
    }
    return UNKNOWN;
  }
}
