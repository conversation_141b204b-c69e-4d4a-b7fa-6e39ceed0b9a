package com.qbook.insight.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 实体基类
 *
 * <AUTHOR>
 */
@Data
public class BaseEntity {

  @ApiModelProperty("记录ID")
  @TableId(type = IdType.AUTO)
  private Long id;

  @ApiModelProperty("创建时间")
  private Date createdAt;

  @ApiModelProperty("更新时间")
  private Date updatedAt;
}
