<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
  <properties>
    <property name="FILE_PATH">${sys:LOG_PATH}</property>
    <property name="FILE_NAME">${sys:LOG_FILE}</property>
    <property name="LOG_PATTERN">
      %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %msg [%c{1}:%L]%n
    </property>
  </properties>

  <Appenders>
    <Console name="CONSOLE" target="SYSTEM_OUT">
      <PatternLayout pattern="${LOG_PATTERN}" />
    </Console>

    <RollingFile name="FILE"
      fileName="${FILE_PATH}/${FILE_NAME}.log"
      filePattern="${FILE_PATH}/${FILE_NAME}-%i.log">
      <PatternLayout pattern="${LOG_PATTERN}" />
      <Policies>
        <SizeBasedTriggeringPolicy size="200 MB" />
      </Policies>
      <DefaultRolloverStrategy max="5" />
    </RollingFile>
  </Appenders>

  <Loggers>
    <Root level="INFO">
      <AppenderRef ref="FILE" />
    </Root>
  </Loggers>
</Configuration>
