package com.qbook.insight.handler.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.entity.MemberOrder;
import com.qbook.insight.handler.vo.MemberOrderVO;
import org.springframework.stereotype.Service;

/**
 * 支付订单接口
 *
 * <AUTHOR>
 */
@Service
public interface MemberOrderService {

  /** 创建支付订单 */
  MemberOrderVO add(MemberOrder order);

  /** 根据订单号获取订单详情 */
  MemberOrder getById(long id);

  /** 获取支付订单详情 */
  MemberOrderVO checkByOrderId(String orderId);

  /** 获取订单分页列表(分页) */
  IPage<MemberOrderVO> pageVO(PageParam pageParam, String status, String paymentMethod);
}
