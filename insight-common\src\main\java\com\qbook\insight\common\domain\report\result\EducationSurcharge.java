package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.ResultLevel1;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 教育费附加及地方教育费附加
 *
 * <AUTHOR>
 */
@Data
public class EducationSurcharge {

  @ApiModelProperty("序号")
  private Integer index;

  @ApiModelProperty("年份")
  private Integer year;

  @ApiModelProperty("出口企业免抵增值税额未申报相应税费(元)")
  @ResultLevel1
  private BigDecimal unrecordedExportCredit;

  @ApiModelProperty("按主税种申报应纳税额预估的少缴税费(元)")
  @ResultLevel1
  private BigDecimal estimatedUnderpayment;

  @ApiModelProperty("因查补的隐匿收入少申报税款(元)")
  private BigDecimal unrecordedHiddenIncome;
}
