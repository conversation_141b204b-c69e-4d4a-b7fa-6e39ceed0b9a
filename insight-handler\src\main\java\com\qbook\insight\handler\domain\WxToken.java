package com.qbook.insight.handler.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 微信公众号token接口返回实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WxToken extends WxEntityBase {

  // access_token
  private String accessToken;

  // 刷新token
  private String refreshToken;

  // 过期时间
  private Integer expiresIn;

  // 微信用户标识
  private String openId;

  // 微信用户平台通用标识
  private String unionId;

  // 权限范围
  private String scope;
}
