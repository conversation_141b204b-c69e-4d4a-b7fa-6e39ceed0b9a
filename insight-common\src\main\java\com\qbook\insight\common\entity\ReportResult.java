package com.qbook.insight.common.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 报告结果实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ReportResult extends BaseEntity {

  @ApiModelProperty("用户ID")
  private Long userId;

  @ApiModelProperty("报告ID")
  private Long reportId;

  @ApiModelProperty("结果")
  private String result;
}
