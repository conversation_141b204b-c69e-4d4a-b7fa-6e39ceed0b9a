package com.qbook.insight.handler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qbook.insight.common.exception.BizException;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.entity.MemberOrder;
import com.qbook.insight.handler.entity.MemberPackage;
import com.qbook.insight.handler.mapper.MemberOrderMapper;
import com.qbook.insight.handler.service.MemberOrderService;
import com.qbook.insight.handler.service.MemberPackageService;
import com.qbook.insight.handler.service.MemberService;
import com.qbook.insight.handler.service.UserService;
import com.qbook.insight.handler.vo.MemberOrderVO;
import java.util.Date;
import java.util.UUID;
import javax.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 支付订单接口实现
 *
 * <AUTHOR>
 */
@Service
public class MemberOrderServiceImpl implements MemberOrderService {

  @Resource private MemberOrderMapper memberOrderMapper;
  @Resource private UserService userService;
  @Resource private MemberPackageService memberPackageService;
  @Resource private MemberService memberService;

  @Override
  public MemberOrderVO add(MemberOrder order) {
    if (order.getPlanId() == null) {
      throw new BizException("请选择有效套餐");
    }

    MemberPackage pkg = memberPackageService.getById(order.getPlanId());
    order.setAmount(pkg.getPrice());
    order.setUserId(userService.getUserId());
    order.setCreatedAt(new Date());
    order.setOrderId(generateOrderId());
    order.setStatus("pending");

    // TODO 调用第三方支付接口
    String paymentUrl = "weixin://pay.weixin.qq.com/bizpayurl/up?pr=NwY5Mz9&groupid=00";
    order.setPaymentUrl(paymentUrl);
    order.setPaymentMethod("wechat_pay");

    memberOrderMapper.insert(order);
    return toVO(order);
  }

  @Override
  public MemberOrder getById(long id) {
    LambdaQueryWrapper<MemberOrder> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MemberOrder::getId, id);
    wrapper.eq(MemberOrder::getUserId, userService.getUserId());
    return memberOrderMapper.selectOne(wrapper);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public MemberOrderVO checkByOrderId(String orderId) {
    LambdaQueryWrapper<MemberOrder> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(MemberOrder::getOrderId, orderId);
    MemberOrder order = memberOrderMapper.selectOne(wrapper);

    if (order == null) {
      throw new BizException("订单不存在");
    }

    if ("pending".equals(order.getStatus())) {
      // TODO 主动调用第三方支付订单查询接口
      order.setStatus("paid");
      order.setPaidAt(new Date());

      // 更新订单状态
      memberOrderMapper.updateById(order);

      // 修改用户会员权限
      memberService.handlePaymentSuccess(order.getUserId(), order.getPlanId());
    }
    return toVO(order);
  }

  @Override
  public IPage<MemberOrderVO> pageVO(PageParam pageParam, String status, String paymentMethod) {
    IPage<MemberOrderVO> page = new Page<>(pageParam.getPage(), pageParam.getPageSize());
    Long userId = userService.getUserId();
    return memberOrderMapper.pageVO(page, userId, status, paymentMethod);
  }

  private String generateOrderId() {
    return "ORDER_"
        + System.currentTimeMillis()
        + "_"
        + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
  }

  private MemberOrderVO toVO(MemberOrder entity) {
    if (entity == null) {
      return null;
    }

    MemberOrderVO vo = new MemberOrderVO();
    BeanUtils.copyProperties(entity, vo);

    return vo;
  }
}
