package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 商品分析
 *
 * <AUTHOR>
 */
@Data
public class ProductAnalysis {

  @ApiModelProperty("主要进项商品列表")
  private List<Product> mainPurchaseProducts;

  @ApiModelProperty("主要销项商品列表")
  private List<Product> mainSalesProducts;

  @ApiModelProperty("结论")
  private String conclusion;
}
