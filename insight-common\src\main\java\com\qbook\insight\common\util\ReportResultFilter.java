package com.qbook.insight.common.util;

import cn.hutool.core.map.MapUtil;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 报告结果过滤器
 *
 * <AUTHOR>
 */
public class ReportResultFilter {

  /**
   * 保留对象中字段值不为 null 且标注了指定注解的字段
   *
   * @param obj Java 对象
   * @param annotationCls 注解类型（如 ResultLevel1.class）
   * @return Map<String, Object>
   */
  public static Map<String, Object> process(Object obj, Class<? extends Annotation> annotationCls) {
    Map<String, Object> result = new LinkedHashMap<>();
    if (obj == null) {
      return result;
    }

    // 获取所有字段（包括父类字段）
    List<Field> fields = new ArrayList<>();
    Class<?> currentClass = obj.getClass();
    while (currentClass != null && currentClass != Object.class) {
      Collections.addAll(fields, currentClass.getDeclaredFields());
      currentClass = currentClass.getSuperclass();
    }

    for (Field field : fields) {
      field.setAccessible(true);

      // 判断字段是否标注了指定注解
      if (!field.isAnnotationPresent(annotationCls)) {
        continue;
      }

      // 获取字段值
      Object value;
      try {
        value = field.get(obj);
      } catch (IllegalAccessException e) {
        continue;
      }

      // 忽略 null 值
      if (value == null) {
        continue;
      }

      // 如果是嵌套对象，递归处理
      if (isCustomClass(value.getClass())) {
        Map<String, Object> nestedMap = process(value, annotationCls);
        if (!MapUtil.isEmpty(nestedMap)) {
          result.put(field.getName(), nestedMap);
        }
      } else {
        result.put(field.getName(), value);
      }
    }

    return result;
  }

  /** 判断是否是自定义类（非 JDK 内置类） */
  private static boolean isCustomClass(Class<?> clazz) {
    return !clazz.isPrimitive()
        && !clazz.getName().startsWith("java.")
        && !clazz.getName().startsWith("javax.");
  }
}
