<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qbook.insight.common.mapper.ColumnMapper">

  <sql id="selectColumnSqlId">
    select column_name,
    if((is_nullable = 'no' and column_key !='PRI'), '1', '0') as is_required,
    if(column_key = 'PRI', '1', '0') as is_primary_key,
    ordinal_position as sort,
    column_comment,
    if(extra = 'auto_increment', '1', '0') as is_auto_increment,
    column_type
    from information_schema.columns
    where table_schema = (select database())
  </sql>

  <select id="getColumnsByTableName">
    <include refid="selectColumnSqlId" />
    and table_name=#{tableName} order by ordinal_position
  </select>
</mapper>
