package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 扣除类调整事项(调减项)
 *
 * <AUTHOR>
 */
@Data
public class ExpenseDeductions {

  @ApiModelProperty("研发费用加计扣除金额(元)")
  private Integer rdDeduction;

  @ApiModelProperty("跨期入账的成本费用(元)")
  private BigDecimal crossPeriodCosts;

  @ApiModelProperty("取得单一来源小规模成本发票列表")
  private List<Object> smallScaleInvoices;

  @ApiModelProperty("取得单一来源小规模成本发票，应调整(元)")
  private BigDecimal smallScaleInvoiceAdjustment;
}
