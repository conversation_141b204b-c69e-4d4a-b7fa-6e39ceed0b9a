package com.qbook.insight.analyzer.domain;

import com.qbook.insight.common.entity.InvoicePurchase;
import com.qbook.insight.common.entity.InvoiceSales;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分析数据
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-04 9:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AnalysisData {

  // 进项发票信息
  List<InvoicePurchase> purchaseList;

  // 销项发票信息
  List<InvoiceSales> saleList;
}
