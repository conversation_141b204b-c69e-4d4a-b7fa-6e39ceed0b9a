package com.qbook.insight.handler.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qbook.insight.common.exception.BizException;
import com.qbook.insight.common.util.HttpClient;
import com.qbook.insight.handler.config.WxConfig;
import com.qbook.insight.handler.config.WxPayConfig;
import com.qbook.insight.handler.entity.MemberOrder;
import com.qbook.insight.handler.mapper.MemberOrderMapper;
import com.qbook.insight.handler.service.WxPayService;
import com.qbook.insight.handler.util.WxPayUtils;
import com.qbook.insight.handler.util.WxUtils;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import com.wechat.pay.java.core.util.NonceUtil;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.jsapi.model.*;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.Instant;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

/** <AUTHOR> */
@Service
@Slf4j
public class WxPayServiceImpl implements WxPayService {

  @Resource protected HttpServletResponse response;
  @Resource private MemberOrderMapper memberOrderMapper;
  @Resource private WxUtils wxUtils;
  @Resource private WxPayUtils wxPayUtils;
  @Resource private WxConfig wxConfig;
  @Resource private WxPayConfig wxPayConfig;

  @Override
  public void redirectWxPay(String orderId) {
    try {
      if (orderId == null || orderId.isEmpty()) {
        throw new RuntimeException("订单id不能为空");
      }
      // TODO: 真实域名
      String callBackUrl = String.format("http://%s/api/wxPay/oauth2Callback", "");
      String redirectUrl =
          wxUtils.getOauth2Url(
              // TODO: 微信正式appid
              "wxf190178e0974411a", URLEncoder.encode(callBackUrl, "UTF-8"), orderId);
      log.warn("redirectUrl地址为: {}", redirectUrl);
      response.sendRedirect(redirectUrl);
    } catch (IOException e) {
      throw new RuntimeException("跳转微信页面失败：" + e.getMessage(), e);
    }
  }

  @Override
  public String showPayPage(HttpServletRequest request, Model model) {
    try {
      log.warn("---进入回调---");
      log.info("code为：{}", request.getParameter("code"));
      log.info("state为：{}", request.getParameter("state"));
      String code = request.getParameter("code");
      String orderId = request.getParameter("state");
      if (code == null || code.isEmpty()) {
        throw new BizException("code为空");
      }
      // 1、获取携带微信openId订单信息
      MemberOrder memberInfo = getPayOpenIdInfo(code, orderId);
      // 2、获取预下单Id
      String prepayId = wxPreOrder(orderId, memberInfo);
      // 3、获取微信支付参数
      PrepayWithRequestPaymentResponse paymentResponse = getPayRequest(prepayId);
      // 4、返回参数到页面模板
      model.addAttribute("appId", wxConfig.getAppId());
      model.addAttribute("timeStamp", paymentResponse.getTimeStamp());
      model.addAttribute("nonceStr", paymentResponse.getNonceStr());
      model.addAttribute("package", paymentResponse.getPackageVal());
      model.addAttribute("signType", "RSA");
      model.addAttribute("paySign", paymentResponse.getPaySign());

      // Map<String, String> stringStringMap = wxPayUtils.buildPayMap(wxConfig.getAppId(),
      // prepayId);
      // // 3、返回参数到页面模板
      // model.addAttribute("appId", wxConfig.getAppId());
      // model.addAttribute("timeStamp", stringStringMap.get("timeStamp"));
      // model.addAttribute("nonce", stringStringMap.get("nonceStr"));
      // model.addAttribute("package", String.format("prepay_id=%s", prepayId));
      // model.addAttribute("paySign", stringStringMap.get("paySign"));
      // 欢迎语
      model.addAttribute("message", "正在准备支付...");
      return "pay";
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  public String wxGetNotify(HttpServletRequest request) {
    // 1、 获取通知参数
    // 2、 验签
    // 3、 获取通知参数
    // 4、 获取订单信息

    // 更新订单，发送通知，记录日志...

    return "success";
  }

  /**
   * 获取携带openId的订单信息
   *
   * @return openId
   */
  private MemberOrder getPayOpenIdInfo(String code, String orderId) {
    MemberOrder memberOrder =
        memberOrderMapper.selectOne(
            new LambdaQueryWrapper<MemberOrder>().eq(MemberOrder::getOrderId, orderId));
    if (Objects.isNull(memberOrder)) {
      throw new BizException("订单不存在");
    }
    String payOpenid = memberOrder.getWxPayOpenid();
    if (payOpenid != null && !payOpenid.isEmpty()) {
      return memberOrder;
    }
    try {
      String url = wxUtils.getOpenIdUrl(code);
      HttpClient.Response response = HttpClient.get(url, null, 5000);
      log.info("GetOpenidUrl response: {}", response.getData());
      JSONObject res = JSONUtil.parseObj(response.getData());
      payOpenid = res.getStr("openid");
      memberOrder.setWxPayOpenid(payOpenid);
      memberOrderMapper.updateById(memberOrder);
      return memberOrder;
    } catch (Exception e) {
      throw new RuntimeException("获取openId失败：" + e.getMessage(), e);
    }
  }

  /**
   * 获取微信支付服务
   *
   * @return JsapiService对象
   */
  private JsapiService getJsapiService() {
    // 使用微信支付公钥的RSA配置
    Config config =
        new RSAPublicKeyConfig.Builder()
            .merchantId(wxPayConfig.getMchId())
            .privateKeyFromPath(wxPayConfig.getMchPriKeyPath())
            .publicKeyFromPath(wxPayConfig.getPublicKeyPath())
            .publicKeyId(wxPayConfig.getPublicKeyId())
            .merchantSerialNumber(wxPayConfig.getMchSerialNo())
            .apiV3Key(wxPayConfig.getApiV3Key())
            .build();
    return new JsapiService.Builder().config(config).build();
  }

  /** 调起微信预下单 */
  private String wxPreOrder(String orderId, MemberOrder memberInfo) {
    JsapiService jsapiPay = getJsapiService();
    PrepayRequest request = getPrepayRequest(orderId, memberInfo);
    // 调用下单方法，得到prepayId
    PrepayResponse response = jsapiPay.prepay(request);
    String prepayId = response.getPrepayId();
    log.warn("prepayId: {}", prepayId);
    return prepayId;
  }

  /**
   * 获取预下单参数
   *
   * @param orderId 订单id
   * @param memberInfo 订单信息
   * @return 预下单入参
   */
  private PrepayRequest getPrepayRequest(String orderId, MemberOrder memberInfo) {
    PrepayRequest request = new PrepayRequest();
    Amount amount = new Amount();
    amount.setTotal(memberInfo.getAmount().multiply(new BigDecimal(100)).intValue());
    request.setAmount(amount);
    request.setAppid(wxConfig.getAppId());
    request.setMchid(wxPayConfig.getMchId());
    request.setDescription("测试商品标题");
    request.setOutTradeNo(orderId);
    request.setNotifyUrl(wxPayConfig.getNotifyUrl());
    Payer payer = new Payer();
    payer.setOpenid(memberInfo.getWxPayOpenid());
    request.setPayer(payer);
    return request;
  }

  /**
   * 获取调起支付参数
   *
   * @param prepayId 预下单Id
   * @return 调起支付入参
   */
  private PrepayWithRequestPaymentResponse getPayRequest(String prepayId) {
    long timestamp = Instant.now().getEpochSecond();
    String nonceStr = NonceUtil.createNonce(32);
    String packageVal = "prepay_id=" + prepayId;
    String message =
        wxConfig.getAppId() + "\n" + timestamp + "\n" + nonceStr + "\n" + packageVal + "\n";
    log.warn("Message for RequestPayment signatures is[{}]", message);
    // String sign = signer.sign(message).getSign();
    PrepayWithRequestPaymentResponse response = new PrepayWithRequestPaymentResponse();
    response.setAppId(wxConfig.getAppId());
    response.setTimeStamp(String.valueOf(timestamp));
    response.setNonceStr(nonceStr);
    response.setPackageVal(packageVal);
    response.setSignType("RSA");
    // response.setPaySign(sign);
    return response;
  }
}
