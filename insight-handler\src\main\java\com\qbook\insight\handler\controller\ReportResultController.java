package com.qbook.insight.handler.controller;

import com.qbook.insight.common.domain.R;
import com.qbook.insight.common.domain.report.result.Result;
import com.qbook.insight.handler.service.ReportResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 报告结果相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "ReportResult Operate", description = "报告结果操作")
@RestController
@RequestMapping("/report_result")
public class ReportResultController {

  @Resource private ReportResultService reportResultService;

  @ApiOperation(value = "修改报告结果")
  @PutMapping("/{reportId}")
  @PreAuthorize("hasRole(@role.AUDITOR)")
  public R update(@PathVariable("reportId") Long reportId, @RequestBody Result result) {
    return R.ok(reportResultService.update(reportId, result));
  }

  @ApiOperation(value = "获取报告结果")
  @GetMapping("/{reportId}")
  public R getReportResult(
      @ApiParam("公司名称") @PathVariable("reportId") Long reportId,
      @ApiParam("报告层级") @RequestParam(required = false, defaultValue = "1") int level) {
    return R.ok(reportResultService.getReportResult(reportId, level));
  }
}
