package com.qbook.insight.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 事件日志实体类
 *
 * <AUTHOR>
 */
@Data
public class EventLog {

  @ApiModelProperty("记录ID")
  @TableId(type = IdType.AUTO)
  private Long id;

  @ApiModelProperty("用户名")
  private Long userId;

  @ApiModelProperty("事件名称")
  private String event;

  @ApiModelProperty("操作结果（0:成功 1:失败）")
  private Integer result;

  @ApiModelProperty("IP地址")
  private String ip;

  @ApiModelProperty("操作地点")
  private String location;

  @ApiModelProperty("操作时间")
  private Date eventTime;

  @ApiModelProperty("操作耗时（毫秒）")
  private Long costTime;
}
