package com.qbook.insight.handler;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * 程序入口
 *
 * <AUTHOR>
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.qbook.insight"})
@MapperScan(basePackages = {"com.qbook.insight.**.mapper"})
public class Application {
  public static void main(String[] args) {
    SpringApplication.run(Application.class, args);
  }
}
