package com.qbook.insight.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 组件配置
 *
 * <AUTHOR>
 */
@Configuration
@Data
public class ComponentConfig {

  // Redis组件
  @Value("${component.redis:false}")
  private boolean redis;

  // DB组件
  @Value("${component.db:false}")
  private boolean db;

  // 分页组件
  @Value("${component.sql-page:false}")
  private boolean sqlPage;

  // JWT组件
  @Value("${component.jwt:false}")
  private boolean jwt;

  // CORS组件
  @Value("${component.cors:false}")
  private boolean cors;

  // 操作日志记录处理组件
  @Value("${component.eventlog-aspect:false}")
  private boolean eventlog;
}
