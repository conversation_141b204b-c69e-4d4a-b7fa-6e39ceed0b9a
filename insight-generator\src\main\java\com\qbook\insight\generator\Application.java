package com.qbook.insight.generator;

import com.qbook.insight.common.core.Pipe;
import com.qbook.insight.generator.service.TaskListener;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;

/**
 * 程序入口
 *
 * <AUTHOR>
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.qbook.insight"})
@MapperScan(basePackages = {"com.qbook.insight.**.mapper"})
@Slf4j
public class Application implements CommandLineRunner {

  @Resource private TaskListener taskListener;

  public static void main(String[] args) {
    SpringApplication.run(Application.class, args);
  }

  @Override
  public void run(String... args) {
    taskListener.start();
  }

  @EventListener
  public void handleContextClose(ContextClosedEvent ignored) {
    Pipe.exit = true;
    log.info("Application stopped");
  }
}
