package com.qbook.insight.common.util;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * JSON更新辅助类 用于收集JSON更新操作，配合Mapper.xml使用 对于list结构数据: 修改时请给实体类增加 index 仅用于索引定位(不会存储到数据库中),追加时无需赋值
 *
 * <AUTHOR>
 */
@Data
public class JsonUpdateMapperHelper {

  private final List<JsonSetOperation> jsonSetOperations = new ArrayList<>();
  private final List<JsonAppendOperation> jsonAppendOperations = new ArrayList<>();

  @Data
  @AllArgsConstructor
  public static class JsonSetOperation {
    private String path;
    private String value;
    private boolean isJson;
  }

  @Data
  @AllArgsConstructor
  public static class JsonAppendOperation {
    private String path;
    private String value;
  }

  /**
   * 构建json字段更新操作
   *
   * @param entity json字段实体映射
   * @param jsonColumn json字段列名
   * @return 更新路径及对于值
   */
  public static Map<String, Object> buildJsonUpdate(Object entity, String jsonColumn) {
    JsonUpdateMapperHelper helper = new JsonUpdateMapperHelper();
    helper.processObject("", entity);
    return helper.toParamMap(jsonColumn);
  }

  private void processObject(String parentPath, Object obj) {
    if (obj == null) return;

    Field[] fields = ReflectUtil.getFields(obj.getClass());
    for (Field field : fields) {
      Object value = ReflectUtil.getFieldValue(obj, field);
      if (value != null) {
        String fieldName = field.getName();
        String currentPath = parentPath.isEmpty() ? "$." + fieldName : parentPath + "." + fieldName;

        if (isBasicType(value.getClass())) {
          jsonSetOperations.add(new JsonSetOperation(currentPath, value.toString(), false));
        } else if (value instanceof List) {
          processList((List<?>) value, currentPath);
        } else {
          processObject(currentPath, value);
        }
      }
    }
  }

  private void processList(List<?> list, String currentPath) {
    for (Object item : list) {
      if (item == null) continue;

      Object index = ReflectUtil.getFieldValue(item, "index");
      JSONObject jsonObj = JSONUtil.parseObj(item);
      jsonObj.remove("index");
      String jsonStr = jsonObj.toString();

      if (index instanceof Number) {
        String listPath = String.format("%s[%d]", currentPath, ((Number) index).intValue());
        jsonSetOperations.add(new JsonSetOperation(listPath, jsonStr, true));
      } else {
        jsonAppendOperations.add(new JsonAppendOperation(currentPath, jsonStr));
      }
    }
  }

  private static boolean isBasicType(Class<?> clazz) {
    return clazz.isPrimitive()
        || String.class.equals(clazz)
        || Number.class.isAssignableFrom(clazz)
        || Boolean.class.equals(clazz);
  }

  public Map<String, Object> toParamMap(String jsonColumn) {
    Map<String, Object> params = new HashMap<>();
    params.put("jsonColumn", jsonColumn);
    params.put("setOperations", jsonSetOperations);
    params.put("appendOperations", jsonAppendOperations);
    return params;
  }
}
