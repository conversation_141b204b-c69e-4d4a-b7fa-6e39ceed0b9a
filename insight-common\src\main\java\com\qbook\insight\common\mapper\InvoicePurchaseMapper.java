package com.qbook.insight.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qbook.insight.common.constant.DsName;
import com.qbook.insight.common.entity.InvoicePurchase;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 进项Mapper
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-04 15:11
 */
@Mapper
@DS(DsName.DATA)
public interface InvoicePurchaseMapper extends BaseMapper<InvoicePurchase> {

  /** 批量查询已存在的zzfpdm+zzfphm */
  List<String> selectExistingInvoiceKeys(@Param("invoiceKeys") List<String> invoiceKeys);

  /** 批量插入进项数据 */
  int insertBatch(List<InvoicePurchase> list);

  /**
   * 获取指定税号指定时间范围内的发票gatherDatetime
   *
   * @param taxId 税号
   * @return gatherDatetimeList
   */
  List<LocalDateTime> getKprqsByTaxIdAndDateRange(
      @Param("taxId") String taxId,
      @Param("startDate") String startDate,
      @Param("endDate") String endDate);

  /**
   * 检测是否存在范围内的发票信息
   *
   * @param taxId 税号
   * @param startDate 开始时间
   * @param endDate 结束时间
   * @return 是否存在
   */
  boolean existsByTaxIdAndDateRange(
      @Param("taxId") String taxId,
      @Param("startDate") String startDate,
      @Param("endDate") String endDate);

  /** 获取指定税号发票gatherDatetime的最小值和最大值 */
  Map<String, LocalDateTime> getMinMaxKprqDateByTaxId(String taxId);
}
