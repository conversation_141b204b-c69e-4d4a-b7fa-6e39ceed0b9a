package com.qbook.insight.common.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class IndicatorConfig extends BaseEntity {

  @ApiModelProperty("指标一级分类编号")
  private Long class1;

  @ApiModelProperty("指标二级分类编号")
  private Long class2;

  @ApiModelProperty("指标编码")
  private String indicatorCode;

  @ApiModelProperty("指标名称")
  private String indicatorName;

  @ApiModelProperty("执行sql")
  private String executeSql;

  @ApiModelProperty("指标数据类型")
  private Integer returnType;

  @ApiModelProperty("指标描述")
  private String description;

  @ApiModelProperty("状态(1-启用,0-禁用)")
  private Integer status;
}
